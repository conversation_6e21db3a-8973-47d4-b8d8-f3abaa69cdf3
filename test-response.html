<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试响应格式</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409eff;
            text-align: center;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        .result {
            margin-top: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #409eff;
        }
        .success {
            border-left-color: #67c23a;
            background-color: #f0f9ff;
        }
        .error {
            border-left-color: #f56c6c;
            background-color: #fef0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 测试Go后台响应格式</h1>
        
        <p>测试修复后的JSON响应格式是否符合扫描控件期望</p>
        
        <button onclick="testHealthCheck()">测试健康检查</button>
        <button onclick="testPdfUpload()">模拟PDF上传</button>
        <button onclick="testTiffUpload()">模拟TIFF上传</button>
        <button onclick="testJpgUpload()">模拟JPG上传</button>
        
        <div id="result" class="result">点击按钮开始测试...</div>
    </div>

    <script>
        // 测试健康检查
        async function testHealthCheck() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在测试健康检查...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch('http://localhost:8080/health');
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 健康检查成功\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 健康检查失败\n错误: ${error.message}`;
            }
        }

        // 模拟PDF上传测试
        async function testPdfUpload() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在模拟PDF上传测试...';
            resultDiv.className = 'result';
            
            // 创建一个简单的测试数据
            const testData = {
                id: 'test_pdf_' + Date.now(),
                desc: '测试PDF上传',
                imageData: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            };
            
            try {
                const response = await fetch('http://localhost:8080/upload', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ PDF上传测试成功\n\n新的响应格式:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ PDF上传测试失败\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ PDF上传测试失败\n错误: ${error.message}`;
            }
        }

        // 模拟TIFF上传测试
        async function testTiffUpload() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在模拟TIFF上传测试...';
            resultDiv.className = 'result';
            
            const testData = {
                id: 'test_tiff_' + Date.now(),
                desc: '测试TIFF上传',
                imageData: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            };
            
            try {
                const response = await fetch('http://localhost:8080/upload/tiff', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ TIFF上传测试成功\n\n新的响应格式:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ TIFF上传测试失败\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ TIFF上传测试失败\n错误: ${error.message}`;
            }
        }

        // 模拟JPG上传测试
        async function testJpgUpload() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在模拟JPG上传测试...';
            resultDiv.className = 'result';
            
            const testData = {
                id: 'test_jpg_' + Date.now(),
                desc: '测试JPG上传',
                index: 0,
                imageData: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
            };
            
            try {
                const response = await fetch('http://localhost:8080/upload/jpg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ JPG上传测试成功\n\n新的响应格式:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ JPG上传测试失败\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ JPG上传测试失败\n错误: ${error.message}`;
            }
        }

        // 页面加载时自动测试健康检查
        window.onload = function() {
            setTimeout(testHealthCheck, 500);
        };
    </script>
</body>
</html>

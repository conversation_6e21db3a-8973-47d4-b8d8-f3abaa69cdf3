{"ast": null, "code": "/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor;\n\n/**\n * The base implementation of `_.repeat` which doesn't coerce arguments.\n *\n * @private\n * @param {string} string The string to repeat.\n * @param {number} n The number of times to repeat the string.\n * @returns {string} Returns the repeated string.\n */\nfunction baseRepeat(string, n) {\n  var result = '';\n  if (!string || n < 1 || n > MAX_SAFE_INTEGER) {\n    return result;\n  }\n  // Leverage the exponentiation by squaring algorithm for a faster repeat.\n  // See https://en.wikipedia.org/wiki/Exponentiation_by_squaring for more details.\n  do {\n    if (n % 2) {\n      result += string;\n    }\n    n = nativeFloor(n / 2);\n    if (n) {\n      string += string;\n    }\n  } while (n);\n  return result;\n}\nexport default baseRepeat;", "map": {"version": 3, "names": ["MAX_SAFE_INTEGER", "nativeFloor", "Math", "floor", "baseRepeat", "string", "n", "result"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_baseRepeat.js"], "sourcesContent": ["/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeFloor = Math.floor;\n\n/**\n * The base implementation of `_.repeat` which doesn't coerce arguments.\n *\n * @private\n * @param {string} string The string to repeat.\n * @param {number} n The number of times to repeat the string.\n * @returns {string} Returns the repeated string.\n */\nfunction baseRepeat(string, n) {\n  var result = '';\n  if (!string || n < 1 || n > MAX_SAFE_INTEGER) {\n    return result;\n  }\n  // Leverage the exponentiation by squaring algorithm for a faster repeat.\n  // See https://en.wikipedia.org/wiki/Exponentiation_by_squaring for more details.\n  do {\n    if (n % 2) {\n      result += string;\n    }\n    n = nativeFloor(n / 2);\n    if (n) {\n      string += string;\n    }\n  } while (n);\n\n  return result;\n}\n\nexport default baseRepeat;\n"], "mappings": "AAAA;AACA,IAAIA,gBAAgB,GAAG,gBAAgB;;AAEvC;AACA,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,CAAC,EAAE;EAC7B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAI,CAACF,MAAM,IAAIC,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGN,gBAAgB,EAAE;IAC5C,OAAOO,MAAM;EACf;EACA;EACA;EACA,GAAG;IACD,IAAID,CAAC,GAAG,CAAC,EAAE;MACTC,MAAM,IAAIF,MAAM;IAClB;IACAC,CAAC,GAAGL,WAAW,CAACK,CAAC,GAAG,CAAC,CAAC;IACtB,IAAIA,CAAC,EAAE;MACLD,MAAM,IAAIA,MAAM;IAClB;EACF,CAAC,QAAQC,CAAC;EAEV,OAAOC,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import baseProperty from './_baseProperty.js';\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\nexport default asciiSize;", "map": {"version": 3, "names": ["baseProperty", "asciiSize"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_asciiSize.js"], "sourcesContent": ["import baseProperty from './_baseProperty.js';\n\n/**\n * Gets the size of an ASCII `string`.\n *\n * @private\n * @param {string} string The string inspect.\n * @returns {number} Returns the string size.\n */\nvar asciiSize = baseProperty('length');\n\nexport default asciiSize;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGD,YAAY,CAAC,QAAQ,CAAC;AAEtC,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color === 'string') {\n    color = stringInputToObject(color);\n  }\n  if (typeof color === 'object') {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = 'hsv';\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = 'hsl';\n    }\n    if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n  CSS_UNIT: new RegExp(CSS_UNIT),\n  rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n  rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n  hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n  hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n  hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n  hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n  color = color.trim().toLowerCase();\n  if (color.length === 0) {\n    return false;\n  }\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color === 'transparent') {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: 'name'\n    };\n  }\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match = matchers.rgb.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  match = matchers.rgba.exec(color);\n  if (match) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsl.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  match = matchers.hsla.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hsv.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  match = matchers.hsva.exec(color);\n  if (match) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  match = matchers.hex8.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex6.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  match = matchers.hex4.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      a: convertHexToDecimal(match[4] + match[4]),\n      format: named ? 'name' : 'hex8'\n    };\n  }\n  match = matchers.hex3.exec(color);\n  if (match) {\n    return {\n      r: parseIntFromHex(match[1] + match[1]),\n      g: parseIntFromHex(match[2] + match[2]),\n      b: parseIntFromHex(match[3] + match[3]),\n      format: named ? 'name' : 'hex'\n    };\n  }\n  return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n  return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}", "map": {"version": 3, "names": ["convertHexToDecimal", "hslToRgb", "hsvToRgb", "parseIntFromHex", "rgbToRgb", "names", "boundAlpha", "convertToPercentage", "inputToRGB", "color", "rgb", "r", "g", "b", "a", "s", "v", "l", "ok", "format", "stringInputToObject", "isValidCSSUnit", "String", "substr", "h", "Object", "prototype", "hasOwnProperty", "call", "Math", "min", "max", "CSS_INTEGER", "CSS_NUMBER", "CSS_UNIT", "concat", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "matchers", "RegExp", "rgba", "hsl", "hsla", "hsv", "hsva", "hex3", "hex6", "hex4", "hex8", "trim", "toLowerCase", "length", "named", "match", "exec", "Boolean"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/@ctrl/tinycolor/dist/module/format-input.js"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-redundant-type-constituents */\nimport { convertHexToDecimal, hslToRgb, hsvToRgb, parseIntFromHex, rgbToRgb, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { boundAlpha, convertToPercentage } from './util.js';\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */\nexport function inputToRGB(color) {\n    var rgb = { r: 0, g: 0, b: 0 };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === 'string') {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === 'object') {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = rgbToRgb(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === '%' ? 'prgb' : 'rgb';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = convertToPercentage(color.s);\n            v = convertToPercentage(color.v);\n            rgb = hsvToRgb(color.h, s, v);\n            ok = true;\n            format = 'hsv';\n        }\n        else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = convertToPercentage(color.s);\n            l = convertToPercentage(color.l);\n            rgb = hslToRgb(color.h, s, l);\n            ok = true;\n            format = 'hsl';\n        }\n        if (Object.prototype.hasOwnProperty.call(color, 'a')) {\n            a = color.a;\n        }\n    }\n    a = boundAlpha(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a,\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp('rgb' + PERMISSIVE_MATCH3),\n    rgba: new RegExp('rgba' + PERMISSIVE_MATCH4),\n    hsl: new RegExp('hsl' + PERMISSIVE_MATCH3),\n    hsla: new RegExp('hsla' + PERMISSIVE_MATCH4),\n    hsv: new RegExp('hsv' + PERMISSIVE_MATCH3),\n    hsva: new RegExp('hsva' + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */\nexport function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (names[color]) {\n        color = names[color];\n        named = true;\n    }\n    else if (color === 'transparent') {\n        return { r: 0, g: 0, b: 0, a: 0, format: 'name' };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3] };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return { r: match[1], g: match[2], b: match[3], a: match[4] };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3] };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], l: match[3], a: match[4] };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3] };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return { h: match[1], s: match[2], v: match[3], a: match[4] };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            a: convertHexToDecimal(match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1]),\n            g: parseIntFromHex(match[2]),\n            b: parseIntFromHex(match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            a: convertHexToDecimal(match[4] + match[4]),\n            format: named ? 'name' : 'hex8',\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: parseIntFromHex(match[1] + match[1]),\n            g: parseIntFromHex(match[2] + match[2]),\n            b: parseIntFromHex(match[3] + match[3]),\n            format: named ? 'name' : 'hex',\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */\nexport function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n"], "mappings": "AAAA;AACA,SAASA,mBAAmB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,QAAQ,QAAS,iBAAiB;AACrG,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,UAAU,EAAEC,mBAAmB,QAAQ,WAAW;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAC9B,IAAIC,GAAG,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC9B,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIC,EAAE,GAAG,KAAK;EACd,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;IAC3BA,KAAK,GAAGW,mBAAmB,CAACX,KAAK,CAAC;EACtC;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAIY,cAAc,CAACZ,KAAK,CAACE,CAAC,CAAC,IAAIU,cAAc,CAACZ,KAAK,CAACG,CAAC,CAAC,IAAIS,cAAc,CAACZ,KAAK,CAACI,CAAC,CAAC,EAAE;MAC/EH,GAAG,GAAGN,QAAQ,CAACK,KAAK,CAACE,CAAC,EAAEF,KAAK,CAACG,CAAC,EAAEH,KAAK,CAACI,CAAC,CAAC;MACzCK,EAAE,GAAG,IAAI;MACTC,MAAM,GAAGG,MAAM,CAACb,KAAK,CAACE,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK;IAChE,CAAC,MACI,IAAIF,cAAc,CAACZ,KAAK,CAACe,CAAC,CAAC,IAAIH,cAAc,CAACZ,KAAK,CAACM,CAAC,CAAC,IAAIM,cAAc,CAACZ,KAAK,CAACO,CAAC,CAAC,EAAE;MACpFD,CAAC,GAAGR,mBAAmB,CAACE,KAAK,CAACM,CAAC,CAAC;MAChCC,CAAC,GAAGT,mBAAmB,CAACE,KAAK,CAACO,CAAC,CAAC;MAChCN,GAAG,GAAGR,QAAQ,CAACO,KAAK,CAACe,CAAC,EAAET,CAAC,EAAEC,CAAC,CAAC;MAC7BE,EAAE,GAAG,IAAI;MACTC,MAAM,GAAG,KAAK;IAClB,CAAC,MACI,IAAIE,cAAc,CAACZ,KAAK,CAACe,CAAC,CAAC,IAAIH,cAAc,CAACZ,KAAK,CAACM,CAAC,CAAC,IAAIM,cAAc,CAACZ,KAAK,CAACQ,CAAC,CAAC,EAAE;MACpFF,CAAC,GAAGR,mBAAmB,CAACE,KAAK,CAACM,CAAC,CAAC;MAChCE,CAAC,GAAGV,mBAAmB,CAACE,KAAK,CAACQ,CAAC,CAAC;MAChCP,GAAG,GAAGT,QAAQ,CAACQ,KAAK,CAACe,CAAC,EAAET,CAAC,EAAEE,CAAC,CAAC;MAC7BC,EAAE,GAAG,IAAI;MACTC,MAAM,GAAG,KAAK;IAClB;IACA,IAAIM,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACnB,KAAK,EAAE,GAAG,CAAC,EAAE;MAClDK,CAAC,GAAGL,KAAK,CAACK,CAAC;IACf;EACJ;EACAA,CAAC,GAAGR,UAAU,CAACQ,CAAC,CAAC;EACjB,OAAO;IACHI,EAAE,EAAEA,EAAE;IACNC,MAAM,EAAEV,KAAK,CAACU,MAAM,IAAIA,MAAM;IAC9BR,CAAC,EAAEkB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAACrB,GAAG,CAACC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCC,CAAC,EAAEiB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAACrB,GAAG,CAACE,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCC,CAAC,EAAEgB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAACrB,GAAG,CAACG,CAAC,EAAE,CAAC,CAAC,CAAC;IACpCC,CAAC,EAAEA;EACP,CAAC;AACL;AACA;AACA,IAAIkB,WAAW,GAAG,eAAe;AACjC;AACA,IAAIC,UAAU,GAAG,sBAAsB;AACvC;AACA,IAAIC,QAAQ,GAAG,KAAK,CAACC,MAAM,CAACF,UAAU,EAAE,OAAO,CAAC,CAACE,MAAM,CAACH,WAAW,EAAE,GAAG,CAAC;AACzE;AACA;AACA;AACA,IAAII,iBAAiB,GAAG,aAAa,CAACD,MAAM,CAACD,QAAQ,EAAE,YAAY,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAE,YAAY,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAE,WAAW,CAAC;AACjI,IAAIG,iBAAiB,GAAG,aAAa,CAACF,MAAM,CAACD,QAAQ,EAAE,YAAY,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAE,YAAY,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAE,YAAY,CAAC,CAACC,MAAM,CAACD,QAAQ,EAAE,WAAW,CAAC;AAChK,IAAII,QAAQ,GAAG;EACXJ,QAAQ,EAAE,IAAIK,MAAM,CAACL,QAAQ,CAAC;EAC9BxB,GAAG,EAAE,IAAI6B,MAAM,CAAC,KAAK,GAAGH,iBAAiB,CAAC;EAC1CI,IAAI,EAAE,IAAID,MAAM,CAAC,MAAM,GAAGF,iBAAiB,CAAC;EAC5CI,GAAG,EAAE,IAAIF,MAAM,CAAC,KAAK,GAAGH,iBAAiB,CAAC;EAC1CM,IAAI,EAAE,IAAIH,MAAM,CAAC,MAAM,GAAGF,iBAAiB,CAAC;EAC5CM,GAAG,EAAE,IAAIJ,MAAM,CAAC,KAAK,GAAGH,iBAAiB,CAAC;EAC1CQ,IAAI,EAAE,IAAIL,MAAM,CAAC,MAAM,GAAGF,iBAAiB,CAAC;EAC5CQ,IAAI,EAAE,sDAAsD;EAC5DC,IAAI,EAAE,sDAAsD;EAC5DC,IAAI,EAAE,sEAAsE;EAC5EC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,SAAS5B,mBAAmBA,CAACX,KAAK,EAAE;EACvCA,KAAK,GAAGA,KAAK,CAACwC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAClC,IAAIzC,KAAK,CAAC0C,MAAM,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EAChB;EACA,IAAIC,KAAK,GAAG,KAAK;EACjB,IAAI/C,KAAK,CAACI,KAAK,CAAC,EAAE;IACdA,KAAK,GAAGJ,KAAK,CAACI,KAAK,CAAC;IACpB2C,KAAK,GAAG,IAAI;EAChB,CAAC,MACI,IAAI3C,KAAK,KAAK,aAAa,EAAE;IAC9B,OAAO;MAAEE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEK,MAAM,EAAE;IAAO,CAAC;EACrD;EACA;EACA;EACA;EACA;EACA,IAAIkC,KAAK,GAAGf,QAAQ,CAAC5B,GAAG,CAAC4C,IAAI,CAAC7C,KAAK,CAAC;EACpC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE1C,CAAC,EAAE0C,KAAK,CAAC,CAAC,CAAC;MAAEzC,CAAC,EAAEyC,KAAK,CAAC,CAAC,CAAC;MAAExC,CAAC,EAAEwC,KAAK,CAAC,CAAC;IAAE,CAAC;EACpD;EACAA,KAAK,GAAGf,QAAQ,CAACE,IAAI,CAACc,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE1C,CAAC,EAAE0C,KAAK,CAAC,CAAC,CAAC;MAAEzC,CAAC,EAAEyC,KAAK,CAAC,CAAC,CAAC;MAAExC,CAAC,EAAEwC,KAAK,CAAC,CAAC,CAAC;MAAEvC,CAAC,EAAEuC,KAAK,CAAC,CAAC;IAAE,CAAC;EACjE;EACAA,KAAK,GAAGf,QAAQ,CAACG,GAAG,CAACa,IAAI,CAAC7C,KAAK,CAAC;EAChC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE7B,CAAC,EAAE6B,KAAK,CAAC,CAAC,CAAC;MAAEtC,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC;MAAEpC,CAAC,EAAEoC,KAAK,CAAC,CAAC;IAAE,CAAC;EACpD;EACAA,KAAK,GAAGf,QAAQ,CAACI,IAAI,CAACY,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE7B,CAAC,EAAE6B,KAAK,CAAC,CAAC,CAAC;MAAEtC,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC;MAAEpC,CAAC,EAAEoC,KAAK,CAAC,CAAC,CAAC;MAAEvC,CAAC,EAAEuC,KAAK,CAAC,CAAC;IAAE,CAAC;EACjE;EACAA,KAAK,GAAGf,QAAQ,CAACK,GAAG,CAACW,IAAI,CAAC7C,KAAK,CAAC;EAChC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE7B,CAAC,EAAE6B,KAAK,CAAC,CAAC,CAAC;MAAEtC,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC;MAAErC,CAAC,EAAEqC,KAAK,CAAC,CAAC;IAAE,CAAC;EACpD;EACAA,KAAK,GAAGf,QAAQ,CAACM,IAAI,CAACU,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MAAE7B,CAAC,EAAE6B,KAAK,CAAC,CAAC,CAAC;MAAEtC,CAAC,EAAEsC,KAAK,CAAC,CAAC,CAAC;MAAErC,CAAC,EAAEqC,KAAK,CAAC,CAAC,CAAC;MAAEvC,CAAC,EAAEuC,KAAK,CAAC,CAAC;IAAE,CAAC;EACjE;EACAA,KAAK,GAAGf,QAAQ,CAACU,IAAI,CAACM,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MACH1C,CAAC,EAAER,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BzC,CAAC,EAAET,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BxC,CAAC,EAAEV,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BvC,CAAC,EAAEd,mBAAmB,CAACqD,KAAK,CAAC,CAAC,CAAC,CAAC;MAChClC,MAAM,EAAEiC,KAAK,GAAG,MAAM,GAAG;IAC7B,CAAC;EACL;EACAC,KAAK,GAAGf,QAAQ,CAACQ,IAAI,CAACQ,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MACH1C,CAAC,EAAER,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BzC,CAAC,EAAET,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BxC,CAAC,EAAEV,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5BlC,MAAM,EAAEiC,KAAK,GAAG,MAAM,GAAG;IAC7B,CAAC;EACL;EACAC,KAAK,GAAGf,QAAQ,CAACS,IAAI,CAACO,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MACH1C,CAAC,EAAER,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCzC,CAAC,EAAET,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCxC,CAAC,EAAEV,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCvC,CAAC,EAAEd,mBAAmB,CAACqD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3ClC,MAAM,EAAEiC,KAAK,GAAG,MAAM,GAAG;IAC7B,CAAC;EACL;EACAC,KAAK,GAAGf,QAAQ,CAACO,IAAI,CAACS,IAAI,CAAC7C,KAAK,CAAC;EACjC,IAAI4C,KAAK,EAAE;IACP,OAAO;MACH1C,CAAC,EAAER,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCzC,CAAC,EAAET,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvCxC,CAAC,EAAEV,eAAe,CAACkD,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;MACvClC,MAAM,EAAEiC,KAAK,GAAG,MAAM,GAAG;IAC7B,CAAC;EACL;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS/B,cAAcA,CAACZ,KAAK,EAAE;EAClC,OAAO8C,OAAO,CAACjB,QAAQ,CAACJ,QAAQ,CAACoB,IAAI,CAAChC,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
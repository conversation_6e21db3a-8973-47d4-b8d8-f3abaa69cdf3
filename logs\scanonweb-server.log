2025-06-05 08:59:29 [main] INFO  com.scanonweb.ScanOnWebApplication - Starting ScanOnWebApplication v1.0.0 using Java 17.0.8 with PID 6104 (D:\peihexian\scanonwebh5_demo\java\springboot\target\scanonweb-server-1.0.0.jar started by phx in D:\peihexian\scanonwebh5_demo)
2025-06-05 08:59:29 [main] DEBUG com.scanonweb.ScanOnWebApplication - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 08:59:29 [main] INFO  com.scanonweb.ScanOnWebApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-05 08:59:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-05 08:59:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 08:59:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 08:59:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 08:59:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1909 ms
2025-06-05 08:59:32 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 13 mappings in 'requestMappingHandlerMapping'
2025-06-05 08:59:32 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
2025-06-05 08:59:32 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 08:59:32 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 08:59:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path ''
2025-06-05 08:59:32 [main] INFO  com.scanonweb.ScanOnWebApplication - Started ScanOnWebApplication in 3.476 seconds (process running for 4.192)

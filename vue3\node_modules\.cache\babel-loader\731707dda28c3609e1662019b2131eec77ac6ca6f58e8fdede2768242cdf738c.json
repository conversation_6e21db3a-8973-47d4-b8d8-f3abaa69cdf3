{"ast": null, "code": "export { default as clamp } from './clamp.js';\nexport { default as inRange } from './inRange.js';\nexport { default as random } from './random.js';\nexport { default } from './number.default.js';", "map": {"version": 3, "names": ["default", "clamp", "inRange", "random"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/number.js"], "sourcesContent": ["export { default as clamp } from './clamp.js';\nexport { default as inRange } from './inRange.js';\nexport { default as random } from './random.js';\nexport { default } from './number.default.js';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,KAAK,QAAQ,YAAY;AAC7C,SAASD,OAAO,IAAIE,OAAO,QAAQ,cAAc;AACjD,SAASF,OAAO,IAAIG,MAAM,QAAQ,aAAa;AAC/C,SAASH,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
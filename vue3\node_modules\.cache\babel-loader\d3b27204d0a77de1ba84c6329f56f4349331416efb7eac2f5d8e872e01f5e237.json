{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.union` except that it accepts `comparator` which\n * is invoked to compare elements of `arrays`. Result values are chosen from\n * the first array in which the value occurs. The comparator is invoked\n * with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.unionWith(objects, others, _.isEqual);\n * // => [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }, { 'x': 1, 'y': 1 }]\n */\nvar unionWith = baseRest(function (arrays) {\n  var comparator = last(arrays);\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), undefined, comparator);\n});\nexport default unionWith;", "map": {"version": 3, "names": ["baseFlatten", "baseRest", "baseUniq", "isArrayLikeObject", "last", "unionWith", "arrays", "comparator", "undefined"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/unionWith.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport baseRest from './_baseRest.js';\nimport baseUniq from './_baseUniq.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.union` except that it accepts `comparator` which\n * is invoked to compare elements of `arrays`. Result values are chosen from\n * the first array in which the value occurs. The comparator is invoked\n * with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of combined values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.unionWith(objects, others, _.isEqual);\n * // => [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }, { 'x': 1, 'y': 1 }]\n */\nvar unionWith = baseRest(function(arrays) {\n  var comparator = last(arrays);\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), undefined, comparator);\n});\n\nexport default unionWith;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGJ,QAAQ,CAAC,UAASK,MAAM,EAAE;EACxC,IAAIC,UAAU,GAAGH,IAAI,CAACE,MAAM,CAAC;EAC7BC,UAAU,GAAG,OAAOA,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAGC,SAAS;EACrE,OAAON,QAAQ,CAACF,WAAW,CAACM,MAAM,EAAE,CAAC,EAAEH,iBAAiB,EAAE,IAAI,CAAC,EAAEK,SAAS,EAAED,UAAU,CAAC;AACzF,CAAC,CAAC;AAEF,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
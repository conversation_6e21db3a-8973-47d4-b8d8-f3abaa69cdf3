[{"D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\main.js": "1", "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\App.vue": "2", "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\components\\ScanOnWebNew.vue": "3", "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\scanonweb.js": "4", "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\services\\api.js": "5"}, {"size": 417, "mtime": 1749017588960, "results": "6", "hashOfConfig": "7"}, {"size": 870, "mtime": 1749017603495, "results": "8", "hashOfConfig": "7"}, {"size": 28247, "mtime": 1749019650188, "results": "9", "hashOfConfig": "7"}, {"size": 10900, "mtime": 1715657320000, "results": "10", "hashOfConfig": "7"}, {"size": 3616, "mtime": 1749017351093, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "14"}, "1kev40s", {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "17"}, {"filePath": "18", "messages": "19", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "14"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "14"}, "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\main.js", [], [], "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\App.vue", [], [], "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\components\\ScanOnWebNew.vue", ["24"], "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\scanonweb.js", [], "D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\services\\api.js", [], {"ruleId": "25", "severity": 2, "message": "26", "line": 409, "column": 10, "nodeType": "27", "messageId": "28", "endLine": 409, "endColumn": 17}, "no-unused-vars", "'scanAPI' is defined but never used.", "Identifier", "unusedVar"]
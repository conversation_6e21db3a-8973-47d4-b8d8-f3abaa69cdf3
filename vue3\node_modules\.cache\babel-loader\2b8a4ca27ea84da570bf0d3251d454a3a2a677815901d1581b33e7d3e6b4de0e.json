{"ast": null, "code": "// scanonweb.js\n\nclass ScanOnWeb {\n  constructor() {\n    this.scaner_work_config = {\n      showUI: false,\n      dpi_x: 300,\n      dpi_y: 300,\n      deviceIndex: 0,\n      showDialog: false,\n      autoFeedEnable: true,\n      autoFeed: false,\n      dupxMode: false,\n      autoDeskew: false,\n      autoBorderDetection: false,\n      colorMode: \"RGB\",\n      transMode: \"memory\"\n    };\n    this.h5socket = null;\n    this.imageCount = 0;\n    this.tryConnect();\n  }\n  getConnectedServer(wssUrls) {\n    console.log(\"尝试连接托盘扫描服务websocket服务器...\");\n    return new Promise((resolve, reject) => {\n      const server = new WebSocket(wssUrls[0]);\n      server.onopen = () => resolve(server);\n      server.onerror = err => reject(err);\n    }).then(server => {\n      console.log(\"连接websocket服务器成功!\");\n      this.initWebsocketCallback(server);\n      console.log(\"尝试获取扫描设备列表...\");\n      this.loadDevices();\n      return server;\n    }).catch(err => {\n      console.error(\"连接websocket服务器失败: \" + err);\n      if (wssUrls.length > 1) {\n        return this.getConnectedServer(wssUrls.slice(1));\n      } else {\n        console.error(\"无法连接到任何 WebSocket 服务器\");\n        return null;\n      }\n    });\n  }\n  tryConnect() {\n    const wssUrls = [\"ws://127.0.0.1:1001\", \"ws://127.0.0.1:2001\", \"ws://127.0.0.1:3001\", \"ws://127.0.0.1:4001\", \"ws://127.0.0.1:5001\"];\n    this.getConnectedServer(wssUrls).then(server => {\n      if (server) {\n        this.h5socket = server;\n      }\n    });\n  }\n  initWebsocketCallback(server) {\n    this.h5socket = server;\n    this.h5socket.onerror = this.onSocketError;\n    this.h5socket.onmessage = this.onSocketMessage.bind(this);\n  }\n  onSocketError(event) {\n    alert(\"无法连接扫描服务程序,请检查扫描服务程序是否已经启动！\");\n    console.log(\"WebSocket error: \" + event.data);\n  }\n  onSocketMessage(event) {\n    const msg = JSON.parse(event.data);\n    switch (msg.cmd_type) {\n      case \"getDevicesList\":\n        if (this.isCallbackExist(this.onGetDevicesListEvent)) {\n          this.onGetDevicesListEvent(msg);\n        }\n        break;\n      case \"scanComplete\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onScanFinishedEvent)) {\n          this.onScanFinishedEvent(msg);\n        }\n        break;\n      case \"selectScanDevice\":\n        this.scaner_work_config.deviceIndex = msg.currentIndex;\n        this.scaner_work_config.showDialog = msg.showDialog;\n        this.scaner_work_config.autoFeedEnable = msg.autoFeedEnable;\n        this.scaner_work_config.autoFeed = msg.autoFeed;\n        this.scaner_work_config.dupxMode = msg.dupxMode;\n        this.scaner_work_config.autoDeskew = msg.autoDeskew;\n        this.scaner_work_config.autoBorderDetection = msg.autoBorderDetection;\n        if (this.isCallbackExist(this.onSelectScanDeviceEvent)) {\n          this.onSelectScanDeviceEvent(msg);\n        }\n        break;\n      case \"getImageCount\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onGetImageCountEvent)) {\n          this.onGetImageCountEvent(msg);\n        }\n        break;\n      case \"getAllImage\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onGetAllImageEvent)) {\n          this.onGetAllImageEvent(msg);\n        }\n        break;\n      case \"getImageById\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onGetImageByIdEvent)) {\n          this.onGetImageByIdEvent(msg);\n        }\n        break;\n      case \"loadImageFromUrl\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onLoadImageFromUrlEvent)) {\n          this.onLoadImageFromUrlEvent(msg);\n        }\n        break;\n      case \"rotateImage\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onRotateImageEvent)) {\n          this.onRotateImageEvent(msg);\n        }\n        break;\n      case \"getImageSize\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onGetImageSizeEvent)) {\n          this.onGetImageSizeEvent(msg);\n        }\n        break;\n      case \"uploadAllImageAsPdfToUrl\":\n        if (this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)) {\n          this.onUploadAllImageAsPdfToUrlEvent(msg);\n        }\n        break;\n      case \"uploadAllImageAsTiffToUrl\":\n        if (this.isCallbackExist(this.uploadAllImageAsTiffToUrlEvent)) {\n          console.log(\"TIFF上传回调:\", msg);\n          this.uploadAllImageAsTiffToUrlEvent(msg);\n        }\n        break;\n      case \"uploadJpgImageByIndex\":\n        if (this.isCallbackExist(this.uploadJpgImageByIndexEvent)) {\n          this.uploadJpgImageByIndexEvent(msg);\n        }\n        break;\n      case \"upload\":\n        this.imageCount = msg.imageCount;\n        if (this.isCallbackExist(this.onUploadEvent)) {\n          this.onUploadEvent(msg);\n        }\n        break;\n      case \"imageEdited\":\n        if (this.isCallbackExist(this.onImageEditedEvent)) {\n          this.onImageEditedEvent(msg);\n        }\n        break;\n    }\n  }\n  isCallbackExist(f) {\n    return typeof f === 'function';\n  }\n  sendWebSocketCommand(commandData) {\n    if (this.h5socket && this.h5socket.readyState === 1) {\n      this.h5socket.send(JSON.stringify(commandData));\n    } else {\n      console.log(\"WebSocket 连接未建立或已断开！发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!\");\n      //alert(\"发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!\");\n    }\n  }\n  setLicenseKey(licenseMode, key1, key2, licenseServerUrl) {\n    const cmdObj = {\n      cmd_type: \"setLicenseKey\",\n      licenseMode,\n      key1,\n      key2,\n      url: licenseServerUrl\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  loadDevices() {\n    const cmdObj = {\n      cmd_type: \"getDevicesList\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  selectScanDevice(deviceIndex) {\n    const cmdObj = {\n      cmd_type: \"selectScanDevice\",\n      deviceIndex\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  startScan() {\n    const cmdObj = {\n      cmd_type: \"startScan\",\n      config: this.scaner_work_config\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  clearAll() {\n    const cmdObj = {\n      cmd_type: \"clearAll\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  getImageCount() {\n    const cmdObj = {\n      cmd_type: \"getImageCount\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  getAllImage() {\n    const cmdObj = {\n      cmd_type: \"getAllImage\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  getImageById(index) {\n    const cmdObj = {\n      cmd_type: \"getImageById\",\n      index\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  loadImageFromUrl(url) {\n    const cmdObj = {\n      cmd_type: \"loadImageFromUrl\",\n      url\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  rotateImage(index, angle) {\n    const cmdObj = {\n      cmd_type: \"rotateImage\",\n      index,\n      angle\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  getImageSize(index) {\n    const cmdObj = {\n      cmd_type: \"getImageSize\",\n      index\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  deleteImageByIndex(index) {\n    const cmdObj = {\n      cmd_type: \"deleteImageByIndex\",\n      index\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  uploadAllImageAsPdfToUrl(url, id, desc) {\n    const cmdObj = {\n      cmd_type: \"uploadAllImageAsPdfToUrl\",\n      url,\n      id,\n      desc\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  uploadAllImageAsTiffToUrl(url, id, desc) {\n    const cmdObj = {\n      cmd_type: \"uploadAllImageAsTiffToUrl\",\n      url,\n      id,\n      desc\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  uploadJpgImageByIndex(url, id, desc, index) {\n    const cmdObj = {\n      cmd_type: \"uploadJpgImageByIndex\",\n      index,\n      url,\n      id,\n      desc\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  saveAllImageToLocal(filename) {\n    const cmdObj = {\n      cmd_type: \"saveAllImageToLocal\",\n      filename\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  openClientLocalfile() {\n    const cmdObj = {\n      cmd_type: \"openClientLocalfile\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  ftpUploadAllImage(serverIp, port, username, password, serverPath, filename) {\n    const cmdObj = {\n      cmd_type: \"ftpUploadAllImage\",\n      serverIp,\n      port,\n      username,\n      password,\n      serverPath,\n      filename\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  setUploadButtonVisible(visible) {\n    const cmdObj = {\n      cmd_type: \"setUploadButtonVisible\",\n      visible\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  setFocus() {\n    const cmdObj = {\n      cmd_type: \"focus\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  hidden() {\n    const cmdObj = {\n      cmd_type: \"hidden\"\n    };\n    this.sendWebSocketCommand(cmdObj);\n  }\n  closeWebSocket() {\n    if (this.h5socket) {\n      this.h5socket.close();\n    }\n  }\n}\nexport default ScanOnWeb;", "map": {"version": 3, "names": ["ScanOnWeb", "constructor", "scaner_work_config", "showUI", "dpi_x", "dpi_y", "deviceIndex", "showDialog", "autoFeedEnable", "autoFeed", "dupxMode", "autoDeskew", "autoBorderDetection", "colorMode", "transMode", "h5socket", "imageCount", "tryConnect", "getConnectedServer", "wssUrls", "console", "log", "Promise", "resolve", "reject", "server", "WebSocket", "onopen", "onerror", "err", "then", "initWebsocketCallback", "loadDevices", "catch", "error", "length", "slice", "onSocketError", "onmessage", "onSocketMessage", "bind", "event", "alert", "data", "msg", "JSON", "parse", "cmd_type", "isCallbackExist", "onGetDevicesListEvent", "onScanFinishedEvent", "currentIndex", "onSelectScanDeviceEvent", "onGetImageCountEvent", "onGetAllImageEvent", "onGetImageByIdEvent", "onLoadImageFromUrlEvent", "onRotateImageEvent", "onGetImageSizeEvent", "onUploadAllImageAsPdfToUrlEvent", "uploadAllImageAsTiffToUrlEvent", "uploadJpgImageByIndexEvent", "onUploadEvent", "onImageEditedEvent", "f", "sendWebSocketCommand", "commandData", "readyState", "send", "stringify", "setLicenseKey", "licenseMode", "key1", "key2", "licenseServerUrl", "cmdObj", "url", "selectScanDevice", "startScan", "config", "clearAll", "getImageCount", "getAllImage", "getImageById", "index", "loadImageFromUrl", "rotateImage", "angle", "getImageSize", "deleteImageByIndex", "uploadAllImageAsPdfToUrl", "id", "desc", "uploadAllImageAsTiffToUrl", "uploadJpgImageByIndex", "saveAllImageToLocal", "filename", "openClientLocalfile", "ftpUploadAllImage", "serverIp", "port", "username", "password", "serverPath", "setUploadButtonVisible", "visible", "setFocus", "hidden", "closeWebSocket", "close"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/src/scanonweb.js"], "sourcesContent": ["// scanonweb.js\r\n\r\nclass ScanOnWeb {\r\n    constructor() {\r\n        this.scaner_work_config = {\r\n            showUI: false,\r\n            dpi_x: 300,\r\n            dpi_y: 300,\r\n            deviceIndex: 0,\r\n            showDialog: false,\r\n            autoFeedEnable: true,\r\n            autoFeed: false,\r\n            dupxMode: false,\r\n            autoDeskew: false,\r\n            autoBorderDetection: false,\r\n            colorMode: \"RGB\",\r\n            transMode: \"memory\"\r\n        };\r\n        this.h5socket = null;\r\n        this.imageCount = 0;\r\n        this.tryConnect();\r\n    }\r\n\r\n    getConnectedServer(wssUrls) {\r\n        console.log(\"尝试连接托盘扫描服务websocket服务器...\");\r\n        return new Promise((resolve, reject) => {\r\n            const server = new WebSocket(wssUrls[0]);\r\n            server.onopen = () => resolve(server);\r\n            server.onerror = (err) => reject(err);\r\n        }).then((server) => {\r\n            console.log(\"连接websocket服务器成功!\");\r\n            this.initWebsocketCallback(server);\r\n            console.log(\"尝试获取扫描设备列表...\");\r\n            this.loadDevices();\r\n            return server;\r\n        }).catch((err) => {\r\n            console.error(\"连接websocket服务器失败: \" + err);\r\n            if (wssUrls.length > 1) {\r\n                return this.getConnectedServer(wssUrls.slice(1));\r\n            } else {\r\n                console.error(\"无法连接到任何 WebSocket 服务器\");\r\n                return null;\r\n            }\r\n        });\r\n    }\r\n\r\n    tryConnect() {\r\n        const wssUrls = [\"ws://127.0.0.1:1001\", \"ws://127.0.0.1:2001\", \"ws://127.0.0.1:3001\", \"ws://127.0.0.1:4001\", \"ws://127.0.0.1:5001\"];\r\n        this.getConnectedServer(wssUrls).then(server => {\r\n            if (server) {\r\n                this.h5socket = server;\r\n            }\r\n        });\r\n    }\r\n\r\n    initWebsocketCallback(server) {\r\n        this.h5socket = server;\r\n        this.h5socket.onerror = this.onSocketError;\r\n        this.h5socket.onmessage = this.onSocketMessage.bind(this);\r\n    }\r\n\r\n    onSocketError(event) {\r\n        alert(\"无法连接扫描服务程序,请检查扫描服务程序是否已经启动！\");\r\n        console.log(\"WebSocket error: \" + event.data);\r\n    }\r\n\r\n    onSocketMessage(event) {\r\n        const msg = JSON.parse(event.data);\r\n        switch (msg.cmd_type) {\r\n            case \"getDevicesList\":\r\n                if (this.isCallbackExist(this.onGetDevicesListEvent)) {\r\n                    this.onGetDevicesListEvent(msg);\r\n                }\r\n                break;\r\n            case \"scanComplete\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onScanFinishedEvent)) {\r\n                    this.onScanFinishedEvent(msg);\r\n                }\r\n                break;\r\n            case \"selectScanDevice\":\r\n                this.scaner_work_config.deviceIndex = msg.currentIndex;\r\n                this.scaner_work_config.showDialog = msg.showDialog;\r\n                this.scaner_work_config.autoFeedEnable = msg.autoFeedEnable;\r\n                this.scaner_work_config.autoFeed = msg.autoFeed;\r\n                this.scaner_work_config.dupxMode = msg.dupxMode;\r\n                this.scaner_work_config.autoDeskew = msg.autoDeskew;\r\n                this.scaner_work_config.autoBorderDetection = msg.autoBorderDetection;\r\n\r\n                if (this.isCallbackExist(this.onSelectScanDeviceEvent)) {\r\n                    this.onSelectScanDeviceEvent(msg);\r\n                }\r\n                break;\r\n            case \"getImageCount\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onGetImageCountEvent)) {\r\n                    this.onGetImageCountEvent(msg);\r\n                }\r\n                break;\r\n            case \"getAllImage\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onGetAllImageEvent)) {\r\n                    this.onGetAllImageEvent(msg);\r\n                }\r\n                break;\r\n            case \"getImageById\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onGetImageByIdEvent)) {\r\n                    this.onGetImageByIdEvent(msg);\r\n                }\r\n                break;\r\n            case \"loadImageFromUrl\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onLoadImageFromUrlEvent)) {\r\n                    this.onLoadImageFromUrlEvent(msg);\r\n                }\r\n                break;\r\n            case \"rotateImage\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onRotateImageEvent)) {\r\n                    this.onRotateImageEvent(msg);\r\n                }\r\n                break;\r\n            case \"getImageSize\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onGetImageSizeEvent)) {\r\n                    this.onGetImageSizeEvent(msg);\r\n                }\r\n                break;\r\n            case \"uploadAllImageAsPdfToUrl\":\r\n                if (this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)) {\r\n                    this.onUploadAllImageAsPdfToUrlEvent(msg);\r\n                }\r\n                break;\r\n            case \"uploadAllImageAsTiffToUrl\": \r\n                if (this.isCallbackExist(this.uploadAllImageAsTiffToUrlEvent)) {\r\n                    console.log(\"TIFF上传回调:\", msg);\r\n                    this.uploadAllImageAsTiffToUrlEvent(msg);\r\n                }\r\n                break;\r\n            case \"uploadJpgImageByIndex\":\r\n                if (this.isCallbackExist(this.uploadJpgImageByIndexEvent)) {\r\n                    this.uploadJpgImageByIndexEvent(msg);\r\n                }\r\n                break;\r\n            case \"upload\":\r\n                this.imageCount = msg.imageCount;\r\n                if (this.isCallbackExist(this.onUploadEvent)) {\r\n                    this.onUploadEvent(msg);\r\n                }\r\n                break;\r\n            case \"imageEdited\":\r\n                if (this.isCallbackExist(this.onImageEditedEvent)) {\r\n                    this.onImageEditedEvent(msg);\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n    isCallbackExist(f) {\r\n        return typeof f === 'function';\r\n    }\r\n\r\n    sendWebSocketCommand(commandData) {\r\n        if (this.h5socket && this.h5socket.readyState === 1) {\r\n            this.h5socket.send(JSON.stringify(commandData));\r\n        } else {\r\n            console.log(\"WebSocket 连接未建立或已断开！发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!\");\r\n            //alert(\"发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!\");\r\n        }\r\n    }\r\n\r\n    setLicenseKey(licenseMode, key1, key2, licenseServerUrl) {\r\n        const cmdObj = {\r\n            cmd_type: \"setLicenseKey\",\r\n            licenseMode,\r\n            key1,\r\n            key2,\r\n            url: licenseServerUrl\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    loadDevices() {\r\n        const cmdObj = { cmd_type: \"getDevicesList\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    selectScanDevice(deviceIndex) {\r\n        const cmdObj = {\r\n            cmd_type: \"selectScanDevice\",\r\n            deviceIndex\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    startScan() {\r\n        const cmdObj = {\r\n            cmd_type: \"startScan\",\r\n            config: this.scaner_work_config\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    clearAll() {\r\n        const cmdObj = { cmd_type: \"clearAll\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    getImageCount() {\r\n        const cmdObj = { cmd_type: \"getImageCount\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    getAllImage() {\r\n        const cmdObj = { cmd_type: \"getAllImage\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    getImageById(index) {\r\n        const cmdObj = {\r\n            cmd_type: \"getImageById\",\r\n            index\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    loadImageFromUrl(url) {\r\n        const cmdObj = {\r\n            cmd_type: \"loadImageFromUrl\",\r\n            url\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    rotateImage(index, angle) {\r\n        const cmdObj = {\r\n            cmd_type: \"rotateImage\",\r\n            index,\r\n            angle\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    getImageSize(index) {\r\n        const cmdObj = {\r\n            cmd_type: \"getImageSize\",\r\n            index\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    deleteImageByIndex(index) {\r\n        const cmdObj = {\r\n            cmd_type: \"deleteImageByIndex\",\r\n            index\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    uploadAllImageAsPdfToUrl(url, id, desc) {\r\n        const cmdObj = {\r\n            cmd_type: \"uploadAllImageAsPdfToUrl\",\r\n            url,\r\n            id,\r\n            desc\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    uploadAllImageAsTiffToUrl(url, id, desc) {\r\n        const cmdObj = {\r\n            cmd_type: \"uploadAllImageAsTiffToUrl\",\r\n            url,\r\n            id,\r\n            desc\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    uploadJpgImageByIndex(url, id, desc, index) {\r\n        const cmdObj = {\r\n            cmd_type: \"uploadJpgImageByIndex\",\r\n            index,\r\n            url,\r\n            id,\r\n            desc\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    saveAllImageToLocal(filename) {\r\n        const cmdObj = {\r\n            cmd_type: \"saveAllImageToLocal\",\r\n            filename\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    openClientLocalfile() {\r\n        const cmdObj = { cmd_type: \"openClientLocalfile\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    ftpUploadAllImage(serverIp, port, username, password, serverPath, filename) {\r\n        const cmdObj = {\r\n            cmd_type: \"ftpUploadAllImage\",\r\n            serverIp,\r\n            port,\r\n            username,\r\n            password,\r\n            serverPath,\r\n            filename\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    setUploadButtonVisible(visible) {\r\n        const cmdObj = {\r\n            cmd_type: \"setUploadButtonVisible\",\r\n            visible\r\n        };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    setFocus() {\r\n        const cmdObj = { cmd_type: \"focus\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    hidden() {\r\n        const cmdObj = { cmd_type: \"hidden\" };\r\n        this.sendWebSocketCommand(cmdObj);\r\n    }\r\n\r\n    closeWebSocket() {\r\n        if (this.h5socket) {\r\n            this.h5socket.close();\r\n        }\r\n    }\r\n}\r\n\r\nexport default ScanOnWeb;\r\n"], "mappings": "AAAA;;AAEA,MAAMA,SAAS,CAAC;EACZC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,kBAAkB,GAAG;MACtBC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,KAAK;MACjBC,mBAAmB,EAAE,KAAK;MAC1BC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACf,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EAEAC,kBAAkBA,CAACC,OAAO,EAAE;IACxBC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,MAAMC,MAAM,GAAG,IAAIC,SAAS,CAACP,OAAO,CAAC,CAAC,CAAC,CAAC;MACxCM,MAAM,CAACE,MAAM,GAAG,MAAMJ,OAAO,CAACE,MAAM,CAAC;MACrCA,MAAM,CAACG,OAAO,GAAIC,GAAG,IAAKL,MAAM,CAACK,GAAG,CAAC;IACzC,CAAC,CAAC,CAACC,IAAI,CAAEL,MAAM,IAAK;MAChBL,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,IAAI,CAACU,qBAAqB,CAACN,MAAM,CAAC;MAClCL,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,IAAI,CAACW,WAAW,CAAC,CAAC;MAClB,OAAOP,MAAM;IACjB,CAAC,CAAC,CAACQ,KAAK,CAAEJ,GAAG,IAAK;MACdT,OAAO,CAACc,KAAK,CAAC,oBAAoB,GAAGL,GAAG,CAAC;MACzC,IAAIV,OAAO,CAACgB,MAAM,GAAG,CAAC,EAAE;QACpB,OAAO,IAAI,CAACjB,kBAAkB,CAACC,OAAO,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM;QACHhB,OAAO,CAACc,KAAK,CAAC,uBAAuB,CAAC;QACtC,OAAO,IAAI;MACf;IACJ,CAAC,CAAC;EACN;EAEAjB,UAAUA,CAAA,EAAG;IACT,MAAME,OAAO,GAAG,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC;IACnI,IAAI,CAACD,kBAAkB,CAACC,OAAO,CAAC,CAACW,IAAI,CAACL,MAAM,IAAI;MAC5C,IAAIA,MAAM,EAAE;QACR,IAAI,CAACV,QAAQ,GAAGU,MAAM;MAC1B;IACJ,CAAC,CAAC;EACN;EAEAM,qBAAqBA,CAACN,MAAM,EAAE;IAC1B,IAAI,CAACV,QAAQ,GAAGU,MAAM;IACtB,IAAI,CAACV,QAAQ,CAACa,OAAO,GAAG,IAAI,CAACS,aAAa;IAC1C,IAAI,CAACtB,QAAQ,CAACuB,SAAS,GAAG,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;EAC7D;EAEAH,aAAaA,CAACI,KAAK,EAAE;IACjBC,KAAK,CAAC,6BAA6B,CAAC;IACpCtB,OAAO,CAACC,GAAG,CAAC,mBAAmB,GAAGoB,KAAK,CAACE,IAAI,CAAC;EACjD;EAEAJ,eAAeA,CAACE,KAAK,EAAE;IACnB,MAAMG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAACE,IAAI,CAAC;IAClC,QAAQC,GAAG,CAACG,QAAQ;MAChB,KAAK,gBAAgB;QACjB,IAAI,IAAI,CAACC,eAAe,CAAC,IAAI,CAACC,qBAAqB,CAAC,EAAE;UAClD,IAAI,CAACA,qBAAqB,CAACL,GAAG,CAAC;QACnC;QACA;MACJ,KAAK,cAAc;QACf,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACE,mBAAmB,CAAC,EAAE;UAChD,IAAI,CAACA,mBAAmB,CAACN,GAAG,CAAC;QACjC;QACA;MACJ,KAAK,kBAAkB;QACnB,IAAI,CAAC1C,kBAAkB,CAACI,WAAW,GAAGsC,GAAG,CAACO,YAAY;QACtD,IAAI,CAACjD,kBAAkB,CAACK,UAAU,GAAGqC,GAAG,CAACrC,UAAU;QACnD,IAAI,CAACL,kBAAkB,CAACM,cAAc,GAAGoC,GAAG,CAACpC,cAAc;QAC3D,IAAI,CAACN,kBAAkB,CAACO,QAAQ,GAAGmC,GAAG,CAACnC,QAAQ;QAC/C,IAAI,CAACP,kBAAkB,CAACQ,QAAQ,GAAGkC,GAAG,CAAClC,QAAQ;QAC/C,IAAI,CAACR,kBAAkB,CAACS,UAAU,GAAGiC,GAAG,CAACjC,UAAU;QACnD,IAAI,CAACT,kBAAkB,CAACU,mBAAmB,GAAGgC,GAAG,CAAChC,mBAAmB;QAErE,IAAI,IAAI,CAACoC,eAAe,CAAC,IAAI,CAACI,uBAAuB,CAAC,EAAE;UACpD,IAAI,CAACA,uBAAuB,CAACR,GAAG,CAAC;QACrC;QACA;MACJ,KAAK,eAAe;QAChB,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACK,oBAAoB,CAAC,EAAE;UACjD,IAAI,CAACA,oBAAoB,CAACT,GAAG,CAAC;QAClC;QACA;MACJ,KAAK,aAAa;QACd,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACM,kBAAkB,CAAC,EAAE;UAC/C,IAAI,CAACA,kBAAkB,CAACV,GAAG,CAAC;QAChC;QACA;MACJ,KAAK,cAAc;QACf,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACO,mBAAmB,CAAC,EAAE;UAChD,IAAI,CAACA,mBAAmB,CAACX,GAAG,CAAC;QACjC;QACA;MACJ,KAAK,kBAAkB;QACnB,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACQ,uBAAuB,CAAC,EAAE;UACpD,IAAI,CAACA,uBAAuB,CAACZ,GAAG,CAAC;QACrC;QACA;MACJ,KAAK,aAAa;QACd,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACS,kBAAkB,CAAC,EAAE;UAC/C,IAAI,CAACA,kBAAkB,CAACb,GAAG,CAAC;QAChC;QACA;MACJ,KAAK,cAAc;QACf,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACU,mBAAmB,CAAC,EAAE;UAChD,IAAI,CAACA,mBAAmB,CAACd,GAAG,CAAC;QACjC;QACA;MACJ,KAAK,0BAA0B;QAC3B,IAAI,IAAI,CAACI,eAAe,CAAC,IAAI,CAACW,+BAA+B,CAAC,EAAE;UAC5D,IAAI,CAACA,+BAA+B,CAACf,GAAG,CAAC;QAC7C;QACA;MACJ,KAAK,2BAA2B;QAC5B,IAAI,IAAI,CAACI,eAAe,CAAC,IAAI,CAACY,8BAA8B,CAAC,EAAE;UAC3DxC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuB,GAAG,CAAC;UAC7B,IAAI,CAACgB,8BAA8B,CAAChB,GAAG,CAAC;QAC5C;QACA;MACJ,KAAK,uBAAuB;QACxB,IAAI,IAAI,CAACI,eAAe,CAAC,IAAI,CAACa,0BAA0B,CAAC,EAAE;UACvD,IAAI,CAACA,0BAA0B,CAACjB,GAAG,CAAC;QACxC;QACA;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC5B,UAAU,GAAG4B,GAAG,CAAC5B,UAAU;QAChC,IAAI,IAAI,CAACgC,eAAe,CAAC,IAAI,CAACc,aAAa,CAAC,EAAE;UAC1C,IAAI,CAACA,aAAa,CAAClB,GAAG,CAAC;QAC3B;QACA;MACJ,KAAK,aAAa;QACd,IAAI,IAAI,CAACI,eAAe,CAAC,IAAI,CAACe,kBAAkB,CAAC,EAAE;UAC/C,IAAI,CAACA,kBAAkB,CAACnB,GAAG,CAAC;QAChC;QACA;IACR;EACJ;EAEAI,eAAeA,CAACgB,CAAC,EAAE;IACf,OAAO,OAAOA,CAAC,KAAK,UAAU;EAClC;EAEAC,oBAAoBA,CAACC,WAAW,EAAE;IAC9B,IAAI,IAAI,CAACnD,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACoD,UAAU,KAAK,CAAC,EAAE;MACjD,IAAI,CAACpD,QAAQ,CAACqD,IAAI,CAACvB,IAAI,CAACwB,SAAS,CAACH,WAAW,CAAC,CAAC;IACnD,CAAC,MAAM;MACH9C,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;IACJ;EACJ;EAEAiD,aAAaA,CAACC,WAAW,EAAEC,IAAI,EAAEC,IAAI,EAAEC,gBAAgB,EAAE;IACrD,MAAMC,MAAM,GAAG;MACX5B,QAAQ,EAAE,eAAe;MACzBwB,WAAW;MACXC,IAAI;MACJC,IAAI;MACJG,GAAG,EAAEF;IACT,CAAC;IACD,IAAI,CAACT,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEA3C,WAAWA,CAAA,EAAG;IACV,MAAM2C,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAiB,CAAC;IAC7C,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAE,gBAAgBA,CAACvE,WAAW,EAAE;IAC1B,MAAMqE,MAAM,GAAG;MACX5B,QAAQ,EAAE,kBAAkB;MAC5BzC;IACJ,CAAC;IACD,IAAI,CAAC2D,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAG,SAASA,CAAA,EAAG;IACR,MAAMH,MAAM,GAAG;MACX5B,QAAQ,EAAE,WAAW;MACrBgC,MAAM,EAAE,IAAI,CAAC7E;IACjB,CAAC;IACD,IAAI,CAAC+D,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAK,QAAQA,CAAA,EAAG;IACP,MAAML,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAW,CAAC;IACvC,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAM,aAAaA,CAAA,EAAG;IACZ,MAAMN,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAgB,CAAC;IAC5C,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAO,WAAWA,CAAA,EAAG;IACV,MAAMP,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAc,CAAC;IAC1C,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAQ,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMT,MAAM,GAAG;MACX5B,QAAQ,EAAE,cAAc;MACxBqC;IACJ,CAAC;IACD,IAAI,CAACnB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAU,gBAAgBA,CAACT,GAAG,EAAE;IAClB,MAAMD,MAAM,GAAG;MACX5B,QAAQ,EAAE,kBAAkB;MAC5B6B;IACJ,CAAC;IACD,IAAI,CAACX,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAW,WAAWA,CAACF,KAAK,EAAEG,KAAK,EAAE;IACtB,MAAMZ,MAAM,GAAG;MACX5B,QAAQ,EAAE,aAAa;MACvBqC,KAAK;MACLG;IACJ,CAAC;IACD,IAAI,CAACtB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAa,YAAYA,CAACJ,KAAK,EAAE;IAChB,MAAMT,MAAM,GAAG;MACX5B,QAAQ,EAAE,cAAc;MACxBqC;IACJ,CAAC;IACD,IAAI,CAACnB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAc,kBAAkBA,CAACL,KAAK,EAAE;IACtB,MAAMT,MAAM,GAAG;MACX5B,QAAQ,EAAE,oBAAoB;MAC9BqC;IACJ,CAAC;IACD,IAAI,CAACnB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAe,wBAAwBA,CAACd,GAAG,EAAEe,EAAE,EAAEC,IAAI,EAAE;IACpC,MAAMjB,MAAM,GAAG;MACX5B,QAAQ,EAAE,0BAA0B;MACpC6B,GAAG;MACHe,EAAE;MACFC;IACJ,CAAC;IACD,IAAI,CAAC3B,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAkB,yBAAyBA,CAACjB,GAAG,EAAEe,EAAE,EAAEC,IAAI,EAAE;IACrC,MAAMjB,MAAM,GAAG;MACX5B,QAAQ,EAAE,2BAA2B;MACrC6B,GAAG;MACHe,EAAE;MACFC;IACJ,CAAC;IACD,IAAI,CAAC3B,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAmB,qBAAqBA,CAAClB,GAAG,EAAEe,EAAE,EAAEC,IAAI,EAAER,KAAK,EAAE;IACxC,MAAMT,MAAM,GAAG;MACX5B,QAAQ,EAAE,uBAAuB;MACjCqC,KAAK;MACLR,GAAG;MACHe,EAAE;MACFC;IACJ,CAAC;IACD,IAAI,CAAC3B,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAoB,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,MAAMrB,MAAM,GAAG;MACX5B,QAAQ,EAAE,qBAAqB;MAC/BiD;IACJ,CAAC;IACD,IAAI,CAAC/B,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAsB,mBAAmBA,CAAA,EAAG;IAClB,MAAMtB,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAsB,CAAC;IAClD,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAuB,iBAAiBA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEP,QAAQ,EAAE;IACxE,MAAMrB,MAAM,GAAG;MACX5B,QAAQ,EAAE,mBAAmB;MAC7BoD,QAAQ;MACRC,IAAI;MACJC,QAAQ;MACRC,QAAQ;MACRC,UAAU;MACVP;IACJ,CAAC;IACD,IAAI,CAAC/B,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEA6B,sBAAsBA,CAACC,OAAO,EAAE;IAC5B,MAAM9B,MAAM,GAAG;MACX5B,QAAQ,EAAE,wBAAwB;MAClC0D;IACJ,CAAC;IACD,IAAI,CAACxC,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEA+B,QAAQA,CAAA,EAAG;IACP,MAAM/B,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAQ,CAAC;IACpC,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAgC,MAAMA,CAAA,EAAG;IACL,MAAMhC,MAAM,GAAG;MAAE5B,QAAQ,EAAE;IAAS,CAAC;IACrC,IAAI,CAACkB,oBAAoB,CAACU,MAAM,CAAC;EACrC;EAEAiC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC7F,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAAC8F,KAAK,CAAC,CAAC;IACzB;EACJ;AACJ;AAEA,eAAe7G,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
@echo off
echo ================================
echo ScanOnWeb SpringBoot Server
echo ================================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请确保已安装Maven
    pause
    exit /b 1
)

echo 正在编译项目...
call mvn clean compile

if %errorlevel% neq 0 (
    echo 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo 正在启动SpringBoot服务器...
echo 访问地址: http://localhost:8080/api
echo 按 Ctrl+C 停止服务器

call mvn spring-boot:run

pause

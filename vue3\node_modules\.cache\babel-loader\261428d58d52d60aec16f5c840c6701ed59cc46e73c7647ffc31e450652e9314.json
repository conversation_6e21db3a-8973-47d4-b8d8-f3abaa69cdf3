{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nimport { datePickTypes } from '../../../../constants/date.mjs';\nimport { isArray } from '@vue/shared';\nconst selectionModes = [\"date\", \"dates\", \"year\", \"years\", \"month\", \"months\", \"week\", \"range\"];\nconst datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType(Function)\n  },\n  date: {\n    type: definePropType(Object),\n    required: true\n  },\n  minDate: {\n    type: definePropType(Object)\n  },\n  maxDate: {\n    type: definePropType(Object)\n  },\n  parsedValue: {\n    type: definePropType([Object, Array])\n  },\n  rangeState: {\n    type: definePropType(Object),\n    default: () => ({\n      endDate: null,\n      selecting: false\n    })\n  }\n});\nconst panelSharedProps = buildProps({\n  type: {\n    type: definePropType(String),\n    required: true,\n    values: datePickTypes\n  },\n  dateFormat: String,\n  timeFormat: String,\n  showNow: {\n    type: Boolean,\n    default: true\n  }\n});\nconst panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  parsedValue: {\n    type: definePropType(Array)\n  }\n});\nconst selectionModeWithDefault = mode => {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode\n  };\n};\nconst rangePickerSharedEmits = {\n  pick: range => isArray(range)\n};\nexport { datePickerSharedProps, panelRangeSharedProps, panelSharedProps, rangePickerSharedEmits, selectionModeWithDefault };", "map": {"version": 3, "names": ["selectionModes", "datePickerSharedProps", "buildProps", "disabledDate", "type", "definePropType", "Function", "date", "Object", "required", "minDate", "maxDate", "parsedValue", "Array", "rangeState", "default", "endDate", "selecting", "panelSharedProps", "String", "values", "datePickTypes", "dateFormat", "timeFormat", "showNow", "Boolean", "panelRangeSharedProps", "unlinkPanels", "selectionModeWithDefault", "mode", "rangePickerSharedEmits", "pick", "range", "isArray"], "sources": ["../../../../../../../packages/components/date-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType, isArray } from '@element-plus/utils'\nimport { datePickTypes } from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DatePickType } from '@element-plus/constants'\n\nconst selectionModes = [\n  'date',\n  'dates',\n  'year',\n  'years',\n  'month',\n  'months',\n  'week',\n  'range',\n]\n\nexport type RangeState = {\n  endDate: null | Dayjs\n  selecting: boolean\n}\n\nexport type DisabledDateType = (date: Date) => boolean\n\nexport const datePickerSharedProps = buildProps({\n  disabledDate: {\n    type: definePropType<DisabledDateType>(Function),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  minDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  maxDate: {\n    type: definePropType<Dayjs | null>(Object),\n  },\n  parsedValue: {\n    type: definePropType<Dayjs | Dayjs[]>([Object, Array]),\n  },\n  rangeState: {\n    type: definePropType<RangeState>(Object),\n    default: () => ({\n      endDate: null,\n      selecting: false,\n    }),\n  },\n} as const)\n\nexport const panelSharedProps = buildProps({\n  type: {\n    type: definePropType<DatePickType>(String),\n    required: true,\n    values: datePickTypes,\n  },\n  dateFormat: String,\n  timeFormat: String,\n  showNow: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\n\nexport const panelRangeSharedProps = buildProps({\n  unlinkPanels: Boolean,\n  parsedValue: {\n    type: definePropType<Dayjs[]>(Array),\n  },\n} as const)\n\nexport const selectionModeWithDefault = (\n  mode: typeof selectionModes[number]\n) => {\n  return {\n    type: String,\n    values: selectionModes,\n    default: mode,\n  }\n}\n\nexport const rangePickerSharedEmits = {\n  pick: (range: [Dayjs, Dayjs]) => isArray(range),\n}\n\nexport type RangePickerSharedEmits = typeof rangePickerSharedEmits\nexport type PanelRangeSharedProps = ExtractPropTypes<\n  typeof panelRangeSharedProps\n>\n"], "mappings": ";;;AAEA,MAAMA,cAAc,GAAG,CACrB,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,CACR;AACW,MAACC,qBAAqB,GAAGC,UAAU,CAAC;EAC9CC,YAAY,EAAE;IACZC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,IAAI,EAAE;IACJH,IAAI,EAAEC,cAAc,CAACG,MAAM,CAAC;IAC5BC,QAAQ,EAAE;EACd,CAAG;EACDC,OAAO,EAAE;IACPN,IAAI,EAAEC,cAAc,CAACG,MAAM;EAC/B,CAAG;EACDG,OAAO,EAAE;IACPP,IAAI,EAAEC,cAAc,CAACG,MAAM;EAC/B,CAAG;EACDI,WAAW,EAAE;IACXR,IAAI,EAAEC,cAAc,CAAC,CAACG,MAAM,EAAEK,KAAK,CAAC;EACxC,CAAG;EACDC,UAAU,EAAE;IACVV,IAAI,EAAEC,cAAc,CAACG,MAAM,CAAC;IAC5BO,OAAO,EAAEA,CAAA,MAAO;MACdC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE;IACjB,CAAK;EACL;AACA,CAAC;AACW,MAACC,gBAAgB,GAAGhB,UAAU,CAAC;EACzCE,IAAI,EAAE;IACJA,IAAI,EAAEC,cAAc,CAACc,MAAM,CAAC;IAC5BV,QAAQ,EAAE,IAAI;IACdW,MAAM,EAAEC;EACZ,CAAG;EACDC,UAAU,EAAEH,MAAM;EAClBI,UAAU,EAAEJ,MAAM;EAClBK,OAAO,EAAE;IACPpB,IAAI,EAAEqB,OAAO;IACbV,OAAO,EAAE;EACb;AACA,CAAC;AACW,MAACW,qBAAqB,GAAGxB,UAAU,CAAC;EAC9CyB,YAAY,EAAEF,OAAO;EACrBb,WAAW,EAAE;IACXR,IAAI,EAAEC,cAAc,CAACQ,KAAK;EAC9B;AACA,CAAC;AACW,MAACe,wBAAwB,GAAIC,IAAI,IAAK;EAChD,OAAO;IACLzB,IAAI,EAAEe,MAAM;IACZC,MAAM,EAAEpB,cAAc;IACtBe,OAAO,EAAEc;EACb,CAAG;AACH;AACY,MAACC,sBAAsB,GAAG;EACpCC,IAAI,EAAGC,KAAK,IAAKC,OAAO,CAACD,KAAK;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
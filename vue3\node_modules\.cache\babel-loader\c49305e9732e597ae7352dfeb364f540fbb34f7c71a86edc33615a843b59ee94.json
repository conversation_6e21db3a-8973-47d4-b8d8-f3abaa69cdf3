{"ast": null, "code": "import baseUpdate from './_baseUpdate.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * This method is like `_.update` except that it accepts `customizer` which is\n * invoked to produce the objects of `path`.  If `customizer` returns `undefined`\n * path creation is handled by the method instead. The `customizer` is invoked\n * with three arguments: (nsValue, key, nsObject).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.6.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {Function} updater The function to produce the updated value.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {};\n *\n * _.updateWith(object, '[0][1]', _.constant('a'), Object);\n * // => { '0': { '1': 'a' } }\n */\nfunction updateWith(object, path, updater, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  return object == null ? object : baseUpdate(object, path, castFunction(updater), customizer);\n}\nexport default updateWith;", "map": {"version": 3, "names": ["baseUpdate", "castFunction", "updateWith", "object", "path", "updater", "customizer", "undefined"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/updateWith.js"], "sourcesContent": ["import baseUpdate from './_baseUpdate.js';\nimport castFunction from './_castFunction.js';\n\n/**\n * This method is like `_.update` except that it accepts `customizer` which is\n * invoked to produce the objects of `path`.  If `customizer` returns `undefined`\n * path creation is handled by the method instead. The `customizer` is invoked\n * with three arguments: (nsValue, key, nsObject).\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.6.0\n * @category Object\n * @param {Object} object The object to modify.\n * @param {Array|string} path The path of the property to set.\n * @param {Function} updater The function to produce the updated value.\n * @param {Function} [customizer] The function to customize assigned values.\n * @returns {Object} Returns `object`.\n * @example\n *\n * var object = {};\n *\n * _.updateWith(object, '[0][1]', _.constant('a'), Object);\n * // => { '0': { '1': 'a' } }\n */\nfunction updateWith(object, path, updater, customizer) {\n  customizer = typeof customizer == 'function' ? customizer : undefined;\n  return object == null ? object : baseUpdate(object, path, castFunction(updater), customizer);\n}\n\nexport default updateWith;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAE;EACrDA,UAAU,GAAG,OAAOA,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAGC,SAAS;EACrE,OAAOJ,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGH,UAAU,CAACG,MAAM,EAAEC,IAAI,EAAEH,YAAY,CAACI,OAAO,CAAC,EAAEC,UAAU,CAAC;AAC9F;AAEA,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
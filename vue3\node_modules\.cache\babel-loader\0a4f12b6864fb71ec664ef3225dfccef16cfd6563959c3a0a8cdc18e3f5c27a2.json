{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, getCurrentInstance, computed, watch, unref } from 'vue';\nimport { getRowIdentity, walkTreeNode } from '../util.mjs';\nimport { isArray } from '@vue/shared';\nimport { isUndefined } from '../../../../utils/types.mjs';\nfunction useTree(watcherData) {\n  const expandRowKeys = ref([]);\n  const treeData = ref({});\n  const indent = ref(16);\n  const lazy = ref(false);\n  const lazyTreeNodeMap = ref({});\n  const lazyColumnIdentifier = ref(\"hasChildren\");\n  const childrenColumnName = ref(\"children\");\n  const checkStrictly = ref(false);\n  const instance = getCurrentInstance();\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {};\n    const data = watcherData.data.value || [];\n    return normalize(data);\n  });\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value;\n    const keys = Object.keys(lazyTreeNodeMap.value);\n    const res = {};\n    if (!keys.length) return res;\n    keys.forEach(key => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = {\n          children: []\n        };\n        lazyTreeNodeMap.value[key].forEach(row => {\n          const currentRowKey = getRowIdentity(row, rowKey);\n          item.children.push(currentRowKey);\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = {\n              children: []\n            };\n          }\n        });\n        res[key] = item;\n      }\n    });\n    return res;\n  });\n  const normalize = data => {\n    const rowKey = watcherData.rowKey.value;\n    const res = {};\n    walkTreeNode(data, (parent, children, level) => {\n      const parentId = getRowIdentity(parent, rowKey);\n      if (isArray(children)) {\n        res[parentId] = {\n          children: children.map(row => getRowIdentity(row, rowKey)),\n          level\n        };\n      } else if (lazy.value) {\n        res[parentId] = {\n          children: [],\n          lazy: true,\n          level\n        };\n      }\n    }, childrenColumnName.value, lazyColumnIdentifier.value);\n    return res;\n  };\n  const updateTreeData = (ifChangeExpandRowKeys = false, ifExpandAll = (_a => (_a = instance.store) == null ? void 0 : _a.states.defaultExpandAll.value)()) => {\n    var _a2;\n    const nested = normalizedData.value;\n    const normalizedLazyNode_ = normalizedLazyNode.value;\n    const keys = Object.keys(nested);\n    const newTreeData = {};\n    if (keys.length) {\n      const oldTreeData = unref(treeData);\n      const rootLazyRowKeys = [];\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key);\n          } else {\n            return !!(ifExpandAll || (oldValue == null ? void 0 : oldValue.expanded));\n          }\n        } else {\n          const included = ifExpandAll || expandRowKeys.value && expandRowKeys.value.includes(key);\n          return !!((oldValue == null ? void 0 : oldValue.expanded) || included);\n        }\n      };\n      keys.forEach(key => {\n        const oldValue = oldTreeData[key];\n        const newValue = {\n          ...nested[key]\n        };\n        newValue.expanded = getExpanded(oldValue, key);\n        if (newValue.lazy) {\n          const {\n            loaded = false,\n            loading = false\n          } = oldValue || {};\n          newValue.loaded = !!loaded;\n          newValue.loading = !!loading;\n          rootLazyRowKeys.push(key);\n        }\n        newTreeData[key] = newValue;\n      });\n      const lazyKeys = Object.keys(normalizedLazyNode_);\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach(key => {\n          const oldValue = oldTreeData[key];\n          const lazyNodeChildren = normalizedLazyNode_[key].children;\n          if (rootLazyRowKeys.includes(key)) {\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error(\"[ElTable]children must be an empty array.\");\n            }\n            newTreeData[key].children = lazyNodeChildren;\n          } else {\n            const {\n              loaded = false,\n              loading = false\n            } = oldValue || {};\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: \"\"\n            };\n          }\n        });\n      }\n    }\n    treeData.value = newTreeData;\n    (_a2 = instance.store) == null ? void 0 : _a2.updateTableScrollY();\n  };\n  watch(() => expandRowKeys.value, () => {\n    updateTreeData(true);\n  });\n  watch(() => normalizedData.value, () => {\n    updateTreeData();\n  });\n  watch(() => normalizedLazyNode.value, () => {\n    updateTreeData();\n  });\n  const updateTreeExpandKeys = value => {\n    expandRowKeys.value = value;\n    updateTreeData();\n  };\n  const isUseLazy = data => {\n    return lazy.value && data && \"loaded\" in data && !data.loaded;\n  };\n  const toggleTreeExpansion = (row, expanded) => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = id && treeData.value[id];\n    if (id && data && \"expanded\" in data) {\n      const oldExpanded = data.expanded;\n      expanded = isUndefined(expanded) ? !data.expanded : expanded;\n      treeData.value[id].expanded = expanded;\n      if (oldExpanded !== expanded) {\n        instance.emit(\"expand-change\", row, expanded);\n      }\n      isUseLazy(data) && loadData(row, id, data);\n      instance.store.updateTableScrollY();\n    }\n  };\n  const loadOrToggle = row => {\n    instance.store.assertRowKey();\n    const rowKey = watcherData.rowKey.value;\n    const id = getRowIdentity(row, rowKey);\n    const data = treeData.value[id];\n    if (isUseLazy(data)) {\n      loadData(row, id, data);\n    } else {\n      toggleTreeExpansion(row, void 0);\n    }\n  };\n  const loadData = (row, key, treeNode) => {\n    const {\n      load\n    } = instance.props;\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true;\n      load(row, treeNode, data => {\n        if (!isArray(data)) {\n          throw new TypeError(\"[ElTable] data must be an array\");\n        }\n        treeData.value[key].loading = false;\n        treeData.value[key].loaded = true;\n        treeData.value[key].expanded = true;\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data;\n        }\n        instance.emit(\"expand-change\", row, true);\n      });\n    }\n  };\n  const updateKeyChildren = (key, data) => {\n    const {\n      lazy: lazy2,\n      rowKey\n    } = instance.props;\n    if (!lazy2) return;\n    if (!rowKey) throw new Error(\"[Table] rowKey is required in updateKeyChild\");\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data;\n    }\n  };\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly\n    }\n  };\n}\nexport { useTree as default };", "map": {"version": 3, "names": ["useTree", "watcherData", "expandRowKeys", "ref", "treeData", "indent", "lazy", "lazyTreeNodeMap", "lazyColumnIdentifier", "childrenColumnName", "checkStrictly", "instance", "getCurrentInstance", "normalizedData", "computed", "<PERSON><PERSON><PERSON>", "value", "data", "normalize", "normalizedLazyNode", "keys", "Object", "res", "length", "for<PERSON>ach", "key", "item", "children", "row", "currentRowKey", "getRowIdentity", "push", "walkTreeNode", "parent", "level", "parentId", "isArray", "map", "updateTreeData", "ifChangeExpandRowKeys", "ifExpandAll", "_a", "store", "states", "defaultExpandAll", "_a2", "nested", "normalizedLazyNode_", "newTreeData", "oldTreeData", "unref", "rootLazyRowKeys", "getExpanded", "oldValue", "includes", "expanded", "included", "newValue", "loaded", "loading", "lazy<PERSON>eys", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON>n", "Error", "updateTableScrollY", "watch", "updateTreeExpandKeys", "isUseLazy", "toggleTreeExpansion", "assertRowKey", "id", "oldExpanded", "isUndefined", "emit", "loadData", "loadOrToggle", "treeNode", "load", "props", "TypeError", "update<PERSON>ey<PERSON><PERSON><PERSON>n", "lazy2"], "sources": ["../../../../../../../packages/components/table/src/store/tree.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, getCurrentInstance, ref, unref, watch } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { getRowIdentity, walkTreeNode } from '../util'\n\nimport type { WatcherPropsData } from '.'\nimport type { Table, TableProps } from '../table/defaults'\n\nfunction useTree<T>(watcherData: WatcherPropsData<T>) {\n  const expandRowKeys = ref<string[]>([])\n  const treeData = ref<unknown>({})\n  const indent = ref(16)\n  const lazy = ref(false)\n  const lazyTreeNodeMap = ref({})\n  const lazyColumnIdentifier = ref('hasChildren')\n  const childrenColumnName = ref('children')\n  const checkStrictly = ref(false)\n  const instance = getCurrentInstance() as Table<T>\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {}\n    const data = watcherData.data.value || []\n    return normalize(data)\n  })\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value\n    const keys = Object.keys(lazyTreeNodeMap.value)\n    const res = {}\n    if (!keys.length) return res\n    keys.forEach((key) => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = { children: [] }\n        lazyTreeNodeMap.value[key].forEach((row) => {\n          const currentRowKey = getRowIdentity(row, rowKey)\n          item.children.push(currentRowKey)\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = { children: [] }\n          }\n        })\n        res[key] = item\n      }\n    })\n    return res\n  })\n\n  const normalize = (data) => {\n    const rowKey = watcherData.rowKey.value\n    const res = {}\n    walkTreeNode(\n      data,\n      (parent, children, level) => {\n        const parentId = getRowIdentity(parent, rowKey)\n        if (isArray(children)) {\n          res[parentId] = {\n            children: children.map((row) => getRowIdentity(row, rowKey)),\n            level,\n          }\n        } else if (lazy.value) {\n          // 当 children 不存在且 lazy 为 true，该节点即为懒加载的节点\n          res[parentId] = {\n            children: [],\n            lazy: true,\n            level,\n          }\n        }\n      },\n      childrenColumnName.value,\n      lazyColumnIdentifier.value\n    )\n    return res\n  }\n\n  const updateTreeData = (\n    ifChangeExpandRowKeys = false,\n    ifExpandAll = instance.store?.states.defaultExpandAll.value\n  ) => {\n    const nested = normalizedData.value\n    const normalizedLazyNode_ = normalizedLazyNode.value\n    const keys = Object.keys(nested)\n    const newTreeData = {}\n\n    if (keys.length) {\n      const oldTreeData = unref(treeData)\n      const rootLazyRowKeys = []\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key)\n          } else {\n            return !!(ifExpandAll || oldValue?.expanded)\n          }\n        } else {\n          const included =\n            ifExpandAll ||\n            (expandRowKeys.value && expandRowKeys.value.includes(key))\n          return !!(oldValue?.expanded || included)\n        }\n      }\n      // 合并 expanded 与 display，确保数据刷新后，状态不变\n      keys.forEach((key) => {\n        const oldValue = oldTreeData[key]\n        const newValue = { ...nested[key] }\n        newValue.expanded = getExpanded(oldValue, key)\n        if (newValue.lazy) {\n          const { loaded = false, loading = false } = oldValue || {}\n          newValue.loaded = !!loaded\n          newValue.loading = !!loading\n          rootLazyRowKeys.push(key)\n        }\n        newTreeData[key] = newValue\n      })\n      // 根据懒加载数据更新 treeData\n      const lazyKeys = Object.keys(normalizedLazyNode_)\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach((key) => {\n          const oldValue = oldTreeData[key]\n          const lazyNodeChildren = normalizedLazyNode_[key].children\n          if (rootLazyRowKeys.includes(key)) {\n            // 懒加载的 root 节点，更新一下原有的数据，原来的 children 一定是空数组\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error('[ElTable]children must be an empty array.')\n            }\n            newTreeData[key].children = lazyNodeChildren\n          } else {\n            const { loaded = false, loading = false } = oldValue || {}\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: '',\n            }\n          }\n        })\n      }\n    }\n    treeData.value = newTreeData\n    instance.store?.updateTableScrollY()\n  }\n\n  watch(\n    () => expandRowKeys.value,\n    () => {\n      updateTreeData(true)\n    }\n  )\n\n  watch(\n    () => normalizedData.value,\n    () => {\n      updateTreeData()\n    }\n  )\n  watch(\n    () => normalizedLazyNode.value,\n    () => {\n      updateTreeData()\n    }\n  )\n\n  const updateTreeExpandKeys = (value: string[]) => {\n    expandRowKeys.value = value\n    updateTreeData()\n  }\n  const isUseLazy = (data): boolean => {\n    return lazy.value && data && 'loaded' in data && !data.loaded\n  }\n  const toggleTreeExpansion = (row: T, expanded?: boolean) => {\n    instance.store.assertRowKey()\n\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = id && treeData.value[id]\n    if (id && data && 'expanded' in data) {\n      const oldExpanded = data.expanded\n      expanded = isUndefined(expanded) ? !data.expanded : expanded\n      treeData.value[id].expanded = expanded\n      if (oldExpanded !== expanded) {\n        instance.emit('expand-change', row, expanded)\n      }\n      isUseLazy(data) && loadData(row, id, data)\n      instance.store.updateTableScrollY()\n    }\n  }\n\n  const loadOrToggle = (row) => {\n    instance.store.assertRowKey()\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = treeData.value[id]\n    if (isUseLazy(data)) {\n      loadData(row, id, data)\n    } else {\n      toggleTreeExpansion(row, undefined)\n    }\n  }\n\n  const loadData = (row: T, key: string, treeNode) => {\n    const { load } = instance.props as unknown as TableProps<T>\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true\n      load(row, treeNode, (data) => {\n        if (!isArray(data)) {\n          throw new TypeError('[ElTable] data must be an array')\n        }\n        treeData.value[key].loading = false\n        treeData.value[key].loaded = true\n        treeData.value[key].expanded = true\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data\n        }\n        instance.emit('expand-change', row, true)\n      })\n    }\n  }\n\n  const updateKeyChildren = (key: string, data: T[]) => {\n    const { lazy, rowKey } = instance.props as unknown as TableProps<T>\n    if (!lazy) return\n    if (!rowKey) throw new Error('[Table] rowKey is required in updateKeyChild')\n\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data\n    }\n  }\n\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly,\n    },\n  }\n}\n\nexport default useTree\n"], "mappings": ";;;;;AAGA,SAASA,OAAOA,CAACC,WAAW,EAAE;EAC5B,MAAMC,aAAa,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC7B,MAAMC,QAAQ,GAAGD,GAAG,CAAC,EAAE,CAAC;EACxB,MAAME,MAAM,GAAGF,GAAG,CAAC,EAAE,CAAC;EACtB,MAAMG,IAAI,GAAGH,GAAG,CAAC,KAAK,CAAC;EACvB,MAAMI,eAAe,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC/B,MAAMK,oBAAoB,GAAGL,GAAG,CAAC,aAAa,CAAC;EAC/C,MAAMM,kBAAkB,GAAGN,GAAG,CAAC,UAAU,CAAC;EAC1C,MAAMO,aAAa,GAAGP,GAAG,CAAC,KAAK,CAAC;EAChC,MAAMQ,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,cAAc,GAAGC,QAAQ,CAAC,MAAM;IACpC,IAAI,CAACb,WAAW,CAACc,MAAM,CAACC,KAAK,EAC3B,OAAO,EAAE;IACX,MAAMC,IAAI,GAAGhB,WAAW,CAACgB,IAAI,CAACD,KAAK,IAAI,EAAE;IACzC,OAAOE,SAAS,CAACD,IAAI,CAAC;EAC1B,CAAG,CAAC;EACF,MAAME,kBAAkB,GAAGL,QAAQ,CAAC,MAAM;IACxC,MAAMC,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACb,eAAe,CAACS,KAAK,CAAC;IAC/C,MAAMM,GAAG,GAAG,EAAE;IACd,IAAI,CAACF,IAAI,CAACG,MAAM,EACd,OAAOD,GAAG;IACZF,IAAI,CAACI,OAAO,CAAEC,GAAG,IAAK;MACpB,IAAIlB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,CAACF,MAAM,EAAE;QACrC,MAAMG,IAAI,GAAG;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAC7BpB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,CAACD,OAAO,CAAEI,GAAG,IAAK;UAC1C,MAAMC,aAAa,GAAGC,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;UACjDW,IAAI,CAACC,QAAQ,CAACI,IAAI,CAACF,aAAa,CAAC;UACjC,IAAID,GAAG,CAACpB,oBAAoB,CAACQ,KAAK,CAAC,IAAI,CAACM,GAAG,CAACO,aAAa,CAAC,EAAE;YAC1DP,GAAG,CAACO,aAAa,CAAC,GAAG;cAAEF,QAAQ,EAAE;YAAE,CAAE;UACjD;QACA,CAAS,CAAC;QACFL,GAAG,CAACG,GAAG,CAAC,GAAGC,IAAI;MACvB;IACA,CAAK,CAAC;IACF,OAAOJ,GAAG;EACd,CAAG,CAAC;EACF,MAAMJ,SAAS,GAAID,IAAI,IAAK;IAC1B,MAAMF,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMM,GAAG,GAAG,EAAE;IACdU,YAAY,CAACf,IAAI,EAAE,CAACgB,MAAM,EAAEN,QAAQ,EAAEO,KAAK,KAAK;MAC9C,MAAMC,QAAQ,GAAGL,cAAc,CAACG,MAAM,EAAElB,MAAM,CAAC;MAC/C,IAAIqB,OAAO,CAACT,QAAQ,CAAC,EAAE;QACrBL,GAAG,CAACa,QAAQ,CAAC,GAAG;UACdR,QAAQ,EAAEA,QAAQ,CAACU,GAAG,CAAET,GAAG,IAAKE,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC,CAAC;UAC5DmB;QACV,CAAS;MACT,CAAO,MAAM,IAAI5B,IAAI,CAACU,KAAK,EAAE;QACrBM,GAAG,CAACa,QAAQ,CAAC,GAAG;UACdR,QAAQ,EAAE,EAAE;UACZrB,IAAI,EAAE,IAAI;UACV4B;QACV,CAAS;MACT;IACA,CAAK,EAAEzB,kBAAkB,CAACO,KAAK,EAAER,oBAAoB,CAACQ,KAAK,CAAC;IACxD,OAAOM,GAAG;EACd,CAAG;EACD,MAAMgB,cAAc,GAAGA,CAACC,qBAAqB,GAAG,KAAK,EAAEC,WAAW,GAAG,CAAEC,EAAE,IAAK,CAACA,EAAE,GAAG9B,QAAQ,CAAC+B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,MAAM,CAACC,gBAAgB,CAAC5B,KAAK,GAAG,KAAK;IAC7J,IAAI6B,GAAG;IACP,MAAMC,MAAM,GAAGjC,cAAc,CAACG,KAAK;IACnC,MAAM+B,mBAAmB,GAAG5B,kBAAkB,CAACH,KAAK;IACpD,MAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC0B,MAAM,CAAC;IAChC,MAAME,WAAW,GAAG,EAAE;IACtB,IAAI5B,IAAI,CAACG,MAAM,EAAE;MACf,MAAM0B,WAAW,GAAGC,KAAK,CAAC9C,QAAQ,CAAC;MACnC,MAAM+C,eAAe,GAAG,EAAE;MAC1B,MAAMC,WAAW,GAAGA,CAACC,QAAQ,EAAE5B,GAAG,KAAK;QACrC,IAAIc,qBAAqB,EAAE;UACzB,IAAIrC,aAAa,CAACc,KAAK,EAAE;YACvB,OAAOwB,WAAW,IAAItC,aAAa,CAACc,KAAK,CAACsC,QAAQ,CAAC7B,GAAG,CAAC;UACnE,CAAW,MAAM;YACL,OAAO,CAAC,EAAEe,WAAW,KAAKa,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,CAAC,CAAC;UACrF;QACA,CAAS,MAAM;UACL,MAAMC,QAAQ,GAAGhB,WAAW,IAAItC,aAAa,CAACc,KAAK,IAAId,aAAa,CAACc,KAAK,CAACsC,QAAQ,CAAC7B,GAAG,CAAC;UACxF,OAAO,CAAC,EAAE,CAAC4B,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,KAAKC,QAAQ,CAAC;QAChF;MACA,CAAO;MACDpC,IAAI,CAACI,OAAO,CAAEC,GAAG,IAAK;QACpB,MAAM4B,QAAQ,GAAGJ,WAAW,CAACxB,GAAG,CAAC;QACjC,MAAMgC,QAAQ,GAAG;UAAE,GAAGX,MAAM,CAACrB,GAAG;QAAC,CAAE;QACnCgC,QAAQ,CAACF,QAAQ,GAAGH,WAAW,CAACC,QAAQ,EAAE5B,GAAG,CAAC;QAC9C,IAAIgC,QAAQ,CAACnD,IAAI,EAAE;UACjB,MAAM;YAAEoD,MAAM,GAAG,KAAK;YAAEC,OAAO,GAAG;UAAK,CAAE,GAAGN,QAAQ,IAAI,EAAE;UAC1DI,QAAQ,CAACC,MAAM,GAAG,CAAC,CAACA,MAAM;UAC1BD,QAAQ,CAACE,OAAO,GAAG,CAAC,CAACA,OAAO;UAC5BR,eAAe,CAACpB,IAAI,CAACN,GAAG,CAAC;QACnC;QACQuB,WAAW,CAACvB,GAAG,CAAC,GAAGgC,QAAQ;MACnC,CAAO,CAAC;MACF,MAAMG,QAAQ,GAAGvC,MAAM,CAACD,IAAI,CAAC2B,mBAAmB,CAAC;MACjD,IAAIzC,IAAI,CAACU,KAAK,IAAI4C,QAAQ,CAACrC,MAAM,IAAI4B,eAAe,CAAC5B,MAAM,EAAE;QAC3DqC,QAAQ,CAACpC,OAAO,CAAEC,GAAG,IAAK;UACxB,MAAM4B,QAAQ,GAAGJ,WAAW,CAACxB,GAAG,CAAC;UACjC,MAAMoC,gBAAgB,GAAGd,mBAAmB,CAACtB,GAAG,CAAC,CAACE,QAAQ;UAC1D,IAAIwB,eAAe,CAACG,QAAQ,CAAC7B,GAAG,CAAC,EAAE;YACjC,IAAIuB,WAAW,CAACvB,GAAG,CAAC,CAACE,QAAQ,CAACJ,MAAM,KAAK,CAAC,EAAE;cAC1C,MAAM,IAAIuC,KAAK,CAAC,2CAA2C,CAAC;YAC1E;YACYd,WAAW,CAACvB,GAAG,CAAC,CAACE,QAAQ,GAAGkC,gBAAgB;UACxD,CAAW,MAAM;YACL,MAAM;cAAEH,MAAM,GAAG,KAAK;cAAEC,OAAO,GAAG;YAAK,CAAE,GAAGN,QAAQ,IAAI,EAAE;YAC1DL,WAAW,CAACvB,GAAG,CAAC,GAAG;cACjBnB,IAAI,EAAE,IAAI;cACVoD,MAAM,EAAE,CAAC,CAACA,MAAM;cAChBC,OAAO,EAAE,CAAC,CAACA,OAAO;cAClBJ,QAAQ,EAAEH,WAAW,CAACC,QAAQ,EAAE5B,GAAG,CAAC;cACpCE,QAAQ,EAAEkC,gBAAgB;cAC1B3B,KAAK,EAAE;YACrB,CAAa;UACb;QACA,CAAS,CAAC;MACV;IACA;IACI9B,QAAQ,CAACY,KAAK,GAAGgC,WAAW;IAC5B,CAACH,GAAG,GAAGlC,QAAQ,CAAC+B,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,GAAG,CAACkB,kBAAkB,EAAE;EACtE,CAAG;EACDC,KAAK,CAAC,MAAM9D,aAAa,CAACc,KAAK,EAAE,MAAM;IACrCsB,cAAc,CAAC,IAAI,CAAC;EACxB,CAAG,CAAC;EACF0B,KAAK,CAAC,MAAMnD,cAAc,CAACG,KAAK,EAAE,MAAM;IACtCsB,cAAc,EAAE;EACpB,CAAG,CAAC;EACF0B,KAAK,CAAC,MAAM7C,kBAAkB,CAACH,KAAK,EAAE,MAAM;IAC1CsB,cAAc,EAAE;EACpB,CAAG,CAAC;EACF,MAAM2B,oBAAoB,GAAIjD,KAAK,IAAK;IACtCd,aAAa,CAACc,KAAK,GAAGA,KAAK;IAC3BsB,cAAc,EAAE;EACpB,CAAG;EACD,MAAM4B,SAAS,GAAIjD,IAAI,IAAK;IAC1B,OAAOX,IAAI,CAACU,KAAK,IAAIC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,CAACA,IAAI,CAACyC,MAAM;EACjE,CAAG;EACD,MAAMS,mBAAmB,GAAGA,CAACvC,GAAG,EAAE2B,QAAQ,KAAK;IAC7C5C,QAAQ,CAAC+B,KAAK,CAAC0B,YAAY,EAAE;IAC7B,MAAMrD,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMqD,EAAE,GAAGvC,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,MAAME,IAAI,GAAGoD,EAAE,IAAIjE,QAAQ,CAACY,KAAK,CAACqD,EAAE,CAAC;IACrC,IAAIA,EAAE,IAAIpD,IAAI,IAAI,UAAU,IAAIA,IAAI,EAAE;MACpC,MAAMqD,WAAW,GAAGrD,IAAI,CAACsC,QAAQ;MACjCA,QAAQ,GAAGgB,WAAW,CAAChB,QAAQ,CAAC,GAAG,CAACtC,IAAI,CAACsC,QAAQ,GAAGA,QAAQ;MAC5DnD,QAAQ,CAACY,KAAK,CAACqD,EAAE,CAAC,CAACd,QAAQ,GAAGA,QAAQ;MACtC,IAAIe,WAAW,KAAKf,QAAQ,EAAE;QAC5B5C,QAAQ,CAAC6D,IAAI,CAAC,eAAe,EAAE5C,GAAG,EAAE2B,QAAQ,CAAC;MACrD;MACMW,SAAS,CAACjD,IAAI,CAAC,IAAIwD,QAAQ,CAAC7C,GAAG,EAAEyC,EAAE,EAAEpD,IAAI,CAAC;MAC1CN,QAAQ,CAAC+B,KAAK,CAACqB,kBAAkB,EAAE;IACzC;EACA,CAAG;EACD,MAAMW,YAAY,GAAI9C,GAAG,IAAK;IAC5BjB,QAAQ,CAAC+B,KAAK,CAAC0B,YAAY,EAAE;IAC7B,MAAMrD,MAAM,GAAGd,WAAW,CAACc,MAAM,CAACC,KAAK;IACvC,MAAMqD,EAAE,GAAGvC,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,MAAME,IAAI,GAAGb,QAAQ,CAACY,KAAK,CAACqD,EAAE,CAAC;IAC/B,IAAIH,SAAS,CAACjD,IAAI,CAAC,EAAE;MACnBwD,QAAQ,CAAC7C,GAAG,EAAEyC,EAAE,EAAEpD,IAAI,CAAC;IAC7B,CAAK,MAAM;MACLkD,mBAAmB,CAACvC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtC;EACA,CAAG;EACD,MAAM6C,QAAQ,GAAGA,CAAC7C,GAAG,EAAEH,GAAG,EAAEkD,QAAQ,KAAK;IACvC,MAAM;MAAEC;IAAI,CAAE,GAAGjE,QAAQ,CAACkE,KAAK;IAC/B,IAAID,IAAI,IAAI,CAACxE,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACiC,MAAM,EAAE;MACvCtD,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACkC,OAAO,GAAG,IAAI;MAClCiB,IAAI,CAAChD,GAAG,EAAE+C,QAAQ,EAAG1D,IAAI,IAAK;QAC5B,IAAI,CAACmB,OAAO,CAACnB,IAAI,CAAC,EAAE;UAClB,MAAM,IAAI6D,SAAS,CAAC,iCAAiC,CAAC;QAChE;QACQ1E,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACkC,OAAO,GAAG,KAAK;QACnCvD,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAACiC,MAAM,GAAG,IAAI;QACjCtD,QAAQ,CAACY,KAAK,CAACS,GAAG,CAAC,CAAC8B,QAAQ,GAAG,IAAI;QACnC,IAAItC,IAAI,CAACM,MAAM,EAAE;UACfhB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,GAAGR,IAAI;QAC3C;QACQN,QAAQ,CAAC6D,IAAI,CAAC,eAAe,EAAE5C,GAAG,EAAE,IAAI,CAAC;MACjD,CAAO,CAAC;IACR;EACA,CAAG;EACD,MAAMmD,iBAAiB,GAAGA,CAACtD,GAAG,EAAER,IAAI,KAAK;IACvC,MAAM;MAAEX,IAAI,EAAE0E,KAAK;MAAEjE;IAAM,CAAE,GAAGJ,QAAQ,CAACkE,KAAK;IAC9C,IAAI,CAACG,KAAK,EACR;IACF,IAAI,CAACjE,MAAM,EACT,MAAM,IAAI+C,KAAK,CAAC,8CAA8C,CAAC;IACjE,IAAIvD,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,EAAE;MAC9BlB,eAAe,CAACS,KAAK,CAACS,GAAG,CAAC,GAAGR,IAAI;IACvC;EACA,CAAG;EACD,OAAO;IACLwD,QAAQ;IACRC,YAAY;IACZP,mBAAmB;IACnBF,oBAAoB;IACpB3B,cAAc;IACdyC,iBAAiB;IACjB7D,SAAS;IACTyB,MAAM,EAAE;MACNzC,aAAa;MACbE,QAAQ;MACRC,MAAM;MACNC,IAAI;MACJC,eAAe;MACfC,oBAAoB;MACpBC,kBAAkB;MAClBC;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
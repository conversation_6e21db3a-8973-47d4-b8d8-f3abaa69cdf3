{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport unzip from './unzip.js';\n\n/**\n * This method is like `_.unzip` except that it accepts `iteratee` to specify\n * how regrouped values should be combined. The iteratee is invoked with the\n * elements of each group: (...group).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Array\n * @param {Array} array The array of grouped elements to process.\n * @param {Function} [iteratee=_.identity] The function to combine\n *  regrouped values.\n * @returns {Array} Returns the new array of regrouped elements.\n * @example\n *\n * var zipped = _.zip([1, 2], [10, 20], [100, 200]);\n * // => [[1, 10, 100], [2, 20, 200]]\n *\n * _.unzipWith(zipped, _.add);\n * // => [3, 30, 300]\n */\nfunction unzipWith(array, iteratee) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  var result = unzip(array);\n  if (iteratee == null) {\n    return result;\n  }\n  return arrayMap(result, function (group) {\n    return apply(iteratee, undefined, group);\n  });\n}\nexport default unzipWith;", "map": {"version": 3, "names": ["apply", "arrayMap", "unzip", "unzipWith", "array", "iteratee", "length", "result", "group", "undefined"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/unzipWith.js"], "sourcesContent": ["import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport unzip from './unzip.js';\n\n/**\n * This method is like `_.unzip` except that it accepts `iteratee` to specify\n * how regrouped values should be combined. The iteratee is invoked with the\n * elements of each group: (...group).\n *\n * @static\n * @memberOf _\n * @since 3.8.0\n * @category Array\n * @param {Array} array The array of grouped elements to process.\n * @param {Function} [iteratee=_.identity] The function to combine\n *  regrouped values.\n * @returns {Array} Returns the new array of regrouped elements.\n * @example\n *\n * var zipped = _.zip([1, 2], [10, 20], [100, 200]);\n * // => [[1, 10, 100], [2, 20, 200]]\n *\n * _.unzipWith(zipped, _.add);\n * // => [3, 30, 300]\n */\nfunction unzipWith(array, iteratee) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  var result = unzip(array);\n  if (iteratee == null) {\n    return result;\n  }\n  return arrayMap(result, function(group) {\n    return apply(iteratee, undefined, group);\n  });\n}\n\nexport default unzipWith;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,YAAY;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAClC,IAAI,EAAED,KAAK,IAAIA,KAAK,CAACE,MAAM,CAAC,EAAE;IAC5B,OAAO,EAAE;EACX;EACA,IAAIC,MAAM,GAAGL,KAAK,CAACE,KAAK,CAAC;EACzB,IAAIC,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAOE,MAAM;EACf;EACA,OAAON,QAAQ,CAACM,MAAM,EAAE,UAASC,KAAK,EAAE;IACtC,OAAOR,KAAK,CAACK,QAAQ,EAAEI,SAAS,EAAED,KAAK,CAAC;EAC1C,CAAC,CAAC;AACJ;AAEA,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
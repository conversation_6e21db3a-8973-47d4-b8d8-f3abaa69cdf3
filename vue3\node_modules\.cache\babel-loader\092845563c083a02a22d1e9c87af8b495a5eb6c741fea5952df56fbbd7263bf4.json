{"ast": null, "code": "import { inject, ref, h } from 'vue';\nimport { debounce } from 'lodash-unified';\nimport { getCell, getColumnByCell, createTablePopper, removePopper } from '../util.mjs';\nimport { TABLE_INJECTION_KEY } from '../tokens.mjs';\nimport { hasClass, addClass, removeClass } from '../../../../utils/dom/style.mjs';\nfunction isGreaterThan(a, b, epsilon = 0.03) {\n  return a - b > epsilon;\n}\nfunction useEvents(props) {\n  const parent = inject(TABLE_INJECTION_KEY);\n  const tooltipContent = ref(\"\");\n  const tooltipTrigger = ref(h(\"div\"));\n  const handleEvent = (event, row, name) => {\n    var _a;\n    const table = parent;\n    const cell = getCell(event);\n    let column;\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    if (cell) {\n      column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (column) {\n        table == null ? void 0 : table.emit(`cell-${name}`, row, column, cell, event);\n      }\n    }\n    table == null ? void 0 : table.emit(`row-${name}`, row, column, event);\n  };\n  const handleDoubleClick = (event, row) => {\n    handleEvent(event, row, \"dblclick\");\n  };\n  const handleClick = (event, row) => {\n    props.store.commit(\"setCurrentRow\", row);\n    handleEvent(event, row, \"click\");\n  };\n  const handleContextMenu = (event, row) => {\n    handleEvent(event, row, \"contextmenu\");\n  };\n  const handleMouseEnter = debounce(index => {\n    props.store.commit(\"setHoverRow\", index);\n  }, 30);\n  const handleMouseLeave = debounce(() => {\n    props.store.commit(\"setHoverRow\", null);\n  }, 30);\n  const getPadding = el => {\n    const style = window.getComputedStyle(el, null);\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0;\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0;\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0;\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0;\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom\n    };\n  };\n  const toggleRowClassByCell = (rowSpan, event, toggle) => {\n    let node = event.target.parentNode;\n    while (rowSpan > 1) {\n      node = node == null ? void 0 : node.nextSibling;\n      if (!node || node.nodeName !== \"TR\") break;\n      toggle(node, \"hover-row hover-fixed-row\");\n      rowSpan--;\n    }\n  };\n  const handleCellMouseEnter = (event, row, tooltipOptions) => {\n    var _a, _b, _c;\n    const table = parent;\n    const cell = getCell(event);\n    const namespace = (_a = table == null ? void 0 : table.vnode.el) == null ? void 0 : _a.dataset.prefix;\n    let column;\n    if (cell) {\n      column = getColumnByCell({\n        columns: props.store.states.columns.value\n      }, cell, namespace);\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass);\n      }\n      const hoverState = table.hoverState = {\n        cell,\n        column,\n        row\n      };\n      table == null ? void 0 : table.emit(\"cell-mouse-enter\", hoverState.row, hoverState.column, hoverState.cell, event);\n    }\n    if (!tooltipOptions) {\n      return;\n    }\n    const cellChild = event.target.querySelector(\".cell\");\n    if (!(hasClass(cellChild, `${namespace}-tooltip`) && cellChild.childNodes.length)) {\n      return;\n    }\n    const range = document.createRange();\n    range.setStart(cellChild, 0);\n    range.setEnd(cellChild, cellChild.childNodes.length);\n    const {\n      width: rangeWidth,\n      height: rangeHeight\n    } = range.getBoundingClientRect();\n    const {\n      width: cellChildWidth,\n      height: cellChildHeight\n    } = cellChild.getBoundingClientRect();\n    const {\n      top,\n      left,\n      right,\n      bottom\n    } = getPadding(cellChild);\n    const horizontalPadding = left + right;\n    const verticalPadding = top + bottom;\n    if (isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) || isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) || isGreaterThan(cellChild.scrollWidth, cellChildWidth)) {\n      createTablePopper(tooltipOptions, cell.innerText || cell.textContent, row, column, cell, table);\n    } else if (((_b = removePopper) == null ? void 0 : _b.trigger) === cell) {\n      (_c = removePopper) == null ? void 0 : _c();\n    }\n  };\n  const handleCellMouseLeave = event => {\n    const cell = getCell(event);\n    if (!cell) return;\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass);\n    }\n    const oldHoverState = parent == null ? void 0 : parent.hoverState;\n    parent == null ? void 0 : parent.emit(\"cell-mouse-leave\", oldHoverState == null ? void 0 : oldHoverState.row, oldHoverState == null ? void 0 : oldHoverState.column, oldHoverState == null ? void 0 : oldHoverState.cell, event);\n  };\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger\n  };\n}\nexport { useEvents as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "b", "epsilon", "useEvents", "props", "parent", "inject", "TABLE_INJECTION_KEY", "tooltipContent", "ref", "tooltipTrigger", "h", "handleEvent", "event", "row", "name", "_a", "table", "cell", "getCell", "column", "namespace", "vnode", "el", "dataset", "prefix", "getColumnByCell", "columns", "store", "states", "value", "emit", "handleDoubleClick", "handleClick", "commit", "handleContextMenu", "handleMouseEnter", "debounce", "index", "handleMouseLeave", "getPadding", "style", "window", "getComputedStyle", "paddingLeft", "Number", "parseInt", "paddingRight", "paddingTop", "paddingBottom", "left", "right", "top", "bottom", "toggleRowClassByCell", "rowSpan", "toggle", "node", "target", "parentNode", "nextS<PERSON>ling", "nodeName", "handleCellMouseEnter", "tooltipOptions", "_b", "_c", "addClass", "hoverState", "cellChild", "querySelector", "hasClass", "childNodes", "length", "range", "document", "createRange", "setStart", "setEnd", "width", "rangeWidth", "height", "rangeHeight", "getBoundingClientRect", "cell<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "cellChildHeight", "horizontalPadding", "verticalPadding", "scrollWidth", "createTablePopper", "innerText", "textContent", "removePopper", "trigger", "handleCellMouseLeave", "removeClass", "oldHoverState"], "sources": ["../../../../../../../packages/components/table/src/table-body/events-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { h, inject, ref } from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { addClass, hasClass, removeClass } from '@element-plus/utils'\nimport {\n  createTablePopper,\n  getCell,\n  getColumnByCell,\n  removePopper,\n} from '../util'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableBodyProps } from './defaults'\nimport type { TableOverflowTooltipOptions } from '../util'\n\nfunction isGreaterThan(a: number, b: number, epsilon = 0.03) {\n  return a - b > epsilon\n}\n\nfunction useEvents<T>(props: Partial<TableBodyProps<T>>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const tooltipContent = ref('')\n  const tooltipTrigger = ref(h('div'))\n  const handleEvent = (event: Event, row: T, name: string) => {\n    const table = parent\n    const cell = getCell(event)\n    let column: TableColumnCtx<T>\n    const namespace = table?.vnode.el?.dataset.prefix\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      if (column) {\n        table?.emit(`cell-${name}`, row, column, cell, event)\n      }\n    }\n    table?.emit(`row-${name}`, row, column, event)\n  }\n  const handleDoubleClick = (event: Event, row: T) => {\n    handleEvent(event, row, 'dblclick')\n  }\n  const handleClick = (event: Event, row: T) => {\n    props.store.commit('setCurrentRow', row)\n    handleEvent(event, row, 'click')\n  }\n  const handleContextMenu = (event: Event, row: T) => {\n    handleEvent(event, row, 'contextmenu')\n  }\n  const handleMouseEnter = debounce((index: number) => {\n    props.store.commit('setHoverRow', index)\n  }, 30)\n  const handleMouseLeave = debounce(() => {\n    props.store.commit('setHoverRow', null)\n  }, 30)\n  const getPadding = (el: HTMLElement) => {\n    const style = window.getComputedStyle(el, null)\n    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0\n    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0\n    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0\n    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0\n    return {\n      left: paddingLeft,\n      right: paddingRight,\n      top: paddingTop,\n      bottom: paddingBottom,\n    }\n  }\n\n  const toggleRowClassByCell = (\n    rowSpan: number,\n    event: MouseEvent,\n    toggle: (el: Element, cls: string) => void\n  ) => {\n    let node = event.target.parentNode\n    while (rowSpan > 1) {\n      node = node?.nextSibling\n      if (!node || node.nodeName !== 'TR') break\n      toggle(node, 'hover-row hover-fixed-row')\n      rowSpan--\n    }\n  }\n\n  const handleCellMouseEnter = (\n    event: MouseEvent,\n    row: T,\n    tooltipOptions: TableOverflowTooltipOptions\n  ) => {\n    const table = parent\n    const cell = getCell(event)\n    const namespace = table?.vnode.el?.dataset.prefix\n    let column: TableColumnCtx<T>\n    if (cell) {\n      column = getColumnByCell(\n        {\n          columns: props.store.states.columns.value,\n        },\n        cell,\n        namespace\n      )\n      if (cell.rowSpan > 1) {\n        toggleRowClassByCell(cell.rowSpan, event, addClass)\n      }\n      const hoverState = (table.hoverState = { cell, column, row })\n      table?.emit(\n        'cell-mouse-enter',\n        hoverState.row,\n        hoverState.column,\n        hoverState.cell,\n        event\n      )\n    }\n\n    if (!tooltipOptions) {\n      return\n    }\n\n    // 判断是否text-overflow, 如果是就显示tooltip\n    const cellChild = (event.target as HTMLElement).querySelector(\n      '.cell'\n    ) as HTMLElement\n    if (\n      !(\n        hasClass(cellChild, `${namespace}-tooltip`) &&\n        cellChild.childNodes.length\n      )\n    ) {\n      return\n    }\n    // use range width instead of scrollWidth to determine whether the text is overflowing\n    // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3\n    const range = document.createRange()\n    range.setStart(cellChild, 0)\n    range.setEnd(cellChild, cellChild.childNodes.length)\n    /** detail: https://github.com/element-plus/element-plus/issues/10790\n     *  What went wrong?\n     *  UI > Browser > Zoom, In Blink/WebKit, getBoundingClientRect() sometimes returns inexact values, probably due to lost precision during internal calculations. In the example above:\n     *    - Expected: 188\n     *    - Actual: 188.00000762939453\n     */\n    const { width: rangeWidth, height: rangeHeight } =\n      range.getBoundingClientRect()\n    const { width: cellChildWidth, height: cellChildHeight } =\n      cellChild.getBoundingClientRect()\n\n    const { top, left, right, bottom } = getPadding(cellChild)\n    const horizontalPadding = left + right\n    const verticalPadding = top + bottom\n    if (\n      isGreaterThan(rangeWidth + horizontalPadding, cellChildWidth) ||\n      isGreaterThan(rangeHeight + verticalPadding, cellChildHeight) ||\n      // When using a high-resolution screen, it is possible that a returns cellChild.scrollWidth value of 1921 and\n      // cellChildWidth returns a value of 1920.994140625. #16856 #16673\n      isGreaterThan(cellChild.scrollWidth, cellChildWidth)\n    ) {\n      createTablePopper(\n        tooltipOptions,\n        cell.innerText || cell.textContent,\n        row,\n        column,\n        cell,\n        table\n      )\n    } else if (removePopper?.trigger === cell) {\n      removePopper?.()\n    }\n  }\n  const handleCellMouseLeave = (event) => {\n    const cell = getCell(event)\n    if (!cell) return\n    if (cell.rowSpan > 1) {\n      toggleRowClassByCell(cell.rowSpan, event, removeClass)\n    }\n    const oldHoverState = parent?.hoverState\n    parent?.emit(\n      'cell-mouse-leave',\n      oldHoverState?.row,\n      oldHoverState?.column,\n      oldHoverState?.cell,\n      event\n    )\n  }\n\n  return {\n    handleDoubleClick,\n    handleClick,\n    handleContextMenu,\n    handleMouseEnter,\n    handleMouseLeave,\n    handleCellMouseEnter,\n    handleCellMouseLeave,\n    tooltipContent,\n    tooltipTrigger,\n  }\n}\n\nexport default useEvents\n"], "mappings": ";;;;;AAUA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAEC,OAAO,GAAG,IAAI,EAAE;EAC3C,OAAOF,CAAC,GAAGC,CAAC,GAAGC,OAAO;AACxB;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,MAAMC,MAAM,GAAGC,MAAM,CAACC,mBAAmB,CAAC;EAC1C,MAAMC,cAAc,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC9B,MAAMC,cAAc,GAAGD,GAAG,CAACE,CAAC,CAAC,KAAK,CAAC,CAAC;EACpC,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,KAAK;IACxC,IAAIC,EAAE;IACN,MAAMC,KAAK,GAAGZ,MAAM;IACpB,MAAMa,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,IAAIO,MAAM;IACV,MAAMC,SAAS,GAAG,CAACL,EAAE,GAAGC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACQ,OAAO,CAACC,MAAM;IACrG,IAAIP,IAAI,EAAE;MACRE,MAAM,GAAGM,eAAe,CAAC;QACvBC,OAAO,EAAEvB,KAAK,CAACwB,KAAK,CAACC,MAAM,CAACF,OAAO,CAACG;MAC5C,CAAO,EAAEZ,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAID,MAAM,EAAE;QACVH,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAE,QAAOhB,IAAK,EAAC,EAAED,GAAG,EAAEM,MAAM,EAAEF,IAAI,EAAEL,KAAK,CAAC;MACrF;IACA;IACII,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAE,OAAMhB,IAAK,EAAC,EAAED,GAAG,EAAEM,MAAM,EAAEP,KAAK,CAAC;EAC1E,CAAG;EACD,MAAMmB,iBAAiB,GAAGA,CAACnB,KAAK,EAAEC,GAAG,KAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,UAAU,CAAC;EACvC,CAAG;EACD,MAAMmB,WAAW,GAAGA,CAACpB,KAAK,EAAEC,GAAG,KAAK;IAClCV,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,eAAe,EAAEpB,GAAG,CAAC;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,OAAO,CAAC;EACpC,CAAG;EACD,MAAMqB,iBAAiB,GAAGA,CAACtB,KAAK,EAAEC,GAAG,KAAK;IACxCF,WAAW,CAACC,KAAK,EAAEC,GAAG,EAAE,aAAa,CAAC;EAC1C,CAAG;EACD,MAAMsB,gBAAgB,GAAGC,QAAQ,CAAEC,KAAK,IAAK;IAC3ClC,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,aAAa,EAAEI,KAAK,CAAC;EAC5C,CAAG,EAAE,EAAE,CAAC;EACN,MAAMC,gBAAgB,GAAGF,QAAQ,CAAC,MAAM;IACtCjC,KAAK,CAACwB,KAAK,CAACM,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC;EAC3C,CAAG,EAAE,EAAE,CAAC;EACN,MAAMM,UAAU,GAAIjB,EAAE,IAAK;IACzB,MAAMkB,KAAK,GAAGC,MAAM,CAACC,gBAAgB,CAACpB,EAAE,EAAE,IAAI,CAAC;IAC/C,MAAMqB,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACG,WAAW,EAAE,EAAE,CAAC,IAAI,CAAC;IAC/D,MAAMG,YAAY,GAAGF,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACM,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;IACjE,MAAMC,UAAU,GAAGH,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACO,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC;IAC7D,MAAMC,aAAa,GAAGJ,MAAM,CAACC,QAAQ,CAACL,KAAK,CAACQ,aAAa,EAAE,EAAE,CAAC,IAAI,CAAC;IACnE,OAAO;MACLC,IAAI,EAAEN,WAAW;MACjBO,KAAK,EAAEJ,YAAY;MACnBK,GAAG,EAAEJ,UAAU;MACfK,MAAM,EAAEJ;IACd,CAAK;EACL,CAAG;EACD,MAAMK,oBAAoB,GAAGA,CAACC,OAAO,EAAE1C,KAAK,EAAE2C,MAAM,KAAK;IACvD,IAAIC,IAAI,GAAG5C,KAAK,CAAC6C,MAAM,CAACC,UAAU;IAClC,OAAOJ,OAAO,GAAG,CAAC,EAAE;MAClBE,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACG,WAAW;MAC/C,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAK,IAAI,EACjC;MACFL,MAAM,CAACC,IAAI,EAAE,2BAA2B,CAAC;MACzCF,OAAO,EAAE;IACf;EACA,CAAG;EACD,MAAMO,oBAAoB,GAAGA,CAACjD,KAAK,EAAEC,GAAG,EAAEiD,cAAc,KAAK;IAC3D,IAAI/C,EAAE,EAAEgD,EAAE,EAAEC,EAAE;IACd,MAAMhD,KAAK,GAAGZ,MAAM;IACpB,MAAMa,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,MAAMQ,SAAS,GAAG,CAACL,EAAE,GAAGC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,KAAK,CAACC,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,EAAE,CAACQ,OAAO,CAACC,MAAM;IACrG,IAAIL,MAAM;IACV,IAAIF,IAAI,EAAE;MACRE,MAAM,GAAGM,eAAe,CAAC;QACvBC,OAAO,EAAEvB,KAAK,CAACwB,KAAK,CAACC,MAAM,CAACF,OAAO,CAACG;MAC5C,CAAO,EAAEZ,IAAI,EAAEG,SAAS,CAAC;MACnB,IAAIH,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;QACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE1C,KAAK,EAAEqD,QAAQ,CAAC;MAC3D;MACM,MAAMC,UAAU,GAAGlD,KAAK,CAACkD,UAAU,GAAG;QAAEjD,IAAI;QAAEE,MAAM;QAAEN;MAAG,CAAE;MAC3DG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACc,IAAI,CAAC,kBAAkB,EAAEoC,UAAU,CAACrD,GAAG,EAAEqD,UAAU,CAAC/C,MAAM,EAAE+C,UAAU,CAACjD,IAAI,EAAEL,KAAK,CAAC;IACxH;IACI,IAAI,CAACkD,cAAc,EAAE;MACnB;IACN;IACI,MAAMK,SAAS,GAAGvD,KAAK,CAAC6C,MAAM,CAACW,aAAa,CAAC,OAAO,CAAC;IACrD,IAAI,EAAEC,QAAQ,CAACF,SAAS,EAAG,GAAE/C,SAAU,UAAS,CAAC,IAAI+C,SAAS,CAACG,UAAU,CAACC,MAAM,CAAC,EAAE;MACjF;IACN;IACI,MAAMC,KAAK,GAAGC,QAAQ,CAACC,WAAW,EAAE;IACpCF,KAAK,CAACG,QAAQ,CAACR,SAAS,EAAE,CAAC,CAAC;IAC5BK,KAAK,CAACI,MAAM,CAACT,SAAS,EAAEA,SAAS,CAACG,UAAU,CAACC,MAAM,CAAC;IACpD,MAAM;MAAEM,KAAK,EAAEC,UAAU;MAAEC,MAAM,EAAEC;IAAW,CAAE,GAAGR,KAAK,CAACS,qBAAqB,EAAE;IAChF,MAAM;MAAEJ,KAAK,EAAEK,cAAc;MAAEH,MAAM,EAAEI;IAAe,CAAE,GAAGhB,SAAS,CAACc,qBAAqB,EAAE;IAC5F,MAAM;MAAE9B,GAAG;MAAEF,IAAI;MAAEC,KAAK;MAAEE;IAAM,CAAE,GAAGb,UAAU,CAAC4B,SAAS,CAAC;IAC1D,MAAMiB,iBAAiB,GAAGnC,IAAI,GAAGC,KAAK;IACtC,MAAMmC,eAAe,GAAGlC,GAAG,GAAGC,MAAM;IACpC,IAAItD,aAAa,CAACgF,UAAU,GAAGM,iBAAiB,EAAEF,cAAc,CAAC,IAAIpF,aAAa,CAACkF,WAAW,GAAGK,eAAe,EAAEF,eAAe,CAAC,IAAIrF,aAAa,CAACqE,SAAS,CAACmB,WAAW,EAAEJ,cAAc,CAAC,EAAE;MAC1LK,iBAAiB,CAACzB,cAAc,EAAE7C,IAAI,CAACuE,SAAS,IAAIvE,IAAI,CAACwE,WAAW,EAAE5E,GAAG,EAAEM,MAAM,EAAEF,IAAI,EAAED,KAAK,CAAC;IACrG,CAAK,MAAM,IAAI,CAAC,CAAC+C,EAAE,GAAG2B,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3B,EAAE,CAAC4B,OAAO,MAAM1E,IAAI,EAAE;MACvE,CAAC+C,EAAE,GAAG0B,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1B,EAAE,EAAE;IACjD;EACA,CAAG;EACD,MAAM4B,oBAAoB,GAAIhF,KAAK,IAAK;IACtC,MAAMK,IAAI,GAAGC,OAAO,CAACN,KAAK,CAAC;IAC3B,IAAI,CAACK,IAAI,EACP;IACF,IAAIA,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE;MACpBD,oBAAoB,CAACpC,IAAI,CAACqC,OAAO,EAAE1C,KAAK,EAAEiF,WAAW,CAAC;IAC5D;IACI,MAAMC,aAAa,GAAG1F,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC8D,UAAU;IACjE9D,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC0B,IAAI,CAAC,kBAAkB,EAAEgE,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACjF,GAAG,EAAEiF,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC3E,MAAM,EAAE2E,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC7E,IAAI,EAAEL,KAAK,CAAC;EACpO,CAAG;EACD,OAAO;IACLmB,iBAAiB;IACjBC,WAAW;IACXE,iBAAiB;IACjBC,gBAAgB;IAChBG,gBAAgB;IAChBuB,oBAAoB;IACpB+B,oBAAoB;IACpBrF,cAAc;IACdE;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
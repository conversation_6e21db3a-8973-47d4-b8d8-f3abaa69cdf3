{"ast": null, "code": "import castFunction from './_castFunction.js';\nimport partial from './partial.js';\n\n/**\n * Creates a function that provides `value` to `wrapper` as its first\n * argument. Any additional arguments provided to the function are appended\n * to those provided to the `wrapper`. The wrapper is invoked with the `this`\n * binding of the created function.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {*} value The value to wrap.\n * @param {Function} [wrapper=identity] The wrapper function.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var p = _.wrap(_.escape, function(func, text) {\n *   return '<p>' + func(text) + '</p>';\n * });\n *\n * p('fred, barney, & pebbles');\n * // => '<p>fred, barney, &amp; pebbles</p>'\n */\nfunction wrap(value, wrapper) {\n  return partial(castFunction(wrapper), value);\n}\nexport default wrap;", "map": {"version": 3, "names": ["castFunction", "partial", "wrap", "value", "wrapper"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/wrap.js"], "sourcesContent": ["import castFunction from './_castFunction.js';\nimport partial from './partial.js';\n\n/**\n * Creates a function that provides `value` to `wrapper` as its first\n * argument. Any additional arguments provided to the function are appended\n * to those provided to the `wrapper`. The wrapper is invoked with the `this`\n * binding of the created function.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {*} value The value to wrap.\n * @param {Function} [wrapper=identity] The wrapper function.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var p = _.wrap(_.escape, function(func, text) {\n *   return '<p>' + func(text) + '</p>';\n * });\n *\n * p('fred, barney, & pebbles');\n * // => '<p>fred, barney, &amp; pebbles</p>'\n */\nfunction wrap(value, wrapper) {\n  return partial(castFunction(wrapper), value);\n}\n\nexport default wrap;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC5B,OAAOH,OAAO,CAACD,YAAY,CAACI,OAAO,CAAC,EAAED,KAAK,CAAC;AAC9C;AAEA,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
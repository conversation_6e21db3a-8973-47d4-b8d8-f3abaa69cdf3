{"ast": null, "code": "/** Used to match wrap detail comments. */\nvar reWrapDetails = /\\{\\n\\/\\* \\[wrapped with (.+)\\] \\*/,\n  reSplitDetails = /,? & /;\n\n/**\n * Extracts wrapper details from the `source` body comment.\n *\n * @private\n * @param {string} source The source to inspect.\n * @returns {Array} Returns the wrapper details.\n */\nfunction getWrapDetails(source) {\n  var match = source.match(reWrapDetails);\n  return match ? match[1].split(reSplitDetails) : [];\n}\nexport default getWrapDetails;", "map": {"version": 3, "names": ["reWrapDetails", "reSplitDetails", "getWrapDetails", "source", "match", "split"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_getWrapDetails.js"], "sourcesContent": ["/** Used to match wrap detail comments. */\nvar reWrapDetails = /\\{\\n\\/\\* \\[wrapped with (.+)\\] \\*/,\n    reSplitDetails = /,? & /;\n\n/**\n * Extracts wrapper details from the `source` body comment.\n *\n * @private\n * @param {string} source The source to inspect.\n * @returns {Array} Returns the wrapper details.\n */\nfunction getWrapDetails(source) {\n  var match = source.match(reWrapDetails);\n  return match ? match[1].split(reSplitDetails) : [];\n}\n\nexport default getWrapDetails;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,mCAAmC;EACnDC,cAAc,GAAG,OAAO;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,MAAM,EAAE;EAC9B,IAAIC,KAAK,GAAGD,MAAM,CAACC,KAAK,CAACJ,aAAa,CAAC;EACvC,OAAOI,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAACJ,cAAc,CAAC,GAAG,EAAE;AACpD;AAEA,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
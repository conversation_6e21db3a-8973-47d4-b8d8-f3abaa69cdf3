{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst basicDateTableProps = buildProps({\n  ...datePickerSharedProps,\n  cellClassName: {\n    type: definePropType(Function)\n  },\n  showWeekNumber: Boolean,\n  selectionMode: selectionModeWithDefault(\"date\")\n});\nconst basicDateTableEmits = [\"changerange\", \"pick\", \"select\"];\nexport { basicDateTableEmits, basicDateTableProps };", "map": {"version": 3, "names": ["basicDateTableProps", "buildProps", "datePickerSharedProps", "cellClassName", "type", "definePropType", "Function", "showWeekNumber", "Boolean", "selectionMode", "selectionModeWithDefault", "basicDateTableEmits"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-date-table.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const basicDateTableProps = buildProps({\n  ...datePickerSharedProps,\n  cellClassName: {\n    type: definePropType<(date: Date) => string>(Function),\n  },\n  showWeekNumber: Boolean,\n  selectionMode: selectionModeWithDefault('date'),\n} as const)\n\nexport const basicDateTableEmits = ['changerange', 'pick', 'select']\n\nexport type BasicDateTableProps = ExtractPropTypes<typeof basicDateTableProps>\nexport type BasicDateTableEmits = typeof basicDateTableEmits\n\nexport type RangePickerEmits = { minDate: Dayjs; maxDate: null }\nexport type DatePickerEmits = Dayjs\nexport type DatesPickerEmits = Dayjs[]\nexport type MonthsPickerEmits = Dayjs[]\nexport type YearsPickerEmits = Dayjs[]\nexport type WeekPickerEmits = {\n  year: number\n  week: number\n  value: string\n  date: Dayjs\n}\n\nexport type DateTableEmits =\n  | RangePickerEmits\n  | DatePickerEmits\n  | DatesPickerEmits\n  | WeekPickerEmits\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5C,GAAGC,qBAAqB;EACxBC,aAAa,EAAE;IACbC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,cAAc,EAAEC,OAAO;EACvBC,aAAa,EAAEC,wBAAwB,CAAC,MAAM;AAChD,CAAC;AACW,MAACC,mBAAmB,GAAG,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
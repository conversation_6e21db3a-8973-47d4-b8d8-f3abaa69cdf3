{"ast": null, "code": "import ScanOnWeb from './components/ScanOnWebNew.vue';\nexport default {\n  name: 'App',\n  components: {\n    ScanOnWeb\n  }\n};", "map": {"version": 3, "names": ["ScanOnWeb", "name", "components"], "sources": ["D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <ScanOnWeb />\n  </div>\n</template>\n\n<script>\nimport ScanOnWeb from './components/ScanOnWebNew.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    ScanOnWeb\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f7fa;\n}\n\n#app {\n  min-height: 100vh;\n}\n\n/* 全局滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": "AAOA,OAAOA,SAAQ,MAAO,+BAA8B;AAEpD,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  id: \"app\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ScanOnWeb = _resolveComponent(\"ScanOnWeb\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_ScanOnWeb)]);\n}", "map": {"version": 3, "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_ScanOnWeb"], "sources": ["D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <ScanOnWeb />\n  </div>\n</template>\n\n<script>\nimport ScanOnWeb from './components/ScanOnWeb.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    ScanOnWeb\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f7fa;\n}\n\n#app {\n  min-height: 100vh;\n}\n\n/* 全局滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n"], "mappings": ";;EACOA,EAAE,EAAC;AAAK;;;uBAAbC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAaC,oBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
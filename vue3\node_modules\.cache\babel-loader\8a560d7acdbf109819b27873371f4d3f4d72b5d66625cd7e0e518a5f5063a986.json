{"ast": null, "code": "import overArg from './_overArg.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\nexport default nativeKeys;", "map": {"version": 3, "names": ["overArg", "nativeKeys", "Object", "keys"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_nativeKeys.js"], "sourcesContent": ["import overArg from './_overArg.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\n\nexport default nativeKeys;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,UAAU,GAAGD,OAAO,CAACE,MAAM,CAACC,IAAI,EAAED,MAAM,CAAC;AAE7C,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
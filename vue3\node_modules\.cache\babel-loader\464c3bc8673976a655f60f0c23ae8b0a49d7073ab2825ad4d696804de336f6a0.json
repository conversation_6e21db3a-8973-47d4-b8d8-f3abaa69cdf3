{"ast": null, "code": "import toInteger from './toInteger.js';\nimport toLength from './toLength.js';\n\n/**\n * The base implementation of `_.fill` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n */\nfunction baseFill(array, value, start, end) {\n  var length = array.length;\n  start = toInteger(start);\n  if (start < 0) {\n    start = -start > length ? 0 : length + start;\n  }\n  end = end === undefined || end > length ? length : toInteger(end);\n  if (end < 0) {\n    end += length;\n  }\n  end = start > end ? 0 : toLength(end);\n  while (start < end) {\n    array[start++] = value;\n  }\n  return array;\n}\nexport default baseFill;", "map": {"version": 3, "names": ["toInteger", "to<PERSON><PERSON><PERSON>", "baseFill", "array", "value", "start", "end", "length", "undefined"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_baseFill.js"], "sourcesContent": ["import toInteger from './toInteger.js';\nimport toLength from './toLength.js';\n\n/**\n * The base implementation of `_.fill` without an iteratee call guard.\n *\n * @private\n * @param {Array} array The array to fill.\n * @param {*} value The value to fill `array` with.\n * @param {number} [start=0] The start position.\n * @param {number} [end=array.length] The end position.\n * @returns {Array} Returns `array`.\n */\nfunction baseFill(array, value, start, end) {\n  var length = array.length;\n\n  start = toInteger(start);\n  if (start < 0) {\n    start = -start > length ? 0 : (length + start);\n  }\n  end = (end === undefined || end > length) ? length : toInteger(end);\n  if (end < 0) {\n    end += length;\n  }\n  end = start > end ? 0 : toLength(end);\n  while (start < end) {\n    array[start++] = value;\n  }\n  return array;\n}\n\nexport default baseFill;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC1C,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM;EAEzBF,KAAK,GAAGL,SAAS,CAACK,KAAK,CAAC;EACxB,IAAIA,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,GAAG,CAACA,KAAK,GAAGE,MAAM,GAAG,CAAC,GAAIA,MAAM,GAAGF,KAAM;EAChD;EACAC,GAAG,GAAIA,GAAG,KAAKE,SAAS,IAAIF,GAAG,GAAGC,MAAM,GAAIA,MAAM,GAAGP,SAAS,CAACM,GAAG,CAAC;EACnE,IAAIA,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAIC,MAAM;EACf;EACAD,GAAG,GAAGD,KAAK,GAAGC,GAAG,GAAG,CAAC,GAAGL,QAAQ,CAACK,GAAG,CAAC;EACrC,OAAOD,KAAK,GAAGC,GAAG,EAAE;IAClBH,KAAK,CAACE,KAAK,EAAE,CAAC,GAAGD,KAAK;EACxB;EACA,OAAOD,KAAK;AACd;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
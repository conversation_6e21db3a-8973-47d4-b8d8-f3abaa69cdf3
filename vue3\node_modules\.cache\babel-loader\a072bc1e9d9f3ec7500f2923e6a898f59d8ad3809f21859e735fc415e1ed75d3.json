{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, pushScopeId as _pushScopeId, popScopeId as _popScopeId } from \"vue\";\nconst _withScopeId = n => (_pushScopeId(\"data-v-1e4b8b90\"), n = n(), _popScopeId(), n);\nconst _hoisted_1 = {\n  class: \"scan-container\"\n};\nconst _hoisted_2 = {\n  class: \"page-title\"\n};\nconst _hoisted_3 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"p\", {\n  class: \"page-subtitle\"\n}, \"专业的文档扫描与管理解决方案\", -1 /* HOISTED */));\nconst _hoisted_4 = {\n  class: \"card-header\"\n};\nconst _hoisted_5 = /*#__PURE__*/_withScopeId(() => /*#__PURE__*/_createElementVNode(\"span\", null, \"扫描配置\", -1 /* HOISTED */));\nconst _hoisted_6 = {\n  class: \"advanced-options-compact\"\n};\nconst _hoisted_7 = {\n  class: \"options-row\"\n};\nconst _hoisted_8 = {\n  class: \"options-row\"\n};\nconst _hoisted_9 = {\n  class: \"card-header\"\n};\nconst _hoisted_10 = {\n  class: \"header-actions\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"image-grid\"\n};\nconst _hoisted_12 = {\n  class: \"image-wrapper\"\n};\nconst _hoisted_13 = [\"src\", \"alt\", \"onClick\"];\nconst _hoisted_14 = {\n  class: \"image-overlay\"\n};\nconst _hoisted_15 = {\n  class: \"image-info\"\n};\nconst _hoisted_16 = {\n  class: \"image-index\"\n};\nconst _hoisted_17 = [\"src\", \"onClick\"];\nconst _hoisted_18 = {\n  key: 0,\n  class: \"preview-container\"\n};\nconst _hoisted_19 = [\"src\", \"alt\"];\nconst _hoisted_20 = {\n  class: \"preview-footer\"\n};\nconst _hoisted_21 = {\n  class: \"preview-info\"\n};\nconst _hoisted_22 = {\n  class: \"preview-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Camera = _resolveComponent(\"Camera\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_Setting = _resolveComponent(\"Setting\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_Picture = _resolveComponent(\"Picture\");\n  const _component_el_button_group = _resolveComponent(\"el-button-group\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 页面标题 \"), _createVNode(_component_el_card, {\n    class: \"header-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"h1\", _hoisted_2, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Camera)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" 文档扫描系统 \")]), _hoisted_3]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 连接状态指示器 \"), !$setup.isConnected ? (_openBlock(), _createBlock(_component_el_alert, {\n    key: 0,\n    title: \"扫描服务未连接\",\n    description: \"请确保扫描服务程序已启动并正在运行\",\n    type: \"warning\",\n    closable: false,\n    \"show-icon\": \"\",\n    class: \"connection-alert\"\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 扫描配置区域 \"), _createVNode(_component_el_card, {\n    class: \"config-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Setting)]),\n      _: 1 /* STABLE */\n    }), _hoisted_5])]),\n    default: _withCtx(() => [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 设备选择 \"), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"扫描设备\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_select, {\n            modelValue: $setup.selectedDevice,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.selectedDevice = $event),\n            onChange: $setup.onDeviceChange,\n            placeholder: \"请选择扫描设备\",\n            style: {\n              \"width\": \"100%\"\n            }\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.devices, (device, index) => {\n              return _openBlock(), _createBlock(_component_el_option, {\n                key: index,\n                label: device,\n                value: index\n              }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n            }), 128 /* KEYED_FRAGMENT */))]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 色彩模式 \"), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"色彩模式\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_select, {\n            modelValue: $setup.config.colorMode,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.config.colorMode = $event),\n            style: {\n              \"width\": \"100%\"\n            }\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_option, {\n              label: \"彩色\",\n              value: \"RGB\"\n            }), _createVNode(_component_el_option, {\n              label: \"灰度\",\n              value: \"GRAY\"\n            }), _createVNode(_component_el_option, {\n              label: \"黑白\",\n              value: \"BW\"\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 分辨率设置 \"), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"分辨率 (DPI)\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_row, {\n            gutter: 10\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_col, {\n              span: 11\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_input_number, {\n                modelValue: $setup.config.dpi_x,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.config.dpi_x = $event),\n                min: 75,\n                max: 1200,\n                step: 25,\n                style: {\n                  \"width\": \"100%\"\n                }\n              }, null, 8 /* PROPS */, [\"modelValue\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_col, {\n              span: 2,\n              class: \"dpi-separator\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(\"×\")]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_col, {\n              span: 11\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_input_number, {\n                modelValue: $setup.config.dpi_y,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.config.dpi_y = $event),\n                min: 75,\n                max: 1200,\n                step: 25,\n                style: {\n                  \"width\": \"100%\"\n                }\n              }, null, 8 /* PROPS */, [\"modelValue\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 高级选项 \"), _createVNode(_component_el_col, {\n        span: 12\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_form_item, {\n          label: \"高级选项\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_checkbox, {\n            modelValue: $setup.config.showDialog,\n            \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.config.showDialog = $event)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"设备对话框\")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_checkbox, {\n            modelValue: $setup.config.autoFeedEnable,\n            \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.config.autoFeedEnable = $event)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"自动进纸\")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_checkbox, {\n            modelValue: $setup.config.dupxMode,\n            \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.config.dupxMode = $event)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"双面扫描\")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_checkbox, {\n            modelValue: $setup.config.autoDeskew,\n            \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.config.autoDeskew = $event)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"自动纠偏\")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_checkbox, {\n            modelValue: $setup.config.autoBorderDetection,\n            \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.config.autoBorderDetection = $event)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"边框检测\")]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"modelValue\"])])])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 操作按钮区域 \"), _createVNode(_component_el_card, {\n    class: \"action-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_row, {\n      gutter: 15\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.loadDevices,\n          loading: $setup.loading.devices,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Refresh)]),\n            _: 1 /* STABLE */\n          }), _createTextVNode(\" 刷新设备 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"success\",\n          onClick: $setup.startScan,\n          loading: $setup.loading.scan,\n          disabled: $setup.selectedDevice === -1 || !$setup.isConnected,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Camera)]),\n            _: 1 /* STABLE */\n          }), _createTextVNode(\" 开始扫描 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"loading\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"info\",\n          onClick: $setup.getAllImage,\n          loading: $setup.loading.images,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Picture)]),\n            _: 1 /* STABLE */\n          }), _createTextVNode(\" 获取图像 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"warning\",\n          onClick: $setup.clearAll,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 🗑️ 清空结果 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.showUploadDialog,\n          disabled: $setup.images.length === 0,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 📤 上传文档 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 4\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"default\",\n          onClick: $setup.saveAs,\n          disabled: $setup.images.length === 0,\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 💾 本地保存 \")]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 扫描结果展示区域 \"), $setup.images.length > 0 ? (_openBlock(), _createBlock(_component_el_card, {\n    key: 1,\n    class: \"result-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Picture)]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"span\", null, \"扫描结果 (\" + _toDisplayString($setup.images.length) + \" 张图像)\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button_group, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: $setup.viewMode === 'grid' ? 'primary' : 'default',\n        onClick: _cache[9] || (_cache[9] = $event => $setup.viewMode = 'grid'),\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\" 🔲 网格 \")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"]), _createVNode(_component_el_button, {\n        type: $setup.viewMode === 'list' ? 'primary' : 'default',\n        onClick: _cache[10] || (_cache[10] = $event => $setup.viewMode = 'list'),\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\" 📋 列表 \")]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"])]),\n      _: 1 /* STABLE */\n    })])])]),\n    default: _withCtx(() => [$setup.viewMode === 'grid' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.images, (image, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"image-item\"\n      }, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"img\", {\n        src: image.src,\n        alt: `扫描图像 ${index + 1}`,\n        onClick: $event => $setup.previewImage(index),\n        class: \"scan-image\"\n      }, null, 8 /* PROPS */, _hoisted_13), _createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_button_group, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => $setup.previewImage(index)\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 👁️ \")]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"success\",\n          size: \"small\",\n          onClick: $event => $setup.downloadImage(index)\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 💾 \")]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.deleteImage(index)\n        }, {\n          default: _withCtx(() => [_createTextVNode(\" 🗑️ \")]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)])]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"span\", _hoisted_16, \"图像 \" + _toDisplayString(index + 1), 1 /* TEXT */)])]);\n    }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 列表视图 \"), _createVNode(_component_el_table, {\n      data: $setup.images,\n      stripe: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        label: \"预览\",\n        width: \"120\"\n      }, {\n        default: _withCtx(({\n          row,\n          $index\n        }) => [_createElementVNode(\"img\", {\n          src: row.src,\n          class: \"table-thumbnail\",\n          onClick: $event => $setup.previewImage($index)\n        }, null, 8 /* PROPS */, _hoisted_17)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"图像编号\",\n        prop: \"index\",\n        width: \"100\"\n      }, {\n        default: _withCtx(({\n          $index\n        }) => [_createTextVNode(\" 图像 \" + _toDisplayString($index + 1), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"格式\",\n        width: \"100\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(\"JPEG\")]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"200\"\n      }, {\n        default: _withCtx(({\n          $index\n        }) => [_createVNode(_component_el_button_group, null, {\n          default: _withCtx(() => [_createVNode(_component_el_button, {\n            type: \"primary\",\n            size: \"small\",\n            onClick: $event => $setup.previewImage($index)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\" 👁️ 预览 \")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n            type: \"success\",\n            size: \"small\",\n            onClick: $event => $setup.downloadImage($index)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\" 💾 下载 \")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n            type: \"danger\",\n            size: \"small\",\n            onClick: $event => $setup.deleteImage($index)\n          }, {\n            default: _withCtx(() => [_createTextVNode(\" 🗑️ 删除 \")]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]),\n    _: 1 /* STABLE */\n  })) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 空状态 \"), _createVNode(_component_el_empty, {\n    description: \"暂无扫描结果\",\n    class: \"empty-state\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.startScan,\n      disabled: $setup.selectedDevice === -1 || !$setup.isConnected\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 开始扫描 \")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]),\n    _: 1 /* STABLE */\n  })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))]), _createCommentVNode(\" 上传对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.uploadDialog.visible,\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $setup.uploadDialog.visible = $event),\n    title: \"上传文档\",\n    width: \"500px\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[14] || (_cache[14] = $event => $setup.uploadDialog.visible = false)\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"取消\")]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.confirmUpload,\n      loading: $setup.loading.upload\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 确认上传 \")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.uploadDialog.form,\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"文档ID\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.uploadDialog.form.id,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.uploadDialog.form.id = $event),\n          placeholder: \"请输入文档ID\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"文档描述\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.uploadDialog.form.description,\n          \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.uploadDialog.form.description = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入文档描述\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"上传格式\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n          modelValue: $setup.uploadDialog.form.format,\n          \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.uploadDialog.form.format = $event)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio, {\n            label: \"pdf\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"PDF文档\")]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_radio, {\n            label: \"tiff\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"TIFF图像\")]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_radio, {\n            label: \"jpg\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(\"JPG图像\")]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 图像预览对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.previewDialog.visible,\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $setup.previewDialog.visible = $event),\n    title: \"图像预览\",\n    width: \"80%\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, \" 图像 \" + _toDisplayString($setup.previewDialog.currentIndex + 1) + \" / \" + _toDisplayString($setup.images.length), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_button, {\n      onClick: $setup.prevImage,\n      disabled: $setup.previewDialog.currentIndex <= 0\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" ⬅️ 上一张 \")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      onClick: $setup.nextImage,\n      disabled: $setup.previewDialog.currentIndex >= $setup.images.length - 1\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 下一张 ➡️ \")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.downloadCurrentImage\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 💾 下载 \")]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      onClick: _cache[16] || (_cache[16] = $event => $setup.previewDialog.visible = false)\n    }, {\n      default: _withCtx(() => [_createTextVNode(\"关闭\")]),\n      _: 1 /* STABLE */\n    })])])]),\n    default: _withCtx(() => [$setup.previewDialog.currentImage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createElementVNode(\"img\", {\n      src: $setup.previewDialog.currentImage.src,\n      class: \"preview-image\",\n      alt: `预览图像 ${$setup.previewDialog.currentIndex + 1}`\n    }, null, 8 /* PROPS */, _hoisted_19)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createElementVNode", "key", "_createElementBlock", "_Fragment", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_el_card", "default", "_withCtx", "_hoisted_2", "_component_el_icon", "_component_Camera", "_", "_createTextVNode", "_hoisted_3", "$setup", "isConnected", "_createBlock", "_component_el_alert", "title", "description", "type", "closable", "header", "_hoisted_4", "_component_Setting", "_hoisted_5", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "_component_el_select", "modelValue", "selected<PERSON><PERSON><PERSON>", "_cache", "$event", "onChange", "onDeviceChange", "placeholder", "style", "_renderList", "devices", "device", "index", "_component_el_option", "value", "config", "colorMode", "_component_el_input_number", "dpi_x", "min", "max", "step", "dpi_y", "_hoisted_6", "_hoisted_7", "_component_el_checkbox", "showDialog", "autoFeedEnable", "dupxMode", "_hoisted_8", "autoDeskew", "autoBorderDetection", "_component_el_button", "onClick", "loadDevices", "loading", "_component_Refresh", "startScan", "scan", "disabled", "getAllImage", "images", "_component_Picture", "clearAll", "showUploadDialog", "length", "saveAs", "_hoisted_9", "_toDisplayString", "_hoisted_10", "_component_el_button_group", "viewMode", "size", "_hoisted_11", "image", "_hoisted_12", "src", "alt", "previewImage", "_hoisted_13", "_hoisted_14", "downloadImage", "deleteImage", "_hoisted_15", "_hoisted_16", "_component_el_table", "data", "stripe", "_component_el_table_column", "width", "row", "$index", "_hoisted_17", "prop", "_component_el_tag", "_component_el_empty", "_component_el_dialog", "uploadDialog", "visible", "footer", "confirmUpload", "upload", "_component_el_form", "model", "form", "_component_el_input", "id", "rows", "_component_el_radio_group", "format", "_component_el_radio", "previewDialog", "_hoisted_20", "_hoisted_21", "currentIndex", "_hoisted_22", "prevImage", "nextImage", "downloadCurrentImage", "currentImage", "_hoisted_18", "_hoisted_19"], "sources": ["D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\components\\ScanOnWebNew.vue"], "sourcesContent": ["<!-- eslint-disable vue/no-unused-components, no-unused-vars -->\n<template>\n  <div class=\"scan-container\">\n    <!-- 页面标题 -->\n    <el-card class=\"header-card\">\n      <h1 class=\"page-title\">\n        <el-icon><Camera /></el-icon>\n        文档扫描系统\n      </h1>\n      <p class=\"page-subtitle\">专业的文档扫描与管理解决方案</p>\n    </el-card>\n\n    <!-- 连接状态指示器 -->\n    <el-alert\n      v-if=\"!isConnected\"\n      title=\"扫描服务未连接\"\n      description=\"请确保扫描服务程序已启动并正在运行\"\n      type=\"warning\"\n      :closable=\"false\"\n      show-icon\n      class=\"connection-alert\"\n    />\n\n    <!-- 扫描配置区域 -->\n    <el-card class=\"config-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Setting /></el-icon>\n          <span>扫描配置</span>\n        </div>\n      </template>\n\n      <el-row :gutter=\"20\">\n        <!-- 设备选择 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"扫描设备\">\n            <el-select \n              v-model=\"selectedDevice\" \n              @change=\"onDeviceChange\"\n              placeholder=\"请选择扫描设备\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"(device, index) in devices\"\n                :key=\"index\"\n                :label=\"device\"\n                :value=\"index\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 色彩模式 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"色彩模式\">\n            <el-select v-model=\"config.colorMode\" style=\"width: 100%\">\n              <el-option label=\"彩色\" value=\"RGB\" />\n              <el-option label=\"灰度\" value=\"GRAY\" />\n              <el-option label=\"黑白\" value=\"BW\" />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 分辨率设置 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"分辨率 (DPI)\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"11\">\n                <el-input-number \n                  v-model=\"config.dpi_x\" \n                  :min=\"75\" \n                  :max=\"1200\" \n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n              <el-col :span=\"2\" class=\"dpi-separator\">×</el-col>\n              <el-col :span=\"11\">\n                <el-input-number \n                  v-model=\"config.dpi_y\" \n                  :min=\"75\" \n                  :max=\"1200\" \n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n            </el-row>\n          </el-form-item>\n        </el-col>\n\n        <!-- 高级选项 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"高级选项\">\n            <div class=\"advanced-options-compact\">\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.showDialog\">设备对话框</el-checkbox>\n                <el-checkbox v-model=\"config.autoFeedEnable\">自动进纸</el-checkbox>\n                <el-checkbox v-model=\"config.dupxMode\">双面扫描</el-checkbox>\n              </div>\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.autoDeskew\">自动纠偏</el-checkbox>\n                <el-checkbox v-model=\"config.autoBorderDetection\">边框检测</el-checkbox>\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 操作按钮区域 -->\n    <el-card class=\"action-card\">\n      <el-row :gutter=\"15\">\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"loadDevices\"\n            :loading=\"loading.devices\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Refresh /></el-icon>\n            刷新设备\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"success\"\n            @click=\"startScan\"\n            :loading=\"loading.scan\"\n            :disabled=\"selectedDevice === -1 || !isConnected\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Camera /></el-icon>\n            开始扫描\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"info\"\n            @click=\"getAllImage\"\n            :loading=\"loading.images\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Picture /></el-icon>\n            获取图像\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"warning\"\n            @click=\"clearAll\"\n            style=\"width: 100%\"\n          >\n            🗑️ 清空结果\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"showUploadDialog\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            📤 上传文档\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"default\"\n            @click=\"saveAs\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            💾 本地保存\n          </el-button>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 扫描结果展示区域 -->\n    <el-card class=\"result-card\" v-if=\"images.length > 0\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Picture /></el-icon>\n          <span>扫描结果 ({{ images.length }} 张图像)</span>\n          <div class=\"header-actions\">\n            <el-button-group>\n              <el-button\n                :type=\"viewMode === 'grid' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'grid'\"\n                size=\"small\"\n              >\n                🔲 网格\n              </el-button>\n              <el-button\n                :type=\"viewMode === 'list' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'list'\"\n                size=\"small\"\n              >\n                📋 列表\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n      </template>\n\n      <!-- 网格视图 -->\n      <div v-if=\"viewMode === 'grid'\" class=\"image-grid\">\n        <div \n          v-for=\"(image, index) in images\" \n          :key=\"index\" \n          class=\"image-item\"\n        >\n          <div class=\"image-wrapper\">\n            <img \n              :src=\"image.src\" \n              :alt=\"`扫描图像 ${index + 1}`\"\n              @click=\"previewImage(index)\"\n              class=\"scan-image\"\n            />\n            <div class=\"image-overlay\">\n              <el-button-group>\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"previewImage(index)\"\n                >\n                  👁️\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"downloadImage(index)\"\n                >\n                  💾\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  @click=\"deleteImage(index)\"\n                >\n                  🗑️\n                </el-button>\n              </el-button-group>\n            </div>\n          </div>\n          <div class=\"image-info\">\n            <span class=\"image-index\">图像 {{ index + 1 }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 列表视图 -->\n      <el-table v-else :data=\"images\" stripe>\n        <el-table-column label=\"预览\" width=\"120\">\n          <template #default=\"{ row, $index }\">\n            <img \n              :src=\"row.src\" \n              class=\"table-thumbnail\"\n              @click=\"previewImage($index)\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"图像编号\" prop=\"index\" width=\"100\">\n          <template #default=\"{ $index }\">\n            图像 {{ $index + 1 }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"格式\" width=\"100\">\n          <template #default>\n            <el-tag type=\"info\">JPEG</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"{ $index }\">\n            <el-button-group>\n              <el-button\n                type=\"primary\"\n                size=\"small\"\n                @click=\"previewImage($index)\"\n              >\n                👁️ 预览\n              </el-button>\n              <el-button\n                type=\"success\"\n                size=\"small\"\n                @click=\"downloadImage($index)\"\n              >\n                💾 下载\n              </el-button>\n              <el-button\n                type=\"danger\"\n                size=\"small\"\n                @click=\"deleteImage($index)\"\n              >\n                🗑️ 删除\n              </el-button>\n            </el-button-group>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 空状态 -->\n    <el-empty \n      v-else \n      description=\"暂无扫描结果\"\n      class=\"empty-state\"\n    >\n      <el-button \n        type=\"primary\" \n        @click=\"startScan\"\n        :disabled=\"selectedDevice === -1 || !isConnected\"\n      >\n        开始扫描\n      </el-button>\n    </el-empty>\n  </div>\n\n  <!-- 上传对话框 -->\n  <el-dialog\n    v-model=\"uploadDialog.visible\"\n    title=\"上传文档\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n  >\n    <el-form :model=\"uploadDialog.form\" label-width=\"100px\">\n      <el-form-item label=\"文档ID\">\n        <el-input v-model=\"uploadDialog.form.id\" placeholder=\"请输入文档ID\" />\n      </el-form-item>\n      <el-form-item label=\"文档描述\">\n        <el-input\n          v-model=\"uploadDialog.form.description\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入文档描述\"\n        />\n      </el-form-item>\n      <el-form-item label=\"上传格式\">\n        <el-radio-group v-model=\"uploadDialog.form.format\">\n          <el-radio label=\"pdf\">PDF文档</el-radio>\n          <el-radio label=\"tiff\">TIFF图像</el-radio>\n          <el-radio label=\"jpg\">JPG图像</el-radio>\n        </el-radio-group>\n      </el-form-item>\n    </el-form>\n\n    <template #footer>\n      <el-button @click=\"uploadDialog.visible = false\">取消</el-button>\n      <el-button\n        type=\"primary\"\n        @click=\"confirmUpload\"\n        :loading=\"loading.upload\"\n      >\n        确认上传\n      </el-button>\n    </template>\n  </el-dialog>\n\n  <!-- 图像预览对话框 -->\n  <el-dialog\n    v-model=\"previewDialog.visible\"\n    title=\"图像预览\"\n    width=\"80%\"\n    :close-on-click-modal=\"false\"\n  >\n    <div class=\"preview-container\" v-if=\"previewDialog.currentImage\">\n      <img\n        :src=\"previewDialog.currentImage.src\"\n        class=\"preview-image\"\n        :alt=\"`预览图像 ${previewDialog.currentIndex + 1}`\"\n      />\n    </div>\n    <template #footer>\n      <div class=\"preview-footer\">\n        <div class=\"preview-info\">\n          图像 {{ previewDialog.currentIndex + 1 }} / {{ images.length }}\n        </div>\n        <div class=\"preview-actions\">\n          <el-button\n            @click=\"prevImage\"\n            :disabled=\"previewDialog.currentIndex <= 0\"\n          >\n            ⬅️ 上一张\n          </el-button>\n          <el-button\n            @click=\"nextImage\"\n            :disabled=\"previewDialog.currentIndex >= images.length - 1\"\n          >\n            下一张 ➡️\n          </el-button>\n          <el-button\n            type=\"success\"\n            @click=\"downloadCurrentImage\"\n          >\n            💾 下载\n          </el-button>\n          <el-button @click=\"previewDialog.visible = false\">关闭</el-button>\n        </div>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Camera, Setting, Picture, Refresh } from '@element-plus/icons-vue'\nimport ScanOnWeb from '@/scanonweb.js'\nimport { utils } from '@/services/api.js'\n\nexport default {\n  name: 'ScanOnWeb',\n  components: {\n    Camera,\n    Setting,\n    Picture,\n    Refresh\n  },\n  setup() {\n    // 响应式数据\n    const scanonweb = ref(null)\n    const isConnected = ref(false)\n    const devices = ref([])\n    const selectedDevice = ref(-1)\n    const images = ref([])\n    const viewMode = ref('grid')\n\n    // 扫描配置\n    const config = reactive({\n      dpi_x: 300,\n      dpi_y: 300,\n      colorMode: 'RGB',\n      showDialog: false,\n      autoFeedEnable: true,\n      autoFeed: false,\n      dupxMode: false,\n      autoDeskew: false,\n      autoBorderDetection: false\n    })\n\n    // 加载状态\n    const loading = reactive({\n      devices: false,\n      scan: false,\n      images: false,\n      upload: false\n    })\n\n    // 上传对话框\n    const uploadDialog = reactive({\n      visible: false,\n      form: {\n        id: '',\n        description: '',\n        format: 'pdf'\n      }\n    })\n\n    // 预览对话框\n    const previewDialog = reactive({\n      visible: false,\n      currentIndex: 0,\n      currentImage: null\n    })\n\n    // 初始化扫描服务\n    const initScanService = () => {\n      try {\n        scanonweb.value = new ScanOnWeb()\n\n        // 设置事件回调\n        scanonweb.value.onGetDevicesListEvent = (msg) => {\n          devices.value = msg.devices || []\n          // 自动选择第一个设备，如果没有当前设备则选择第一个\n          if (devices.value.length > 0) {\n            selectedDevice.value = msg.currentIndex >= 0 ? msg.currentIndex : 0\n            // 如果自动选择了设备，调用设备选择事件\n            if (selectedDevice.value >= 0) {\n              setTimeout(() => {\n                onDeviceChange(selectedDevice.value)\n              }, 100)\n            }\n          } else {\n            selectedDevice.value = -1\n          }\n          loading.devices = false\n          isConnected.value = true\n          ElMessage.success(`发现 ${devices.value.length} 个扫描设备${selectedDevice.value >= 0 ? '，已自动选择第一个设备' : ''}`)\n        }\n\n        scanonweb.value.onScanFinishedEvent = (msg) => {\n          loading.scan = false\n          ElMessage.success(`扫描完成！共扫描 ${msg.imageAfterCount} 张图像`)\n          getAllImage()\n        }\n\n        scanonweb.value.onGetAllImageEvent = (msg) => {\n          loading.images = false\n          if (msg.images && msg.images.length > 0) {\n            images.value = msg.images.map((image, index) => ({\n              src: `data:image/jpg;base64,${image}`,\n              index: index,\n              base64: image\n            }))\n            ElMessage.success(`获取到 ${images.value.length} 张图像`)\n          } else {\n            ElMessage.info('暂无扫描图像')\n          }\n        }\n\n        scanonweb.value.onGetImageByIdEvent = (msg) => {\n          if (msg.imageBase64) {\n            addImage(msg.imageBase64)\n          }\n        }\n\n        scanonweb.value.onImageEditedEvent = (msg) => {\n          ElMessage.info(`图像 ${msg.imageIndex + 1} 已编辑`)\n          if (msg.imageBase64) {\n            editImage(msg.imageIndex, msg.imageBase64)\n          }\n        }\n\n        scanonweb.value.onUploadEvent = () => {\n          ElMessage.info('用户点击了上传按钮')\n          showUploadDialog()\n        }\n\n        // 检查连接状态\n        setTimeout(() => {\n          if (devices.value.length === 0) {\n            isConnected.value = false\n            ElMessage.warning('扫描服务连接失败，请检查服务是否启动')\n          }\n        }, 3000)\n\n      } catch (error) {\n        console.error('初始化扫描服务失败:', error)\n        isConnected.value = false\n        ElMessage.error('初始化扫描服务失败')\n      }\n    }\n\n    // 加载设备列表\n    const loadDevices = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      loading.devices = true\n      try {\n        scanonweb.value.loadDevices()\n      } catch (error) {\n        loading.devices = false\n        ElMessage.error('获取设备列表失败')\n      }\n    }\n\n    // 设备选择变化\n    const onDeviceChange = (deviceIndex) => {\n      if (scanonweb.value) {\n        scanonweb.value.selectScanDevice(deviceIndex)\n        ElMessage.info(`已选择设备: ${devices.value[deviceIndex]}`)\n      }\n    }\n\n    // 开始扫描\n    const startScan = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (selectedDevice.value === -1) {\n        ElMessage.warning('请先选择扫描设备')\n        return\n      }\n\n      loading.scan = true\n      try {\n        // 更新扫描配置\n        scanonweb.value.scaner_work_config = {\n          ...scanonweb.value.scaner_work_config,\n          ...config,\n          deviceIndex: selectedDevice.value\n        }\n\n        scanonweb.value.startScan()\n        ElMessage.info('开始扫描...')\n      } catch (error) {\n        loading.scan = false\n        ElMessage.error('启动扫描失败')\n      }\n    }\n\n    // 获取所有图像\n    const getAllImage = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      loading.images = true\n      try {\n        scanonweb.value.getAllImage()\n      } catch (error) {\n        loading.images = false\n        ElMessage.error('获取图像失败')\n      }\n    }\n\n    // 清空所有图像\n    const clearAll = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有扫描结果吗？', '确认操作', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        if (scanonweb.value) {\n          scanonweb.value.clearAll()\n          images.value = []\n          ElMessage.success('已清空所有扫描结果')\n        }\n      } catch {\n        // 用户取消操作\n      }\n    }\n\n    // 添加图像\n    const addImage = (imageBase64) => {\n      images.value.push({\n        src: `data:image/jpg;base64,${imageBase64}`,\n        index: images.value.length,\n        base64: imageBase64\n      })\n    }\n\n    // 编辑图像\n    const editImage = (index, imageBase64) => {\n      if (index >= 0 && index < images.value.length) {\n        images.value[index] = {\n          src: `data:image/jpg;base64,${imageBase64}`,\n          index: index,\n          base64: imageBase64\n        }\n      }\n    }\n\n    // 显示上传对话框\n    const showUploadDialog = () => {\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像')\n        return\n      }\n\n      uploadDialog.form.id = utils.generateId()\n      uploadDialog.form.description = `扫描文档_${new Date().toLocaleString()}`\n      uploadDialog.visible = true\n    }\n\n    // 确认上传\n    const confirmUpload = async () => {\n      if (!uploadDialog.form.id.trim()) {\n        ElMessage.warning('请输入文档ID')\n        return\n      }\n\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像')\n        return\n      }\n\n      loading.upload = true\n      try {\n        const { id, description, format } = uploadDialog.form\n        const uploadUrl = 'http://localhost:8080/upload'\n\n        // 设置上传完成回调\n        scanonweb.value.onUploadAllImageAsPdfToUrlEvent = (msg) => {\n          loading.upload = false\n          if (msg.success) {\n            ElMessage.success('PDF文档上传成功！')\n            uploadDialog.visible = false\n          } else {\n            ElMessage.error(`上传失败: ${msg.message || '未知错误'}`)\n          }\n        }\n\n        scanonweb.value.onUploadAllImageAsTiffToUrlEvent = (msg) => {\n          loading.upload = false\n          if (msg.success) {\n            ElMessage.success('TIFF文档上传成功！')\n            uploadDialog.visible = false\n          } else {\n            ElMessage.error(`上传失败: ${msg.message || '未知错误'}`)\n          }\n        }\n\n        scanonweb.value.uploadJpgImageByIndexEvent = (msg) => {\n          loading.upload = false\n          if (msg.success) {\n            ElMessage.success('JPG图像上传成功！')\n            uploadDialog.visible = false\n          } else {\n            ElMessage.error(`上传失败: ${msg.message || '未知错误'}`)\n          }\n        }\n\n        // 调用控件内置的上传方法\n        if (format === 'pdf') {\n          ElMessage.info('开始上传PDF文档...')\n          scanonweb.value.uploadAllImageAsPdfToUrl(uploadUrl, id, description)\n        } else if (format === 'tiff') {\n          ElMessage.info('开始上传TIFF文档...')\n          const tiffUploadUrl = 'http://localhost:8080/upload-tiff'\n          scanonweb.value.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description)\n        } else if (format === 'jpg') {\n          ElMessage.info('开始上传JPG图像...')\n          const jpgUploadUrl = 'http://localhost:8080/upload-jpg'\n          // 上传第一张图像作为示例，实际可以循环上传所有图像\n          scanonweb.value.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0)\n        }\n\n      } catch (error) {\n        loading.upload = false\n        console.error('上传失败:', error)\n        ElMessage.error(`上传失败: ${error.message || '未知错误'}`)\n      }\n    }\n\n    // 预览图像\n    const previewImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        previewDialog.currentIndex = index\n        previewDialog.currentImage = images.value[index]\n        previewDialog.visible = true\n      }\n    }\n\n    // 上一张图像\n    const prevImage = () => {\n      if (previewDialog.currentIndex > 0) {\n        previewDialog.currentIndex--\n        previewDialog.currentImage = images.value[previewDialog.currentIndex]\n      }\n    }\n\n    // 下一张图像\n    const nextImage = () => {\n      if (previewDialog.currentIndex < images.value.length - 1) {\n        previewDialog.currentIndex++\n        previewDialog.currentImage = images.value[previewDialog.currentIndex]\n      }\n    }\n\n    // 下载图像\n    const downloadImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        const image = images.value[index]\n        const filename = `scan_image_${index + 1}.jpg`\n        utils.downloadBase64Image(image.base64, filename)\n        ElMessage.success(`图像 ${index + 1} 下载成功`)\n      }\n    }\n\n    // 下载当前预览图像\n    const downloadCurrentImage = () => {\n      downloadImage(previewDialog.currentIndex)\n    }\n\n    // 删除图像\n    const deleteImage = async (index) => {\n      try {\n        await ElMessageBox.confirm(`确定要删除图像 ${index + 1} 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        images.value.splice(index, 1)\n        // 重新设置索引\n        images.value.forEach((img, idx) => {\n          img.index = idx\n        })\n\n        ElMessage.success('图像删除成功')\n      } catch {\n        // 用户取消操作\n      }\n    }\n\n    // 本地保存\n    const saveAs = () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可保存的图像')\n        return\n      }\n\n      try {\n        const filename = `d:/scan_${Date.now()}.pdf`\n        scanonweb.value.saveAllImageToLocal(filename)\n        ElMessage.success('文件保存成功')\n      } catch (error) {\n        ElMessage.error('保存失败')\n      }\n    }\n\n    // 生命周期钩子\n    onMounted(() => {\n      initScanService()\n      // 自动加载设备列表\n      setTimeout(() => {\n        loadDevices()\n      }, 1000)\n    })\n\n    onUnmounted(() => {\n      // 清理资源\n      if (scanonweb.value && scanonweb.value.h5socket) {\n        scanonweb.value.h5socket.close()\n      }\n    })\n\n    // 返回模板需要的数据和方法\n    return {\n      // 响应式数据\n      isConnected,\n      devices,\n      selectedDevice,\n      images,\n      viewMode,\n      config,\n      loading,\n      uploadDialog,\n      previewDialog,\n\n      // 方法\n      loadDevices,\n      onDeviceChange,\n      startScan,\n      getAllImage,\n      clearAll,\n      showUploadDialog,\n      confirmUpload,\n      previewImage,\n      prevImage,\n      nextImage,\n      downloadImage,\n      downloadCurrentImage,\n      deleteImage,\n      saveAs\n    }\n  }\n}\n</script>\n\n<style scoped>\n.scan-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n.header-card {\n  margin-bottom: 20px;\n  text-align: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n}\n\n.page-title {\n  margin: 0;\n  font-size: 2.5em;\n  font-weight: 300;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n}\n\n.page-subtitle {\n  margin: 10px 0 0 0;\n  font-size: 1.1em;\n  opacity: 0.9;\n}\n\n.connection-alert {\n  margin-bottom: 20px;\n}\n\n.config-card,\n.action-card,\n.result-card {\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  border: none;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-actions {\n  margin-left: auto;\n}\n\n.dpi-separator {\n  text-align: center;\n  line-height: 32px;\n  font-weight: bold;\n  color: #909399;\n}\n\n.advanced-options-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.options-row {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n  padding: 10px 0;\n}\n\n.image-item {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.image-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n.image-wrapper {\n  position: relative;\n  overflow: hidden;\n}\n\n.scan-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.scan-image:hover {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.2s;\n}\n\n.image-wrapper:hover .image-overlay {\n  opacity: 1;\n}\n\n.image-info {\n  padding: 15px;\n  text-align: center;\n}\n\n.image-index {\n  font-weight: 600;\n  color: #303133;\n}\n\n.table-thumbnail {\n  width: 80px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.table-thumbnail:hover {\n  transform: scale(1.1);\n}\n\n.empty-state {\n  margin: 40px 0;\n  padding: 40px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.preview-container {\n  text-align: center;\n  padding: 20px;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.preview-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n}\n\n.preview-info {\n  font-weight: 600;\n  color: #606266;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .scan-container {\n    padding: 10px;\n  }\n\n  .page-title {\n    font-size: 2em;\n  }\n\n  .image-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 15px;\n  }\n\n  .preview-footer {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .preview-actions {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.config-card,\n.action-card,\n.result-card {\n  animation: fadeIn 0.5s ease-out;\n}\n\n/* Element Plus 组件样式覆盖 */\n.el-card__header {\n  background-color: #fafafa;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.el-button {\n  transition: all 0.2s;\n}\n\n.el-button:hover {\n  transform: translateY(-1px);\n}\n\n.el-form-item__label {\n  font-weight: 600;\n  color: #606266;\n}\n\n.el-alert {\n  border-radius: 8px;\n}\n</style>\n"], "mappings": ";;;EAEOA,KAAK,EAAC;AAAgB;;EAGnBA,KAAK,EAAC;AAAY;gEAItBC,mBAAA,CAA2C;EAAxCD,KAAK,EAAC;AAAe,GAAC,gBAAc;;EAiBhCA,KAAK,EAAC;AAAa;gEAEtBC,mBAAA,CAAiB,cAAX,MAAI;;EAiEHD,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAa;;EAKnBA,KAAK,EAAC;AAAa;;EAkFzBA,KAAK,EAAC;AAAa;;EAGjBA,KAAK,EAAC;AAAgB;;EAxLrCE,GAAA;EA8MsCF,KAAK,EAAC;;;EAM7BA,KAAK,EAAC;AAAe;oBApNpC;;EA2NiBA,KAAK,EAAC;AAAe;;EA0BvBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAa;oBAtPrC;;EAAAE,GAAA;EA6WSF,KAAK,EAAC;;oBA7Wf;;EAqXWA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAzXpCG,mBAAA,CAAAC,SAAA,SAEEH,mBAAA,CA0TM,OA1TNI,UA0TM,GAzTJC,mBAAA,UAAa,EACbC,YAAA,CAMUC,kBAAA;IANDR,KAAK,EAAC;EAAa;IAJhCS,OAAA,EAAAC,QAAA,CAKM,MAGK,CAHLT,mBAAA,CAGK,MAHLU,UAGK,GAFHJ,YAAA,CAA6BK,kBAAA;MANrCH,OAAA,EAAAC,QAAA,CAMiB,MAAU,CAAVH,YAAA,CAAUM,iBAAA,E;MAN3BC,CAAA;QAAAC,gBAAA,CAMqC,UAE/B,E,GACAC,UAA2C,C;IATjDF,CAAA;MAYIR,mBAAA,aAAgB,E,CAEPW,MAAA,CAAAC,WAAW,I,cADpBC,YAAA,CAQEC,mBAAA;IArBNlB,GAAA;IAeMmB,KAAK,EAAC,SAAS;IACfC,WAAW,EAAC,mBAAmB;IAC/BC,IAAI,EAAC,SAAS;IACbC,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT,EAAS;IACTxB,KAAK,EAAC;QApBZM,mBAAA,gBAuBIA,mBAAA,YAAe,EACfC,YAAA,CAmFUC,kBAAA;IAnFDR,KAAK,EAAC;EAAa;IACfyB,MAAM,EAAAf,QAAA,CACf,MAGM,CAHNT,mBAAA,CAGM,OAHNyB,UAGM,GAFJnB,YAAA,CAA8BK,kBAAA;MA3BxCH,OAAA,EAAAC,QAAA,CA2BmB,MAAW,CAAXH,YAAA,CAAWoB,kBAAA,E;MA3B9Bb,CAAA;QA4BUc,UAAiB,C;IA5B3BnB,OAAA,EAAAC,QAAA,CAgCM,MA0ES,CA1ETH,YAAA,CA0ESsB,iBAAA;MA1EAC,MAAM,EAAE;IAAE;MAhCzBrB,OAAA,EAAAC,QAAA,CAiCQ,MAAa,CAAbJ,mBAAA,UAAa,EACbC,YAAA,CAgBSwB,iBAAA;QAhBAC,IAAI,EAAE;MAAE;QAlCzBvB,OAAA,EAAAC,QAAA,CAmCU,MAce,CAdfH,YAAA,CAce0B,uBAAA;UAdDC,KAAK,EAAC;QAAM;UAnCpCzB,OAAA,EAAAC,QAAA,CAoCY,MAYY,CAZZH,YAAA,CAYY4B,oBAAA;YAhDxBC,UAAA,EAqCuBnB,MAAA,CAAAoB,cAAc;YArCrC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqCuBtB,MAAA,CAAAoB,cAAc,GAAAE,MAAA;YACtBC,QAAM,EAAEvB,MAAA,CAAAwB,cAAc;YACvBC,WAAW,EAAC,SAAS;YACrBC,KAAmB,EAAnB;cAAA;YAAA;;YAxCdlC,OAAA,EAAAC,QAAA,CA2CgB,MAAkC,E,kBADpCP,mBAAA,CAKEC,SAAA,QA/ChBwC,WAAA,CA2C0C3B,MAAA,CAAA4B,OAAO,EA3CjD,CA2CwBC,MAAM,EAAEC,KAAK;mCADvB5B,YAAA,CAKE6B,oBAAA;gBAHC9C,GAAG,EAAE6C,KAAK;gBACVb,KAAK,EAAEY,MAAM;gBACbG,KAAK,EAAEF;;;YA9CxBjC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAoDQR,mBAAA,UAAa,EACbC,YAAA,CAQSwB,iBAAA;QARAC,IAAI,EAAE;MAAE;QArDzBvB,OAAA,EAAAC,QAAA,CAsDU,MAMe,CANfH,YAAA,CAMe0B,uBAAA;UANDC,KAAK,EAAC;QAAM;UAtDpCzB,OAAA,EAAAC,QAAA,CAuDY,MAIY,CAJZH,YAAA,CAIY4B,oBAAA;YA3DxBC,UAAA,EAuDgCnB,MAAA,CAAAiC,MAAM,CAACC,SAAS;YAvDhD,uBAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuDgCtB,MAAA,CAAAiC,MAAM,CAACC,SAAS,GAAAZ,MAAA;YAAEI,KAAmB,EAAnB;cAAA;YAAA;;YAvDlDlC,OAAA,EAAAC,QAAA,CAwDc,MAAoC,CAApCH,YAAA,CAAoCyC,oBAAA;cAAzBd,KAAK,EAAC,IAAI;cAACe,KAAK,EAAC;gBAC5B1C,YAAA,CAAqCyC,oBAAA;cAA1Bd,KAAK,EAAC,IAAI;cAACe,KAAK,EAAC;gBAC5B1C,YAAA,CAAmCyC,oBAAA;cAAxBd,KAAK,EAAC,IAAI;cAACe,KAAK,EAAC;;YA1D1CnC,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA+DQR,mBAAA,WAAc,EACdC,YAAA,CAwBSwB,iBAAA;QAxBAC,IAAI,EAAE;MAAE;QAhEzBvB,OAAA,EAAAC,QAAA,CAiEU,MAsBe,CAtBfH,YAAA,CAsBe0B,uBAAA;UAtBDC,KAAK,EAAC;QAAW;UAjEzCzB,OAAA,EAAAC,QAAA,CAkEY,MAoBS,CApBTH,YAAA,CAoBSsB,iBAAA;YApBAC,MAAM,EAAE;UAAE;YAlE/BrB,OAAA,EAAAC,QAAA,CAmEc,MAQS,CARTH,YAAA,CAQSwB,iBAAA;cARAC,IAAI,EAAE;YAAE;cAnE/BvB,OAAA,EAAAC,QAAA,CAoEgB,MAME,CANFH,YAAA,CAME6C,0BAAA;gBA1ElBhB,UAAA,EAqE2BnB,MAAA,CAAAiC,MAAM,CAACG,KAAK;gBArEvC,uBAAAf,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqE2BtB,MAAA,CAAAiC,MAAM,CAACG,KAAK,GAAAd,MAAA;gBACpBe,GAAG,EAAE,EAAE;gBACPC,GAAG,EAAE,IAAI;gBACTC,IAAI,EAAE,EAAE;gBACTb,KAAmB,EAAnB;kBAAA;gBAAA;;cAzElB7B,CAAA;gBA4EcP,YAAA,CAAkDwB,iBAAA;cAAzCC,IAAI,EAAE,CAAC;cAAEhC,KAAK,EAAC;;cA5EtCS,OAAA,EAAAC,QAAA,CA4EsD,MAAC,CA5EvDK,gBAAA,CA4EsD,GAAC,E;cA5EvDD,CAAA;gBA6EcP,YAAA,CAQSwB,iBAAA;cARAC,IAAI,EAAE;YAAE;cA7E/BvB,OAAA,EAAAC,QAAA,CA8EgB,MAME,CANFH,YAAA,CAME6C,0BAAA;gBApFlBhB,UAAA,EA+E2BnB,MAAA,CAAAiC,MAAM,CAACO,KAAK;gBA/EvC,uBAAAnB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA+E2BtB,MAAA,CAAAiC,MAAM,CAACO,KAAK,GAAAlB,MAAA;gBACpBe,GAAG,EAAE,EAAE;gBACPC,GAAG,EAAE,IAAI;gBACTC,IAAI,EAAE,EAAE;gBACTb,KAAmB,EAAnB;kBAAA;gBAAA;;cAnFlB7B,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA0FQR,mBAAA,UAAa,EACbC,YAAA,CAcSwB,iBAAA;QAdAC,IAAI,EAAE;MAAE;QA3FzBvB,OAAA,EAAAC,QAAA,CA4FU,MAYe,CAZfH,YAAA,CAYe0B,uBAAA;UAZDC,KAAK,EAAC;QAAM;UA5FpCzB,OAAA,EAAAC,QAAA,CA6FY,MAUM,CAVNT,mBAAA,CAUM,OAVNyD,UAUM,GATJzD,mBAAA,CAIM,OAJN0D,UAIM,GAHJpD,YAAA,CAA4DqD,sBAAA;YA/F5ExB,UAAA,EA+FsCnB,MAAA,CAAAiC,MAAM,CAACW,UAAU;YA/FvD,uBAAAvB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA+FsCtB,MAAA,CAAAiC,MAAM,CAACW,UAAU,GAAAtB,MAAA;;YA/FvD9B,OAAA,EAAAC,QAAA,CA+FyD,MAAK,CA/F9DK,gBAAA,CA+FyD,OAAK,E;YA/F9DD,CAAA;6CAgGgBP,YAAA,CAA+DqD,sBAAA;YAhG/ExB,UAAA,EAgGsCnB,MAAA,CAAAiC,MAAM,CAACY,cAAc;YAhG3D,uBAAAxB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAgGsCtB,MAAA,CAAAiC,MAAM,CAACY,cAAc,GAAAvB,MAAA;;YAhG3D9B,OAAA,EAAAC,QAAA,CAgG6D,MAAI,CAhGjEK,gBAAA,CAgG6D,MAAI,E;YAhGjED,CAAA;6CAiGgBP,YAAA,CAAyDqD,sBAAA;YAjGzExB,UAAA,EAiGsCnB,MAAA,CAAAiC,MAAM,CAACa,QAAQ;YAjGrD,uBAAAzB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAiGsCtB,MAAA,CAAAiC,MAAM,CAACa,QAAQ,GAAAxB,MAAA;;YAjGrD9B,OAAA,EAAAC,QAAA,CAiGuD,MAAI,CAjG3DK,gBAAA,CAiGuD,MAAI,E;YAjG3DD,CAAA;+CAmGcb,mBAAA,CAGM,OAHN+D,UAGM,GAFJzD,YAAA,CAA2DqD,sBAAA;YApG3ExB,UAAA,EAoGsCnB,MAAA,CAAAiC,MAAM,CAACe,UAAU;YApGvD,uBAAA3B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoGsCtB,MAAA,CAAAiC,MAAM,CAACe,UAAU,GAAA1B,MAAA;;YApGvD9B,OAAA,EAAAC,QAAA,CAoGyD,MAAI,CApG7DK,gBAAA,CAoGyD,MAAI,E;YApG7DD,CAAA;6CAqGgBP,YAAA,CAAoEqD,sBAAA;YArGpFxB,UAAA,EAqGsCnB,MAAA,CAAAiC,MAAM,CAACgB,mBAAmB;YArGhE,uBAAA5B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqGsCtB,MAAA,CAAAiC,MAAM,CAACgB,mBAAmB,GAAA3B,MAAA;;YArGhE9B,OAAA,EAAAC,QAAA,CAqGkE,MAAI,CArGtEK,gBAAA,CAqGkE,MAAI,E;YArGtED,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA6GIR,mBAAA,YAAe,EACfC,YAAA,CAkEUC,kBAAA;IAlEDR,KAAK,EAAC;EAAa;IA9GhCS,OAAA,EAAAC,QAAA,CA+GM,MAgES,CAhETH,YAAA,CAgESsB,iBAAA;MAhEAC,MAAM,EAAE;IAAE;MA/GzBrB,OAAA,EAAAC,QAAA,CAgHQ,MAUS,CAVTH,YAAA,CAUSwB,iBAAA;QAVAC,IAAI,EAAE;MAAC;QAhHxBvB,OAAA,EAAAC,QAAA,CAiHU,MAQY,CARZH,YAAA,CAQY4D,oBAAA;UAPV5C,IAAI,EAAC,SAAS;UACb6C,OAAK,EAAEnD,MAAA,CAAAoD,WAAW;UAClBC,OAAO,EAAErD,MAAA,CAAAqD,OAAO,CAACzB,OAAO;UACzBF,KAAmB,EAAnB;YAAA;UAAA;;UArHZlC,OAAA,EAAAC,QAAA,CAuHY,MAA8B,CAA9BH,YAAA,CAA8BK,kBAAA;YAvH1CH,OAAA,EAAAC,QAAA,CAuHqB,MAAW,CAAXH,YAAA,CAAWgE,kBAAA,E;YAvHhCzD,CAAA;cAAAC,gBAAA,CAuH0C,QAEhC,E;UAzHVD,CAAA;;QAAAA,CAAA;UA2HQP,YAAA,CAWSwB,iBAAA;QAXAC,IAAI,EAAE;MAAC;QA3HxBvB,OAAA,EAAAC,QAAA,CA4HU,MASY,CATZH,YAAA,CASY4D,oBAAA;UARV5C,IAAI,EAAC,SAAS;UACb6C,OAAK,EAAEnD,MAAA,CAAAuD,SAAS;UAChBF,OAAO,EAAErD,MAAA,CAAAqD,OAAO,CAACG,IAAI;UACrBC,QAAQ,EAAEzD,MAAA,CAAAoB,cAAc,YAAYpB,MAAA,CAAAC,WAAW;UAChDyB,KAAmB,EAAnB;YAAA;UAAA;;UAjIZlC,OAAA,EAAAC,QAAA,CAmIY,MAA6B,CAA7BH,YAAA,CAA6BK,kBAAA;YAnIzCH,OAAA,EAAAC,QAAA,CAmIqB,MAAU,CAAVH,YAAA,CAAUM,iBAAA,E;YAnI/BC,CAAA;cAAAC,gBAAA,CAmIyC,QAE/B,E;UArIVD,CAAA;;QAAAA,CAAA;UAuIQP,YAAA,CAUSwB,iBAAA;QAVAC,IAAI,EAAE;MAAC;QAvIxBvB,OAAA,EAAAC,QAAA,CAwIU,MAQY,CARZH,YAAA,CAQY4D,oBAAA;UAPV5C,IAAI,EAAC,MAAM;UACV6C,OAAK,EAAEnD,MAAA,CAAA0D,WAAW;UAClBL,OAAO,EAAErD,MAAA,CAAAqD,OAAO,CAACM,MAAM;UACxBjC,KAAmB,EAAnB;YAAA;UAAA;;UA5IZlC,OAAA,EAAAC,QAAA,CA8IY,MAA8B,CAA9BH,YAAA,CAA8BK,kBAAA;YA9I1CH,OAAA,EAAAC,QAAA,CA8IqB,MAAW,CAAXH,YAAA,CAAWsE,kBAAA,E;YA9IhC/D,CAAA;cAAAC,gBAAA,CA8I0C,QAEhC,E;UAhJVD,CAAA;;QAAAA,CAAA;UAkJQP,YAAA,CAQSwB,iBAAA;QARAC,IAAI,EAAE;MAAC;QAlJxBvB,OAAA,EAAAC,QAAA,CAmJU,MAMY,CANZH,YAAA,CAMY4D,oBAAA;UALV5C,IAAI,EAAC,SAAS;UACb6C,OAAK,EAAEnD,MAAA,CAAA6D,QAAQ;UAChBnC,KAAmB,EAAnB;YAAA;UAAA;;UAtJZlC,OAAA,EAAAC,QAAA,CAuJW,MAED,CAzJVK,gBAAA,CAuJW,YAED,E;UAzJVD,CAAA;;QAAAA,CAAA;UA2JQP,YAAA,CASSwB,iBAAA;QATAC,IAAI,EAAE;MAAC;QA3JxBvB,OAAA,EAAAC,QAAA,CA4JU,MAOY,CAPZH,YAAA,CAOY4D,oBAAA;UANV5C,IAAI,EAAC,SAAS;UACb6C,OAAK,EAAEnD,MAAA,CAAA8D,gBAAgB;UACvBL,QAAQ,EAAEzD,MAAA,CAAA2D,MAAM,CAACI,MAAM;UACxBrC,KAAmB,EAAnB;YAAA;UAAA;;UAhKZlC,OAAA,EAAAC,QAAA,CAiKW,MAED,CAnKVK,gBAAA,CAiKW,WAED,E;UAnKVD,CAAA;;QAAAA,CAAA;UAqKQP,YAAA,CASSwB,iBAAA;QATAC,IAAI,EAAE;MAAC;QArKxBvB,OAAA,EAAAC,QAAA,CAsKU,MAOY,CAPZH,YAAA,CAOY4D,oBAAA;UANV5C,IAAI,EAAC,SAAS;UACb6C,OAAK,EAAEnD,MAAA,CAAAgE,MAAM;UACbP,QAAQ,EAAEzD,MAAA,CAAA2D,MAAM,CAACI,MAAM;UACxBrC,KAAmB,EAAnB;YAAA;UAAA;;UA1KZlC,OAAA,EAAAC,QAAA,CA2KW,MAED,CA7KVK,gBAAA,CA2KW,WAED,E;UA7KVD,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MAkLIR,mBAAA,cAAiB,EACkBW,MAAA,CAAA2D,MAAM,CAACI,MAAM,Q,cAAhD7D,YAAA,CAyHUX,kBAAA;IA5SdN,GAAA;IAmLaF,KAAK,EAAC;;IACFyB,MAAM,EAAAf,QAAA,CACf,MAqBM,CArBNT,mBAAA,CAqBM,OArBNiF,UAqBM,GApBJ3E,YAAA,CAA8BK,kBAAA;MAtLxCH,OAAA,EAAAC,QAAA,CAsLmB,MAAW,CAAXH,YAAA,CAAWsE,kBAAA,E;MAtL9B/D,CAAA;QAuLUb,mBAAA,CAA2C,cAArC,QAAM,GAAAkF,gBAAA,CAAGlE,MAAA,CAAA2D,MAAM,CAACI,MAAM,IAAG,OAAK,iBACpC/E,mBAAA,CAiBM,OAjBNmF,WAiBM,GAhBJ7E,YAAA,CAekB8E,0BAAA;MAxM9B5E,OAAA,EAAAC,QAAA,CA0Lc,MAMY,CANZH,YAAA,CAMY4D,oBAAA;QALT5C,IAAI,EAAEN,MAAA,CAAAqE,QAAQ;QACdlB,OAAK,EAAA9B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEtB,MAAA,CAAAqE,QAAQ;QAChBC,IAAI,EAAC;;QA7LrB9E,OAAA,EAAAC,QAAA,CA8Le,MAED,CAhMdK,gBAAA,CA8Le,SAED,E;QAhMdD,CAAA;mCAiMcP,YAAA,CAMY4D,oBAAA;QALT5C,IAAI,EAAEN,MAAA,CAAAqE,QAAQ;QACdlB,OAAK,EAAA9B,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEtB,MAAA,CAAAqE,QAAQ;QAChBC,IAAI,EAAC;;QApMrB9E,OAAA,EAAAC,QAAA,CAqMe,MAED,CAvMdK,gBAAA,CAqMe,SAED,E;QAvMdD,CAAA;;MAAAA,CAAA;;IAAAL,OAAA,EAAAC,QAAA,CA8MM,MA2CM,CA3CKO,MAAA,CAAAqE,QAAQ,e,cAAnBnF,mBAAA,CA2CM,OA3CNqF,WA2CM,I,kBA1CJrF,mBAAA,CAyCMC,SAAA,QAxPdwC,WAAA,CAgNmC3B,MAAA,CAAA2D,MAAM,EAhNzC,CAgNkBa,KAAK,EAAE1C,KAAK;2BADtB5C,mBAAA,CAyCM;QAvCHD,GAAG,EAAE6C,KAAK;QACX/C,KAAK,EAAC;UAENC,mBAAA,CAgCM,OAhCNyF,WAgCM,GA/BJzF,mBAAA,CAKE;QAJC0F,GAAG,EAAEF,KAAK,CAACE,GAAG;QACdC,GAAG,UAAU7C,KAAK;QAClBqB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA4E,YAAY,CAAC9C,KAAK;QAC1B/C,KAAK,EAAC;8BAzNpB8F,WAAA,GA2NY7F,mBAAA,CAwBM,OAxBN8F,WAwBM,GAvBJxF,YAAA,CAsBkB8E,0BAAA;QAlPhC5E,OAAA,EAAAC,QAAA,CA6NgB,MAMY,CANZH,YAAA,CAMY4D,oBAAA;UALV5C,IAAI,EAAC,SAAS;UACdgE,IAAI,EAAC,OAAO;UACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA4E,YAAY,CAAC9C,KAAK;;UAhO5CtC,OAAA,EAAAC,QAAA,CAiOiB,MAED,CAnOhBK,gBAAA,CAiOiB,OAED,E;UAnOhBD,CAAA;0DAoOgBP,YAAA,CAMY4D,oBAAA;UALV5C,IAAI,EAAC,SAAS;UACdgE,IAAI,EAAC,OAAO;UACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA+E,aAAa,CAACjD,KAAK;;UAvO7CtC,OAAA,EAAAC,QAAA,CAwOiB,MAED,CA1OhBK,gBAAA,CAwOiB,MAED,E;UA1OhBD,CAAA;0DA2OgBP,YAAA,CAMY4D,oBAAA;UALV5C,IAAI,EAAC,QAAQ;UACbgE,IAAI,EAAC,OAAO;UACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAAgF,WAAW,CAAClD,KAAK;;UA9O3CtC,OAAA,EAAAC,QAAA,CA+OiB,MAED,CAjPhBK,gBAAA,CA+OiB,OAED,E;UAjPhBD,CAAA;;QAAAA,CAAA;wCAqPUb,mBAAA,CAEM,OAFNiG,WAEM,GADJjG,mBAAA,CAAmD,QAAnDkG,WAAmD,EAAzB,KAAG,GAAAhB,gBAAA,CAAGpC,KAAK,qB;uDAM3C5C,mBAAA,CA+CWC,SAAA;MA3SjBF,GAAA;IAAA,IA2PMI,mBAAA,UAAa,EACbC,YAAA,CA+CW6F,mBAAA;MA/COC,IAAI,EAAEpF,MAAA,CAAA2D,MAAM;MAAE0B,MAAM,EAAN;;MA5PtC7F,OAAA,EAAAC,QAAA,CA6PQ,MAQkB,CARlBH,YAAA,CAQkBgG,0BAAA;QARDrE,KAAK,EAAC,IAAI;QAACsE,KAAK,EAAC;;QACrB/F,OAAO,EAAAC,QAAA,CAChB,CAIE;UALkB+F,GAAG;UAAEC;QAAM,OAC/BzG,mBAAA,CAIE;UAHC0F,GAAG,EAAEc,GAAG,CAACd,GAAG;UACb3F,KAAK,EAAC,iBAAiB;UACtBoE,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA4E,YAAY,CAACa,MAAM;gCAlQzCC,WAAA,E;QAAA7F,CAAA;UAsQQP,YAAA,CAIkBgG,0BAAA;QAJDrE,KAAK,EAAC,MAAM;QAAC0E,IAAI,EAAC,OAAO;QAACJ,KAAK,EAAC;;QACpC/F,OAAO,EAAAC,QAAA,CAAc,CAC3B;UADiBgG;QAAM,OAvQtC3F,gBAAA,CAuQ0C,MAC3B,GAAAoE,gBAAA,CAAGuB,MAAM,qB;QAxQxB5F,CAAA;UA2QQP,YAAA,CAIkBgG,0BAAA;QAJDrE,KAAK,EAAC,IAAI;QAACsE,KAAK,EAAC;;QACrB/F,OAAO,EAAAC,QAAA,CAChB,MAAiC,CAAjCH,YAAA,CAAiCsG,iBAAA;UAAzBtF,IAAI,EAAC;QAAM;UA7Q/Bd,OAAA,EAAAC,QAAA,CA6QgC,MAAI,CA7QpCK,gBAAA,CA6QgC,MAAI,E;UA7QpCD,CAAA;;QAAAA,CAAA;UAgRQP,YAAA,CA0BkBgG,0BAAA;QA1BDrE,KAAK,EAAC,IAAI;QAACsE,KAAK,EAAC;;QACrB/F,OAAO,EAAAC,QAAA,CAChB,CAsBkB;UAvBEgG;QAAM,OAC1BnG,YAAA,CAsBkB8E,0BAAA;UAxS9B5E,OAAA,EAAAC,QAAA,CAmRc,MAMY,CANZH,YAAA,CAMY4D,oBAAA;YALV5C,IAAI,EAAC,SAAS;YACdgE,IAAI,EAAC,OAAO;YACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA4E,YAAY,CAACa,MAAM;;YAtR3CjG,OAAA,EAAAC,QAAA,CAuRe,MAED,CAzRdK,gBAAA,CAuRe,UAED,E;YAzRdD,CAAA;4DA0RcP,YAAA,CAMY4D,oBAAA;YALV5C,IAAI,EAAC,SAAS;YACdgE,IAAI,EAAC,OAAO;YACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAA+E,aAAa,CAACU,MAAM;;YA7R5CjG,OAAA,EAAAC,QAAA,CA8Re,MAED,CAhSdK,gBAAA,CA8Re,SAED,E;YAhSdD,CAAA;4DAiScP,YAAA,CAMY4D,oBAAA;YALV5C,IAAI,EAAC,QAAQ;YACbgE,IAAI,EAAC,OAAO;YACXnB,OAAK,EAAA7B,MAAA,IAAEtB,MAAA,CAAAgF,WAAW,CAACS,MAAM;;YApS1CjG,OAAA,EAAAC,QAAA,CAqSe,MAED,CAvSdK,gBAAA,CAqSe,UAED,E;YAvSdD,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;uBA+SIX,mBAAA,CAYWC,SAAA;IA3TfF,GAAA;EAAA,IA8SII,mBAAA,SAAY,EACZC,YAAA,CAYWuG,mBAAA;IAVTxF,WAAW,EAAC,QAAQ;IACpBtB,KAAK,EAAC;;IAlTZS,OAAA,EAAAC,QAAA,CAoTM,MAMY,CANZH,YAAA,CAMY4D,oBAAA;MALV5C,IAAI,EAAC,SAAS;MACb6C,OAAK,EAAEnD,MAAA,CAAAuD,SAAS;MAChBE,QAAQ,EAAEzD,MAAA,CAAAoB,cAAc,YAAYpB,MAAA,CAAAC;;MAvT7CT,OAAA,EAAAC,QAAA,CAwTO,MAED,CA1TNK,gBAAA,CAwTO,QAED,E;MA1TND,CAAA;;IAAAA,CAAA;0DA8TER,mBAAA,WAAc,EACdC,YAAA,CAqCYwG,oBAAA;IApWd3E,UAAA,EAgUanB,MAAA,CAAA+F,YAAY,CAACC,OAAO;IAhUjC,uBAAA3E,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAgUatB,MAAA,CAAA+F,YAAY,CAACC,OAAO,GAAA1E,MAAA;IAC7BlB,KAAK,EAAC,MAAM;IACZmF,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE;;IAuBZU,MAAM,EAAAxG,QAAA,CACf,MAA+D,CAA/DH,YAAA,CAA+D4D,oBAAA;MAAnDC,OAAK,EAAA9B,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEtB,MAAA,CAAA+F,YAAY,CAACC,OAAO;;MA3V7CxG,OAAA,EAAAC,QAAA,CA2VuD,MAAE,CA3VzDK,gBAAA,CA2VuD,IAAE,E;MA3VzDD,CAAA;QA4VMP,YAAA,CAMY4D,oBAAA;MALV5C,IAAI,EAAC,SAAS;MACb6C,OAAK,EAAEnD,MAAA,CAAAkG,aAAa;MACpB7C,OAAO,EAAErD,MAAA,CAAAqD,OAAO,CAAC8C;;MA/V1B3G,OAAA,EAAAC,QAAA,CAgWO,MAED,CAlWNK,gBAAA,CAgWO,QAED,E;MAlWND,CAAA;;IAAAL,OAAA,EAAAC,QAAA,CAqUI,MAmBU,CAnBVH,YAAA,CAmBU8G,kBAAA;MAnBAC,KAAK,EAAErG,MAAA,CAAA+F,YAAY,CAACO,IAAI;MAAE,aAAW,EAAC;;MArUpD9G,OAAA,EAAAC,QAAA,CAsUM,MAEe,CAFfH,YAAA,CAEe0B,uBAAA;QAFDC,KAAK,EAAC;MAAM;QAtUhCzB,OAAA,EAAAC,QAAA,CAuUQ,MAAiE,CAAjEH,YAAA,CAAiEiH,mBAAA;UAvUzEpF,UAAA,EAuU2BnB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACE,EAAE;UAvU/C,uBAAAnF,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAuU2BtB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACE,EAAE,GAAAlF,MAAA;UAAEG,WAAW,EAAC;;QAvU7D5B,CAAA;UAyUMP,YAAA,CAOe0B,uBAAA;QAPDC,KAAK,EAAC;MAAM;QAzUhCzB,OAAA,EAAAC,QAAA,CA0UQ,MAKE,CALFH,YAAA,CAKEiH,mBAAA;UA/UVpF,UAAA,EA2UmBnB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACjG,WAAW;UA3UhD,uBAAAgB,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA2UmBtB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACjG,WAAW,GAAAiB,MAAA;UACtChB,IAAI,EAAC,UAAU;UACdmG,IAAI,EAAE,CAAC;UACRhF,WAAW,EAAC;;QA9UtB5B,CAAA;UAiVMP,YAAA,CAMe0B,uBAAA;QANDC,KAAK,EAAC;MAAM;QAjVhCzB,OAAA,EAAAC,QAAA,CAkVQ,MAIiB,CAJjBH,YAAA,CAIiBoH,yBAAA;UAtVzBvF,UAAA,EAkViCnB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACK,MAAM;UAlVzD,uBAAAtF,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAkViCtB,MAAA,CAAA+F,YAAY,CAACO,IAAI,CAACK,MAAM,GAAArF,MAAA;;UAlVzD9B,OAAA,EAAAC,QAAA,CAmVU,MAAsC,CAAtCH,YAAA,CAAsCsH,mBAAA;YAA5B3F,KAAK,EAAC;UAAK;YAnV/BzB,OAAA,EAAAC,QAAA,CAmVgC,MAAK,CAnVrCK,gBAAA,CAmVgC,OAAK,E;YAnVrCD,CAAA;cAoVUP,YAAA,CAAwCsH,mBAAA;YAA9B3F,KAAK,EAAC;UAAM;YApVhCzB,OAAA,EAAAC,QAAA,CAoViC,MAAM,CApVvCK,gBAAA,CAoViC,QAAM,E;YApVvCD,CAAA;cAqVUP,YAAA,CAAsCsH,mBAAA;YAA5B3F,KAAK,EAAC;UAAK;YArV/BzB,OAAA,EAAAC,QAAA,CAqVgC,MAAK,CArVrCK,gBAAA,CAqVgC,OAAK,E;YArVrCD,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;qCAsWER,mBAAA,aAAgB,EAChBC,YAAA,CAyCYwG,oBAAA;IAhZd3E,UAAA,EAwWanB,MAAA,CAAA6G,aAAa,CAACb,OAAO;IAxWlC,uBAAA3E,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAwWatB,MAAA,CAAA6G,aAAa,CAACb,OAAO,GAAA1E,MAAA;IAC9BlB,KAAK,EAAC,MAAM;IACZmF,KAAK,EAAC,KAAK;IACV,sBAAoB,EAAE;;IASZU,MAAM,EAAAxG,QAAA,CACf,MAyBM,CAzBNT,mBAAA,CAyBM,OAzBN8H,WAyBM,GAxBJ9H,mBAAA,CAEM,OAFN+H,WAEM,EAFoB,MACrB,GAAA7C,gBAAA,CAAGlE,MAAA,CAAA6G,aAAa,CAACG,YAAY,QAAO,KAAG,GAAA9C,gBAAA,CAAGlE,MAAA,CAAA2D,MAAM,CAACI,MAAM,kBAE5D/E,mBAAA,CAoBM,OApBNiI,WAoBM,GAnBJ3H,YAAA,CAKY4D,oBAAA;MAJTC,OAAK,EAAEnD,MAAA,CAAAkH,SAAS;MAChBzD,QAAQ,EAAEzD,MAAA,CAAA6G,aAAa,CAACG,YAAY;;MA5XjDxH,OAAA,EAAAC,QAAA,CA6XW,MAED,CA/XVK,gBAAA,CA6XW,UAED,E;MA/XVD,CAAA;gDAgYUP,YAAA,CAKY4D,oBAAA;MAJTC,OAAK,EAAEnD,MAAA,CAAAmH,SAAS;MAChB1D,QAAQ,EAAEzD,MAAA,CAAA6G,aAAa,CAACG,YAAY,IAAIhH,MAAA,CAAA2D,MAAM,CAACI,MAAM;;MAlYlEvE,OAAA,EAAAC,QAAA,CAmYW,MAED,CArYVK,gBAAA,CAmYW,UAED,E;MArYVD,CAAA;gDAsYUP,YAAA,CAKY4D,oBAAA;MAJV5C,IAAI,EAAC,SAAS;MACb6C,OAAK,EAAEnD,MAAA,CAAAoH;;MAxYpB5H,OAAA,EAAAC,QAAA,CAyYW,MAED,CA3YVK,gBAAA,CAyYW,SAED,E;MA3YVD,CAAA;oCA4YUP,YAAA,CAAgE4D,oBAAA;MAApDC,OAAK,EAAA9B,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEtB,MAAA,CAAA6G,aAAa,CAACb,OAAO;;MA5YlDxG,OAAA,EAAAC,QAAA,CA4Y4D,MAAE,CA5Y9DK,gBAAA,CA4Y4D,IAAE,E;MA5Y9DD,CAAA;;IAAAL,OAAA,EAAAC,QAAA,CA6WI,MAMM,CAN+BO,MAAA,CAAA6G,aAAa,CAACQ,YAAY,I,cAA/DnI,mBAAA,CAMM,OANNoI,WAMM,GALJtI,mBAAA,CAIE;MAHC0F,GAAG,EAAE1E,MAAA,CAAA6G,aAAa,CAACQ,YAAY,CAAC3C,GAAG;MACpC3F,KAAK,EAAC,eAAe;MACpB4F,GAAG,UAAU3E,MAAA,CAAA6G,aAAa,CAACG,YAAY;4BAjXhDO,WAAA,E,KAAAlI,mBAAA,e;IAAAQ,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
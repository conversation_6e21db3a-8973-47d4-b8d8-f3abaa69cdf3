# ScanOnWeb Rust Server

🦀 高性能Rust后台服务，用于接收Vue3前端页面提交的上传数据。采用现代Rust技术栈，提供极致的性能和内存安全保证，完全兼容Go、SpringBoot、Servlet版本的API。

## 🎯 设计理念

- **高性能**: 基于Tokio异步运行时，极致的并发性能
- **内存安全**: Rust语言特性保证内存安全，零成本抽象
- **API兼容**: 与Go、SpringBoot、Servlet版本完全兼容
- **现代化**: 使用最新的Rust生态系统工具

## ✅ 功能特性

- 📤 支持PDF、TIFF、JPG格式文件上传
- 🔄 支持multipart/form-data和JSON两种上传方式
- 🌐 CORS跨域支持
- 📁 文件下载和管理功能
- 💊 健康检查接口
- 🚀 异步处理，高并发支持
- 🛡️ 内存安全，无数据竞争

## 🔧 技术栈

- **Rust**: 2021 Edition
- **Web框架**: Axum 0.7 (基于Tokio)
- **异步运行时**: Tokio
- **序列化**: Serde + serde_json
- **文件上传**: Axum内置multipart支持
- **Base64**: base64 crate
- **日志**: tracing + tracing-subscriber
- **错误处理**: anyhow + thiserror

## 🚀 快速开始

### 环境要求

- Rust 1.70 或更高版本
- Cargo (随Rust安装)

### 安装Rust

如果还没有安装Rust，请访问 [https://rustup.rs/](https://rustup.rs/) 安装。

```bash
# Windows
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Linux/Mac
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 启动方式

#### 方式1: 使用启动脚本（推荐）
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

#### 方式2: 使用Cargo命令
```bash
# 开发模式启动
cargo run

# 发布模式启动（性能更好）
cargo run --release

# 构建可执行文件
cargo build --release
./target/release/scanonweb-server
```

### 访问地址

- **服务地址**: http://localhost:8080
- **健康检查**: http://localhost:8080/health
- **测试页面**: 打开 `test-rust.html`

## 📋 API接口

### 1. 健康检查
```
GET /health
```

**响应示例**:
```json
{
  "status": "ok",
  "time": "2024-01-01T12:00:00Z",
  "service": "ScanOnWeb Rust Server",
  "version": "1.0.0"
}
```

### 2. PDF上传
```
POST /upload
Content-Type: multipart/form-data 或 application/json
```

### 3. TIFF上传
```
POST /upload-tiff
```

### 4. JPG上传
```
POST /upload-jpg
```

### 5. 文件下载
```
GET /uploads/{filename}
```

### 6. 文件列表
```
GET /files
```

### 7. 删除文件
```
DELETE /files/{filename}
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "PDF文件上传成功",
  "code": 200,
  "fileId": "scan_123456",
  "path": "/uploads/scan_123456_1701234567890.pdf",
  "data": {
    "filename": "scan_123456_1701234567890.pdf",
    "originalName": "document.pdf",
    "size": 1024000,
    "id": "scan_123456",
    "format": "pdf"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "缺少上传文件",
  "code": 400
}
```

## 📁 项目结构

```
rust/
├── src/
│   ├── main.rs                    # 🚀 主程序入口
│   ├── models.rs                  # 📋 数据模型
│   ├── handlers/                  # 🎯 请求处理器
│   │   ├── mod.rs
│   │   ├── health_handler.rs      # 💊 健康检查
│   │   ├── upload_handler.rs      # 📤 文件上传
│   │   └── file_handler.rs        # 📁 文件管理
│   └── utils/                     # 🛠️ 工具模块
│       ├── mod.rs
│       └── file_utils.rs          # 📂 文件工具
├── Cargo.toml                     # 📦 项目配置
├── start.bat                      # 🪟 Windows启动脚本
├── start.sh                       # 🐧 Linux/Mac启动脚本
├── test-rust.html                 # 🧪 测试页面
└── README.md                      # 📖 说明文档
```

## 🔄 版本对比

| 特性 | Rust版本 | Go版本 | SpringBoot版本 | Servlet版本 |
|------|----------|--------|---------------|------------|
| **性能** | ⭐⭐⭐⭐⭐ 极致 | ⭐⭐⭐⭐ 很高 | ⭐⭐⭐ 高 | ⭐⭐⭐ 高 |
| **内存占用** | ⭐⭐⭐⭐⭐ 极低 | ⭐⭐⭐⭐ 很低 | ⭐⭐ 中等 | ⭐⭐⭐ 低 |
| **启动速度** | ⭐⭐⭐⭐ 很快 | ⭐⭐⭐⭐⭐ 极快 | ⭐⭐ 中等 | ⭐⭐⭐ 快 |
| **并发能力** | ⭐⭐⭐⭐⭐ 极强 | ⭐⭐⭐⭐ 很强 | ⭐⭐⭐ 强 | ⭐⭐⭐ 强 |
| **内存安全** | ⭐⭐⭐⭐⭐ 编译时保证 | ⭐⭐⭐ 运行时检查 | ⭐⭐⭐ JVM保证 | ⭐⭐⭐ JVM保证 |
| **学习成本** | ⭐⭐ 较高 | ⭐⭐⭐ 中等 | ⭐⭐⭐⭐ 较低 | ⭐⭐⭐⭐ 较低 |
| **生态系统** | ⭐⭐⭐ 快速发展 | ⭐⭐⭐⭐ 成熟 | ⭐⭐⭐⭐⭐ 非常成熟 | ⭐⭐⭐⭐⭐ 非常成熟 |

## 🎯 适用场景

### 选择Rust版本的理由：
- ✅ 需要极致的性能
- ✅ 高并发场景
- ✅ 内存使用敏感
- ✅ 长期运行的服务
- ✅ 微服务架构
- ✅ 学习现代系统编程

### 性能优势：
- **零成本抽象**: 编译时优化，运行时无额外开销
- **无GC**: 没有垃圾回收暂停
- **异步I/O**: 基于Tokio的高效异步处理
- **内存安全**: 编译时防止内存泄漏和数据竞争

## 🛠️ 开发说明

### 添加新的上传格式

1. 在 `upload_handler.rs` 中添加新的处理函数
2. 在 `main.rs` 中添加路由映射

示例：
```rust
/// PNG上传处理器
pub async fn upload_png(
    headers: HeaderMap,
    request: Request,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    handle_upload_request(headers, request, "png").await
}
```

### 自定义配置

修改 `file_utils.rs` 中的配置常量：
```rust
const UPLOAD_DIR: &str = "./uploads";
```

### 日志配置

通过环境变量控制日志级别：
```bash
# 设置日志级别
export RUST_LOG=debug
cargo run

# 或者在启动时设置
RUST_LOG=info cargo run
```

## 🔧 故障排除

### 常见问题

1. **编译错误**: 确保Rust版本 >= 1.70
2. **端口冲突**: 修改 `main.rs` 中的端口号
3. **文件权限**: 确保有创建 `uploads` 目录的权限
4. **依赖问题**: 运行 `cargo clean` 后重新构建

### 性能调优

1. **发布模式**: 使用 `cargo run --release`
2. **编译优化**: 在 `Cargo.toml` 中添加优化选项
3. **内存配置**: 根据需要调整文件大小限制

## 🚀 部署建议

### Docker部署
```dockerfile
FROM rust:1.70 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
COPY --from=builder /app/target/release/scanonweb-server /usr/local/bin/
EXPOSE 8080
CMD ["scanonweb-server"]
```

### 系统服务
```ini
[Unit]
Description=ScanOnWeb Rust Server
After=network.target

[Service]
Type=simple
User=www-data
ExecStart=/usr/local/bin/scanonweb-server
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📄 许可证

MIT License

---

**🦀 高性能、内存安全、现代化** - ScanOnWeb Rust版本为您提供极致性能的文档上传解决方案！

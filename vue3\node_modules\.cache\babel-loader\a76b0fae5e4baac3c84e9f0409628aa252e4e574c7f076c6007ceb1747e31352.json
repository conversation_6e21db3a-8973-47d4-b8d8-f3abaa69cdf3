{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function (iteratee) {\n      if (isArray(iteratee)) {\n        return function (value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        };\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n  var result = baseMap(collection, function (value, key, collection) {\n    var criteria = arrayMap(iteratees, function (iteratee) {\n      return iteratee(value);\n    });\n    return {\n      'criteria': criteria,\n      'index': ++index,\n      'value': value\n    };\n  });\n  return baseSortBy(result, function (object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\nexport default baseOrderBy;", "map": {"version": 3, "names": ["arrayMap", "baseGet", "baseIteratee", "baseMap", "baseSortBy", "baseUnary", "compareMultiple", "identity", "isArray", "baseOrderBy", "collection", "iteratees", "orders", "length", "iteratee", "value", "index", "result", "key", "criteria", "object", "other"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_baseOrderBy.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseGet from './_baseGet.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseMap from './_baseMap.js';\nimport baseSortBy from './_baseSortBy.js';\nimport baseUnary from './_baseUnary.js';\nimport compareMultiple from './_compareMultiple.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\n\n/**\n * The base implementation of `_.orderBy` without param guards.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.\n * @param {string[]} orders The sort orders of `iteratees`.\n * @returns {Array} Returns the new sorted array.\n */\nfunction baseOrderBy(collection, iteratees, orders) {\n  if (iteratees.length) {\n    iteratees = arrayMap(iteratees, function(iteratee) {\n      if (isArray(iteratee)) {\n        return function(value) {\n          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);\n        }\n      }\n      return iteratee;\n    });\n  } else {\n    iteratees = [identity];\n  }\n\n  var index = -1;\n  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n\n  var result = baseMap(collection, function(value, key, collection) {\n    var criteria = arrayMap(iteratees, function(iteratee) {\n      return iteratee(value);\n    });\n    return { 'criteria': criteria, 'index': ++index, 'value': value };\n  });\n\n  return baseSortBy(result, function(object, other) {\n    return compareMultiple(object, other, orders);\n  });\n}\n\nexport default baseOrderBy;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAEC,SAAS,EAAEC,MAAM,EAAE;EAClD,IAAID,SAAS,CAACE,MAAM,EAAE;IACpBF,SAAS,GAAGX,QAAQ,CAACW,SAAS,EAAE,UAASG,QAAQ,EAAE;MACjD,IAAIN,OAAO,CAACM,QAAQ,CAAC,EAAE;QACrB,OAAO,UAASC,KAAK,EAAE;UACrB,OAAOd,OAAO,CAACc,KAAK,EAAED,QAAQ,CAACD,MAAM,KAAK,CAAC,GAAGC,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC;QACvE,CAAC;MACH;MACA,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,MAAM;IACLH,SAAS,GAAG,CAACJ,QAAQ,CAAC;EACxB;EAEA,IAAIS,KAAK,GAAG,CAAC,CAAC;EACdL,SAAS,GAAGX,QAAQ,CAACW,SAAS,EAAEN,SAAS,CAACH,YAAY,CAAC,CAAC;EAExD,IAAIe,MAAM,GAAGd,OAAO,CAACO,UAAU,EAAE,UAASK,KAAK,EAAEG,GAAG,EAAER,UAAU,EAAE;IAChE,IAAIS,QAAQ,GAAGnB,QAAQ,CAACW,SAAS,EAAE,UAASG,QAAQ,EAAE;MACpD,OAAOA,QAAQ,CAACC,KAAK,CAAC;IACxB,CAAC,CAAC;IACF,OAAO;MAAE,UAAU,EAAEI,QAAQ;MAAE,OAAO,EAAE,EAAEH,KAAK;MAAE,OAAO,EAAED;IAAM,CAAC;EACnE,CAAC,CAAC;EAEF,OAAOX,UAAU,CAACa,MAAM,EAAE,UAASG,MAAM,EAAEC,KAAK,EAAE;IAChD,OAAOf,eAAe,CAACc,MAAM,EAAEC,KAAK,EAAET,MAAM,CAAC;EAC/C,CAAC,CAAC;AACJ;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport arrayMap from './_arrayMap.js';\nimport baseProperty from './_baseProperty.js';\nimport baseTimes from './_baseTimes.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.zip` except that it accepts an array of grouped\n * elements and creates an array regrouping the elements to their pre-zip\n * configuration.\n *\n * @static\n * @memberOf _\n * @since 1.2.0\n * @category Array\n * @param {Array} array The array of grouped elements to process.\n * @returns {Array} Returns the new array of regrouped elements.\n * @example\n *\n * var zipped = _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n *\n * _.unzip(zipped);\n * // => [['a', 'b'], [1, 2], [true, false]]\n */\nfunction unzip(array) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  var length = 0;\n  array = arrayFilter(array, function (group) {\n    if (isArrayLikeObject(group)) {\n      length = nativeMax(group.length, length);\n      return true;\n    }\n  });\n  return baseTimes(length, function (index) {\n    return arrayMap(array, baseProperty(index));\n  });\n}\nexport default unzip;", "map": {"version": 3, "names": ["arrayFilter", "arrayMap", "baseProperty", "baseTimes", "isArrayLikeObject", "nativeMax", "Math", "max", "unzip", "array", "length", "group", "index"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/unzip.js"], "sourcesContent": ["import arrayFilter from './_arrayFilter.js';\nimport arrayMap from './_arrayMap.js';\nimport baseProperty from './_baseProperty.js';\nimport baseTimes from './_baseTimes.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * This method is like `_.zip` except that it accepts an array of grouped\n * elements and creates an array regrouping the elements to their pre-zip\n * configuration.\n *\n * @static\n * @memberOf _\n * @since 1.2.0\n * @category Array\n * @param {Array} array The array of grouped elements to process.\n * @returns {Array} Returns the new array of regrouped elements.\n * @example\n *\n * var zipped = _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n *\n * _.unzip(zipped);\n * // => [['a', 'b'], [1, 2], [true, false]]\n */\nfunction unzip(array) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  var length = 0;\n  array = arrayFilter(array, function(group) {\n    if (isArrayLikeObject(group)) {\n      length = nativeMax(group.length, length);\n      return true;\n    }\n  });\n  return baseTimes(length, function(index) {\n    return arrayMap(array, baseProperty(index));\n  });\n}\n\nexport default unzip;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,iBAAiB,MAAM,wBAAwB;;AAEtD;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,IAAI,EAAEA,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC,EAAE;IAC5B,OAAO,EAAE;EACX;EACA,IAAIA,MAAM,GAAG,CAAC;EACdD,KAAK,GAAGT,WAAW,CAACS,KAAK,EAAE,UAASE,KAAK,EAAE;IACzC,IAAIP,iBAAiB,CAACO,KAAK,CAAC,EAAE;MAC5BD,MAAM,GAAGL,SAAS,CAACM,KAAK,CAACD,MAAM,EAAEA,MAAM,CAAC;MACxC,OAAO,IAAI;IACb;EACF,CAAC,CAAC;EACF,OAAOP,SAAS,CAACO,MAAM,EAAE,UAASE,KAAK,EAAE;IACvC,OAAOX,QAAQ,CAACQ,KAAK,EAAEP,YAAY,CAACU,KAAK,CAAC,CAAC;EAC7C,CAAC,CAAC;AACJ;AAEA,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
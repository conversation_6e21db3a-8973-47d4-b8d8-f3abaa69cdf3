<!-- src/components/ScanOnWeb.vue -->
<template>
    <div>
      <form>
        <div>
          <select v-model="selectedDevice" @change="onDeviceChange">
            <option v-for="(device, index) in devices" :key="index" :value="index">
              {{ device }}
            </option>
          </select>
          <label>分辨率</label> 
          <input type="text" v-model="config.dpi_x" style="width: 25px;" /> 
          <label>X</label> 
          <input type="text" v-model="config.dpi_y" style="width: 25px;" />
          <label>色彩模式</label>
          <select v-model="config.colorMode">
            <option value="RGB">彩色</option>
            <option value="GRAY">灰色</option>
            <option value="BW">黑白</option>
          </select>
          <label>是否显示设备内置对话框</label>
          <select v-model="config.showDialog">
            <option value="true">显示</option>
            <option value="false">不显示</option>
          </select>
          <label>自动进纸模式</label>
          <select v-model="config.autoFeedEnable">
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
          <label>自动装填纸张</label>
          <select v-model="config.autoFeed">
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
          <label>双面模式</label>
          <select v-model="config.dupxMode">
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
          <label>自动纠偏</label>
          <select v-model="config.autoDeskew">
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
          <label>自动边框检测</label>
          <select v-model="config.autoBorderDetection">
            <option value="true">是</option>
            <option value="false">否</option>
          </select>
        </div>
        <div id="imageList">
          <img v-for="(image, index) in images" :key="index" :src="image.src" :alt="'Scanned Image ' + index" width="300" height="300" :data-index="index" />
        </div>
        <input type="button" value="获取设备列表" @click="loadDevices" />
        <input type="button" value="开始扫描" @click="startScan" />
        <input type="button" value="清空扫描结果" @click="clearAll" />
        <input type="button" value="获取所有图像" @click="getAllImage" />
        <input type="button" value="上传结果" @click="uploadAllImageAsPdfFormat" />
        <input type="button" value="上传结果2" @click="uploadImageFromDom" />
        <input type="button" value="显示界面" @click="focusService" />
        <input type="button" value="隐藏界面" @click="hideService" />
        <input type="button" value="本地另存" @click="saveAs" />
      </form>
    </div>
  </template>
  
  <script>
  import ScanOnWeb from '@/scanonweb.js';
  
  export default {
    data() {
      return {
        scanonweb: null,
        devices: [],
        selectedDevice: 0,
        config: {
          dpi_x: 300,
          dpi_y: 300,
          colorMode: 'RGB',
          showDialog: false,
          autoFeedEnable: false,
          autoFeed: false,
          dupxMode: false,
          autoDeskew: false,
          autoBorderDetection: false,
        },
        images: []
      };
    },
    methods: {
      loadDevices() {
        this.scanonweb.onGetDevicesListEvent = (msg) => {
          this.devices = msg.devices;
          this.selectedDevice = msg.currentIndex;
        };
        this.scanonweb.loadDevices();
      },
      startScan() {
        if (this.selectedDevice === -1) {
          alert('请先刷新或者选中要使用的扫描设备后再开始扫描!');
          return;
        }
        this.scanonweb.scaner_work_config = this.config;
        this.scanonweb.scaner_work_config.deviceIndex = this.selectedDevice;
        this.scanonweb.startScan();
      },
      clearAll() {
        this.scanonweb.clearAll();
        this.images = [];
      },
      getAllImage() {
        this.scanonweb.onGetAllImageEvent = (msg) => {
          this.images = msg.images.map((image, index) => ({
            src: `data:image/jpg;base64,${image}`,
            index: index
          }));
        };
        this.scanonweb.getAllImage();
      },
      uploadAllImageAsPdfFormat() {
        this.scanonweb.uploadAllImageAsPdfToUrl('http://localhost:8080/upload', '1234', 'test');
      },
      uploadImageFromDom() {
        // Implementation same as provided in your HTML file
      },
      focusService() {
        this.scanonweb.setFocus();
      },
      hideService() {
        this.scanonweb.hidden();
      },
      saveAs() {
        this.scanonweb.saveAllImageToLocal("d:/test.pdf");
      },
      onDeviceChange(event) {
        this.scanonweb.selectScanDevice(event.target.value);
      },
      addImage(imageBase64) {
        this.images.push({ src: `data:image/jpg;base64,${imageBase64}`, index: this.images.length });
      },
      editImage(imageIndex, imageBase64) {
        const imageSrc = `data:image/jpg;base64,${imageBase64}`;
        const imageElement = this.images.find(image => image.index === imageIndex);
        if (imageElement) {
          imageElement.src = imageSrc;
        }
      }
    },
    mounted() {
      this.scanonweb = new ScanOnWeb();
      this.loadDevices();
      this.scanonweb.onScanFinishedEvent = (msg) => {
        console.log("扫描完成事件回调,扫描前:", msg.imageBeforeCount, " 扫描后:", msg.imageAfterCount);
        this.getAllImage();
      };
      this.scanonweb.onGetImageByIdEvent = (msg) => {
        this.addImage(msg.imageBase64);
      };
      this.scanonweb.onImageEditedEvent = (msg) => {
        console.log("图像编辑事件回调,图像id:", msg.imageIndex);
        this.editImage(msg.imageIndex, msg.imageBase64);
      };
      this.scanonweb.onGetImageCountEvent = (msg) => {
        console.log("图像总数:", msg.imageCount, " 当前编辑图像索引:", msg.currentSelected);
      };
      this.scanonweb.onGetAllImageEvent = (msg) => {
        console.log("图像总数:", msg.imageCount, " 当前选中编辑的图像id:", msg.currentSelected);
        this.images = msg.images.map((image, index) => ({
          src: `data:image/jpg;base64,${image}`,
          index: index
        }));
      };
      this.scanonweb.onUploadEvent = (msg) => {
        console.log("用户点击了开始上传按钮,当前图像总数:", msg.imageCount);
        this.uploadAllImageAsPdfFormat();
      };
    }
  };
  </script>
  
  <style scoped>
  /* 添加样式以适应组件 */
  </style>
  
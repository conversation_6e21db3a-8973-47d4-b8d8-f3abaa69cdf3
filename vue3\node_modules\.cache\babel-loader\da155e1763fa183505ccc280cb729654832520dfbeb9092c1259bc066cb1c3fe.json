{"ast": null, "code": "/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n    result = Array(n);\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\nexport default baseTimes;", "map": {"version": 3, "names": ["baseTimes", "n", "iteratee", "index", "result", "Array"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_baseTimes.js"], "sourcesContent": ["/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\nexport default baseTimes;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,CAAC,EAAEC,QAAQ,EAAE;EAC9B,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACJ,CAAC,CAAC;EAErB,OAAO,EAAEE,KAAK,GAAGF,CAAC,EAAE;IAClBG,MAAM,CAACD,KAAK,CAAC,GAAGD,QAAQ,CAACC,KAAK,CAAC;EACjC;EACA,OAAOC,MAAM;AACf;AAEA,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
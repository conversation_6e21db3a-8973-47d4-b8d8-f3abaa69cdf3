{"ast": null, "code": "import toNumber from './toNumber.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n  MAX_INTEGER = 1.7976931348623157e+308;\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = value < 0 ? -1 : 1;\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\nexport default toFinite;", "map": {"version": 3, "names": ["toNumber", "INFINITY", "MAX_INTEGER", "toFinite", "value", "sign"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/toFinite.js"], "sourcesContent": ["import toNumber from './toNumber.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0,\n    MAX_INTEGER = 1.7976931348623157e+308;\n\n/**\n * Converts `value` to a finite number.\n *\n * @static\n * @memberOf _\n * @since 4.12.0\n * @category Lang\n * @param {*} value The value to convert.\n * @returns {number} Returns the converted number.\n * @example\n *\n * _.toFinite(3.2);\n * // => 3.2\n *\n * _.toFinite(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toFinite(Infinity);\n * // => 1.7976931348623157e+308\n *\n * _.toFinite('3.2');\n * // => 3.2\n */\nfunction toFinite(value) {\n  if (!value) {\n    return value === 0 ? value : 0;\n  }\n  value = toNumber(value);\n  if (value === INFINITY || value === -INFINITY) {\n    var sign = (value < 0 ? -1 : 1);\n    return sign * MAX_INTEGER;\n  }\n  return value === value ? value : 0;\n}\n\nexport default toFinite;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;EAChBC,WAAW,GAAG,uBAAuB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAI,CAACA,KAAK,EAAE;IACV,OAAOA,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC;EAChC;EACAA,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC;EACvB,IAAIA,KAAK,KAAKH,QAAQ,IAAIG,KAAK,KAAK,CAACH,QAAQ,EAAE;IAC7C,IAAII,IAAI,GAAID,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;IAC/B,OAAOC,IAAI,GAAGH,WAAW;EAC3B;EACA,OAAOE,KAAK,KAAKA,KAAK,GAAGA,KAAK,GAAG,CAAC;AACpC;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
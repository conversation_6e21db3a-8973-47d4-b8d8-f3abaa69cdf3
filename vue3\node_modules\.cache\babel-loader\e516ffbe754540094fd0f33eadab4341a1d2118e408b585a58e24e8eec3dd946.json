{"ast": null, "code": "import eq from './eq.js';\nimport isArrayLike from './isArrayLike.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number' ? isArrayLike(object) && isIndex(index, object.length) : type == 'string' && index in object) {\n    return eq(object[index], value);\n  }\n  return false;\n}\nexport default isIterateeCall;", "map": {"version": 3, "names": ["eq", "isArrayLike", "isIndex", "isObject", "isIterateeCall", "value", "index", "object", "type", "length"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_isIterateeCall.js"], "sourcesContent": ["import eq from './eq.js';\nimport isArrayLike from './isArrayLike.js';\nimport isIndex from './_isIndex.js';\nimport isObject from './isObject.js';\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\nexport default isIterateeCall;\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,SAAS;AACxB,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAC5C,IAAI,CAACJ,QAAQ,CAACI,MAAM,CAAC,EAAE;IACrB,OAAO,KAAK;EACd;EACA,IAAIC,IAAI,GAAG,OAAOF,KAAK;EACvB,IAAIE,IAAI,IAAI,QAAQ,GACXP,WAAW,CAACM,MAAM,CAAC,IAAIL,OAAO,CAACI,KAAK,EAAEC,MAAM,CAACE,MAAM,CAAC,GACpDD,IAAI,IAAI,QAAQ,IAAIF,KAAK,IAAIC,MAAO,EACvC;IACJ,OAAOP,EAAE,CAACO,MAAM,CAACD,KAAK,CAAC,EAAED,KAAK,CAAC;EACjC;EACA,OAAO,KAAK;AACd;AAEA,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
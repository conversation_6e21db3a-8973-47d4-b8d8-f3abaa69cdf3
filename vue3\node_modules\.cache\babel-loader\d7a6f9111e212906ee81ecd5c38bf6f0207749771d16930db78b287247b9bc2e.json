{"ast": null, "code": "import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\nexport default Map;", "map": {"version": 3, "names": ["getNative", "root", "Map"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_Map.js"], "sourcesContent": ["import getNative from './_getNative.js';\nimport root from './_root.js';\n\n/* Built-in method references that are verified to be native. */\nvar Map = getNative(root, 'Map');\n\nexport default Map;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,GAAG,GAAGF,SAAS,CAACC,IAAI,EAAE,KAAK,CAAC;AAEhC,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
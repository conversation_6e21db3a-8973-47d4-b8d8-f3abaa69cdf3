{"ast": null, "code": "import { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nconst basicYearTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault(\"year\")\n});\nexport { basicYearTableProps };", "map": {"version": 3, "names": ["basicYearTableProps", "buildProps", "datePickerSharedProps", "selectionMode", "selectionModeWithDefault"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-year-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nexport const basicYearTableProps = buildProps({\n  ...datePickerSharedProps,\n  selectionMode: selectionModeWithDefault('year'),\n} as const)\n\nexport type BasicYearTableProps = ExtractPropTypes<typeof basicYearTableProps>\n"], "mappings": ";;AAEY,MAACA,mBAAmB,GAAGC,UAAU,CAAC;EAC5C,GAAGC,qBAAqB;EACxBC,aAAa,EAAEC,wBAAwB,CAAC,MAAM;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
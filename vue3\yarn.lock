# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@achrinza/node-ipc@^9.2.5":
  version "9.2.8"
  resolved "https://registry.npmmirror.com/@achrinza/node-ipc/-/node-ipc-9.2.8.tgz"
  integrity sha512-DSzEEkbMYbAUVlhy7fg+BzccoRuSQzqHbIPGxGv19OJ2WKwS3/9ChAnQcII4g+GujcHhyJ8BUuOVAx/S5uAfQg==
  dependencies:
    "@node-ipc/js-queue" "2.0.3"
    event-pubsub "4.3.0"
    js-message "1.0.7"

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "https://registry.npmmirror.com/@ampproject/remapping/-/remapping-2.3.0.tgz"
  integrity sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.2":
  version "7.24.2"
  resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.24.2.tgz"
  integrity sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==
  dependencies:
    "@babel/highlight" "^7.24.2"
    picocolors "^1.0.0"

"@babel/code-frame@7.12.11":
  version "7.12.11"
  resolved "https://registry.npmmirror.com/@babel/code-frame/-/code-frame-7.12.11.tgz"
  integrity sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.23.5", "@babel/compat-data@^7.24.4":
  version "7.24.4"
  resolved "https://registry.npmmirror.com/@babel/compat-data/-/compat-data-7.24.4.tgz"
  integrity sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.0.0-0 || ^8.0.0-0 <8.0.0", "@babel/core@^7.11.0", "@babel/core@^7.12.0", "@babel/core@^7.12.16", "@babel/core@^7.13.0", "@babel/core@^7.4.0 || ^8.0.0-0 <8.0.0":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/core/-/core-7.24.5.tgz"
  integrity sha512-tVQRucExLQ02Boi4vdPp49svNGcfL2GhdTCT9aldhXgCJVAI21EtRfBettiuLUwce/7r6bFdgs6JFkcdTiFttA==
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-module-transforms" "^7.24.5"
    "@babel/helpers" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/eslint-parser@^7.12.16":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/eslint-parser/-/eslint-parser-7.24.5.tgz"
  integrity sha512-gsUcqS/fPlgAw1kOtpss7uhY6E9SFFANQ6EFX5GTvzUwaV0+sGaZWk6xq22MOdeT9wfxyokW3ceCUvOiRtZciQ==
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    eslint-visitor-keys "^2.1.0"
    semver "^6.3.1"

"@babel/generator@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/generator/-/generator-7.24.5.tgz"
  integrity sha512-x32i4hEXvr+iI0NEoEfDKzlemF8AmtOP8CcrRaEcpzysWuoEb1KknpcvMsHKPONoKZiDuItklgWhB18xEhr9PA==
  dependencies:
    "@babel/types" "^7.24.5"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz"
  integrity sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.22.15":
  version "7.22.15"
  resolved "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.22.15.tgz"
  integrity sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-compilation-targets@^7.12.16", "@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.23.6":
  version "7.23.6"
  resolved "https://registry.npmmirror.com/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz"
  integrity sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==
  dependencies:
    "@babel/compat-data" "^7.23.5"
    "@babel/helper-validator-option" "^7.23.5"
    browserslist "^4.22.2"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.24.1", "@babel/helper-create-class-features-plugin@^7.24.4", "@babel/helper-create-class-features-plugin@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.5.tgz"
  integrity sha512-uRc4Cv8UQWnE4NXlYTIIdM7wfFkOqlFztcC/gVXDKohKoVB3OyonfelUBaJzSwpBntZ2KYGF/9S7asCHsXwW6g==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-member-expression-to-functions" "^7.24.5"
    "@babel/helper-optimise-call-expression" "^7.22.5"
    "@babel/helper-replace-supers" "^7.24.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.22.15", "@babel/helper-create-regexp-features-plugin@^7.22.5":
  version "7.22.15"
  resolved "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.15.tgz"
  integrity sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    regexpu-core "^5.3.1"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.1", "@babel/helper-define-polyfill-provider@^0.6.2":
  version "0.6.2"
  resolved "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.2.tgz"
  integrity sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ==
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-environment-visitor@^7.22.20":
  version "7.22.20"
  resolved "https://registry.npmmirror.com/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz"
  integrity sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==

"@babel/helper-function-name@^7.23.0":
  version "7.23.0"
  resolved "https://registry.npmmirror.com/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz"
  integrity sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==
  dependencies:
    "@babel/template" "^7.22.15"
    "@babel/types" "^7.23.0"

"@babel/helper-hoist-variables@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmmirror.com/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz"
  integrity sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-member-expression-to-functions@^7.23.0", "@babel/helper-member-expression-to-functions@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.24.5.tgz"
  integrity sha512-4owRteeihKWKamtqg4JmWSsEZU445xpFRXPEwp44HbgbxdWlUV1b4Agg4lkA806Lil5XM/e+FJyS0vj5T6vmcA==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.24.1", "@babel/helper-module-imports@^7.24.3":
  version "7.24.3"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.24.3.tgz"
  integrity sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==
  dependencies:
    "@babel/types" "^7.24.0"

"@babel/helper-module-imports@~7.22.15":
  version "7.22.15"
  resolved "https://registry.npmmirror.com/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz"
  integrity sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.23.3", "@babel/helper-module-transforms@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-module-transforms/-/helper-module-transforms-7.24.5.tgz"
  integrity sha512-9GxeY8c2d2mdQUP1Dye0ks3VDyIMS98kt/llQ2nUId8IsWqTF0l1LkSX0/uP7l7MCDrzXS009Hyhe2gzTiGW8A==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-module-imports" "^7.24.3"
    "@babel/helper-simple-access" "^7.24.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/helper-validator-identifier" "^7.24.5"

"@babel/helper-optimise-call-expression@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz"
  integrity sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.0", "@babel/helper-plugin-utils@^7.24.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.5.tgz"
  integrity sha512-xjNLDopRzW2o6ba0gKbkZq5YWEBaK3PCyTOY1K2P/O07LGMhMqlMXPxwN4S5/RhWuCobT8z0jrlKGlYmeR1OhQ==

"@babel/helper-remap-async-to-generator@^7.22.20":
  version "7.22.20"
  resolved "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.20.tgz"
  integrity sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-wrap-function" "^7.22.20"

"@babel/helper-replace-supers@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/helper-replace-supers/-/helper-replace-supers-7.24.1.tgz"
  integrity sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-member-expression-to-functions" "^7.23.0"
    "@babel/helper-optimise-call-expression" "^7.22.5"

"@babel/helper-simple-access@^7.22.5", "@babel/helper-simple-access@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-simple-access/-/helper-simple-access-7.24.5.tgz"
  integrity sha512-uH3Hmf5q5n7n8mz7arjUlDOCbttY/DW4DYhE6FUsjKJ/oYC1kQQUvwEQWxRwUpX9qQKRXeqLwWxrqilMrf32sQ==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-skip-transparent-expression-wrappers@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz"
  integrity sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==
  dependencies:
    "@babel/types" "^7.22.5"

"@babel/helper-split-export-declaration@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.24.5.tgz"
  integrity sha512-5CHncttXohrHk8GWOFCcCl4oRD9fKosWlIRgWm4ql9VYioKm52Mk2xsmoohvm7f3JoiLSM5ZgJuRaf5QZZYd3Q==
  dependencies:
    "@babel/types" "^7.24.5"

"@babel/helper-string-parser@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/helper-string-parser/-/helper-string-parser-7.24.1.tgz"
  integrity sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==

"@babel/helper-validator-identifier@^7.22.20", "@babel/helper-validator-identifier@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-identifier/-/helper-validator-identifier-7.24.5.tgz"
  integrity sha512-3q93SSKX2TWCG30M2G2kwaKeTYgEUp5Snjuj8qm729SObL6nbtUldAi37qbxkD5gg3xnBio+f9nqpSepGZMvxA==

"@babel/helper-validator-option@^7.23.5":
  version "7.23.5"
  resolved "https://registry.npmmirror.com/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz"
  integrity sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==

"@babel/helper-wrap-function@^7.22.20":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helper-wrap-function/-/helper-wrap-function-7.24.5.tgz"
  integrity sha512-/xxzuNvgRl4/HLNKvnFwdhdgN3cpLxgLROeLDl83Yx0AJ1SGvq1ak0OszTOjDfiB8Vx03eJbeDWh9r+jCCWttw==
  dependencies:
    "@babel/helper-function-name" "^7.23.0"
    "@babel/template" "^7.24.0"
    "@babel/types" "^7.24.5"

"@babel/helpers@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/helpers/-/helpers-7.24.5.tgz"
  integrity sha512-CiQmBMMpMQHwM5m01YnrM6imUG1ebgYJ+fAIW4FZe6m4qHTPaRHti+R8cggAwkdz4oXhtO4/K9JWlh+8hIfR2Q==
  dependencies:
    "@babel/template" "^7.24.0"
    "@babel/traverse" "^7.24.5"
    "@babel/types" "^7.24.5"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.24.2":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/highlight/-/highlight-7.24.5.tgz"
  integrity sha512-8lLmua6AVh/8SLJRRVD6V8p73Hir9w5mJrhE+IPpILG31KKlI9iz5zmBYKcWPS59qSfgP9RaSBQSHHE81WKuEw==
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.5"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.23.9", "@babel/parser@^7.24.0", "@babel/parser@^7.24.4", "@babel/parser@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/parser/-/parser-7.24.5.tgz"
  integrity sha512-EOv5IK8arwh3LI47dz1b0tKUb/1uhHAnHJOrjgtQMIpu1uXd9mlFrJg9IUgGUgZ41Ch0K8REPTYpO7B76b4vJg==

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.5.tgz"
  integrity sha512-LdXRi1wEMTrHVR4Zc9F8OewC3vdm5h4QB6L71zy6StmYeqGi1b3ttIO8UC+BfZKcH9jdr4aI249rBkm+3+YvHw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.1.tgz"
  integrity sha512-y4HqEnkelJIOQGd+3g1bTeKsA5c6qM7eOn7VggGVbBc0y8MLSKHacwcIE2PplNlQSj0PqS9rrXL/nkPVK+kUNg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.24.1.tgz"
  integrity sha512-Hj791Ii4ci8HqnaKHAlLNs+zaLXb0EzSDhiAWp5VNlyvCNymYfacs64pxTxbH1znW/NcArSmwpmG9IKE/TUVVQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-transform-optional-chaining" "^7.24.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.24.1.tgz"
  integrity sha512-m9m/fXsXLiHfwdgydIFnpk+7jlVbnvlK5B2EKiPdLUb6WX654ZaaEWJUjk8TftRbZpK0XibovlLWX4KIZhV6jw==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-proposal-class-properties@^7.12.13":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz"
  integrity sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.12.13":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.1.tgz"
  integrity sha512-zPEvzFijn+hRvJuX2Vu3KbEBN39LN3f7tW3MQO2LsIs57B26KU+kUc82BdAktS1VCM6libzh45eKGI65lg0cpA==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-decorators" "^7.24.1"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "https://registry.npmmirror.com/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz"
  integrity sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz"
  integrity sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz"
  integrity sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz"
  integrity sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.24.1.tgz"
  integrity sha512-05RJdO/cCrtVWuAaSn1tS3bH8jbsJa/Y1uD186u6J4C/1mnHFxseeuWpsqr9anvo7TUulev7tm7GDwRV+VuhDw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz"
  integrity sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz"
  integrity sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-import-assertions@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.1.tgz"
  integrity sha512-IuwnI5XnuF189t91XbxmXeCDz3qs6iDRO7GJ++wcfgeXNs/8FmIlKcpDSXNVyuLQxlwvskmI3Ct73wUODkJBlQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-import-attributes@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.1.tgz"
  integrity sha512-zhQTMH0X2nVLnb04tz+s7AMuasX8U0FnpE+nHTOhSOINjWMnopoZTxtIKsd45n4GQ/HIZLyfIpoul8e2m0DnRA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz"
  integrity sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz"
  integrity sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.12.13", "@babel/plugin-syntax-jsx@^7.2.0", "@babel/plugin-syntax-jsx@^7.23.3":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.1.tgz"
  integrity sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  integrity sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  integrity sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz"
  integrity sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz"
  integrity sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  integrity sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz"
  integrity sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz"
  integrity sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz"
  integrity sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "https://registry.npmmirror.com/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz"
  integrity sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.1.tgz"
  integrity sha512-ngT/3NkRhsaep9ck9uj2Xhv9+xB1zShY3tM3g6om4xxCELwCDN4g4Aq5dRn48+0hasAql7s2hdBOysCfNpr4fw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-async-generator-functions@^7.24.3":
  version "7.24.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.3.tgz"
  integrity sha512-Qe26CMYVjpQxJ8zxM1340JFNjZaF+ISWpr1Kt/jGo+ZTUzKkfw/pphEWbRCb+lmSM6k/TOgfYLvmbHkUQ0asIg==
  dependencies:
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-remap-async-to-generator" "^7.22.20"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-transform-async-to-generator@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.1.tgz"
  integrity sha512-AawPptitRXp1y0n4ilKcGbRYWfbbzFWz2NqNu7dacYDtFtz0CMjG64b3LQsb3KIgnf4/obcUL78hfaOS7iCUfw==
  dependencies:
    "@babel/helper-module-imports" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-remap-async-to-generator" "^7.22.20"

"@babel/plugin-transform-block-scoped-functions@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.24.1.tgz"
  integrity sha512-TWWC18OShZutrv9C6mye1xwtam+uNi2bnTOCBUd5sZxyHOiWbU6ztSROofIMrK84uweEZC219POICK/sTYwfgg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-block-scoping@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.5.tgz"
  integrity sha512-sMfBc3OxghjC95BkYrYocHL3NaOplrcaunblzwXhGmlPwpmfsxr4vK+mBBt49r+S240vahmv+kUxkeKgs+haCw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-class-properties@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.1.tgz"
  integrity sha512-OMLCXi0NqvJfORTaPQBwqLXHhb93wkBKZ4aNwMl6WtehO7ar+cmp+89iPEQPqxAnxsOKTaMcs3POz3rKayJ72g==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-class-static-block@^7.24.4":
  version "7.24.4"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.4.tgz"
  integrity sha512-B8q7Pz870Hz/q9UgP8InNpY01CSLDSCyqX7zcRuv3FcPl87A2G17lASroHWaCtbdIcbYzOZ7kWmXFKbijMSmFg==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.4"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-transform-classes@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.5.tgz"
  integrity sha512-gWkLP25DFj2dwe9Ck8uwMOpko4YsqyfZJrOmqqcegeDYEbp7rmn4U6UQZNj08UF6MaX39XenSpKRCvpDRBtZ7Q==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-replace-supers" "^7.24.1"
    "@babel/helper-split-export-declaration" "^7.24.5"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.24.1.tgz"
  integrity sha512-5pJGVIUfJpOS+pAqBQd+QMaTD2vCL/HcePooON6pDpHgRp4gNRmzyHTPIkXntwKsq3ayUFVfJaIKPw2pOkOcTw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/template" "^7.24.0"

"@babel/plugin-transform-destructuring@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.24.5.tgz"
  integrity sha512-SZuuLyfxvsm+Ah57I/i1HVjveBENYK9ue8MJ7qkc7ndoNjqquJiElzA7f5yaAXjyW2hKojosOTAQQRX50bPSVg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-dotall-regex@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.1.tgz"
  integrity sha512-p7uUxgSoZwZ2lPNMzUkqCts3xlp8n+o05ikjy7gbtFJSt9gdU88jAmtfmOxHM14noQXBxfgzf2yRWECiNVhTCw==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-duplicate-keys@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.1.tgz"
  integrity sha512-msyzuUnvsjsaSaocV6L7ErfNsa5nDWL1XKNnDePLgmz+WdU4w/J8+AxBMrWfi9m4IxfL5sZQKUPQKDQeeAT6lA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-dynamic-import@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.1.tgz"
  integrity sha512-av2gdSTyXcJVdI+8aFZsCAtR29xJt0S5tas+Ef8NvBNmD1a+N/3ecMLeMBgfcK+xzsjdLDT6oHt+DFPyeqUbDA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-transform-exponentiation-operator@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.1.tgz"
  integrity sha512-U1yX13dVBSwS23DEAqU+Z/PkwE9/m7QQy8Y9/+Tdb8UWYaGNDYwTLi19wqIAiROr8sXVum9A/rtiH5H0boUcTw==
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-export-namespace-from@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.1.tgz"
  integrity sha512-Ft38m/KFOyzKw2UaJFkWG9QnHPG/Q/2SkOrRk4pNBPg5IPZ+dOxcmkK5IyuBcxiNPyyYowPGUReyBvrvZs7IlQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-transform-for-of@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.1.tgz"
  integrity sha512-OxBdcnF04bpdQdR3i4giHZNZQn7cm8RQKcSwA17wAAqEELo1ZOwp5FFgeptWUQXFyT9kwHo10aqqauYkRZPCAg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-function-name@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.1.tgz"
  integrity sha512-BXmDZpPlh7jwicKArQASrj8n22/w6iymRnvHYYd2zO30DbE277JO20/7yXJT3QxDPtiQiOxQBbZH4TpivNXIxA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-json-strings@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.24.1.tgz"
  integrity sha512-U7RMFmRvoasscrIFy5xA4gIp8iWnWubnKkKuUGJjsuOH7GfbMkB+XZzeslx2kLdEGdOJDamEmCqOks6e8nv8DQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-transform-literals@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.1.tgz"
  integrity sha512-zn9pwz8U7nCqOYIiBaOxoQOtYmMODXTJnkxG4AtX8fPmnCRYWBOHD0qcpwS9e2VDSp1zNJYpdnFMIKb8jmwu6g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-logical-assignment-operators@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.1.tgz"
  integrity sha512-OhN6J4Bpz+hIBqItTeWJujDOfNP+unqv/NJgyhlpSqgBTPm37KkMmZV6SYcOj+pnDbdcl1qRGV/ZiIjX9Iy34w==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-transform-member-expression-literals@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.1.tgz"
  integrity sha512-4ojai0KysTWXzHseJKa1XPNXKRbuUrhkOPY4rEGeR+7ChlJVKxFa3H3Bz+7tWaGKgJAXUWKOGmltN+u9B3+CVg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-modules-amd@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.1.tgz"
  integrity sha512-lAxNHi4HVtjnHd5Rxg3D5t99Xm6H7b04hUS7EHIXcUl2EV4yl1gWdqZrNzXnSrHveL9qMdbODlLF55mvgjAfaQ==
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-modules-commonjs@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.24.1.tgz"
  integrity sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-simple-access" "^7.22.5"

"@babel/plugin-transform-modules-systemjs@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.1.tgz"
  integrity sha512-mqQ3Zh9vFO1Tpmlt8QPnbwGHzNz3lpNEMxQb1kAemn/erstyqw1r9KeOlOfo3y6xAnFEcOv2tSyrXfmMk+/YZA==
  dependencies:
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-validator-identifier" "^7.22.20"

"@babel/plugin-transform-modules-umd@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.1.tgz"
  integrity sha512-tuA3lpPj+5ITfcCluy6nWonSL7RvaG0AOTeAuvXqEKS34lnLzXpDb0dcP6K8jD0zWZFNDVly90AGFJPnm4fOYg==
  dependencies:
    "@babel/helper-module-transforms" "^7.23.3"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz"
  integrity sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.5"
    "@babel/helper-plugin-utils" "^7.22.5"

"@babel/plugin-transform-new-target@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.1.tgz"
  integrity sha512-/rurytBM34hYy0HKZQyA0nHbQgQNFm4Q/BOc9Hflxi2X3twRof7NaE5W46j4kQitm7SvACVRXsa6N/tSZxvPug==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-nullish-coalescing-operator@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.1.tgz"
  integrity sha512-iQ+caew8wRrhCikO5DrUYx0mrmdhkaELgFa+7baMcVuhxIkN7oxt06CZ51D65ugIb1UWRQ8oQe+HXAVM6qHFjw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-transform-numeric-separator@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.1.tgz"
  integrity sha512-7GAsGlK4cNL2OExJH1DzmDeKnRv/LXq0eLUSvudrehVA5Rgg4bIrqEUW29FbKMBRT0ztSqisv7kjP+XIC4ZMNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-transform-object-rest-spread@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.5.tgz"
  integrity sha512-7EauQHszLGM3ay7a161tTQH7fj+3vVM/gThlz5HpFtnygTxjrlvoeq7MPVA1Vy9Q555OB8SnAOsMkLShNkkrHA==
  dependencies:
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.24.5"

"@babel/plugin-transform-object-super@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.1.tgz"
  integrity sha512-oKJqR3TeI5hSLRxudMjFQ9re9fBVUU0GICqM3J1mi8MqlhVr6hC/ZN4ttAyMuQR6EZZIY6h/exe5swqGNNIkWQ==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-replace-supers" "^7.24.1"

"@babel/plugin-transform-optional-catch-binding@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.1.tgz"
  integrity sha512-oBTH7oURV4Y+3EUrf6cWn1OHio3qG/PVwO5J03iSJmBg6m2EhKjkAu/xuaXaYwWW9miYtvbWv4LNf0AmR43LUA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-transform-optional-chaining@^7.24.1", "@babel/plugin-transform-optional-chaining@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.5.tgz"
  integrity sha512-xWCkmwKT+ihmA6l7SSTpk8e4qQl/274iNbSKRRS8mpqFR32ksy36+a+LWY8OXCCEefF8WFlnOHVsaDI2231wBg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-transform-parameters@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.5.tgz"
  integrity sha512-9Co00MqZ2aoky+4j2jhofErthm6QVLKbpQrvz20c3CH9KQCLHyNB+t2ya4/UrRpQGR+Wrwjg9foopoeSdnHOkA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-private-methods@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.24.1.tgz"
  integrity sha512-tGvisebwBO5em4PaYNqt4fkw56K2VALsAbAakY0FjTYqJp7gfdrgr7YX76Or8/cpik0W6+tj3rZ0uHU9Oil4tw==
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.1"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-private-property-in-object@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.5.tgz"
  integrity sha512-JM4MHZqnWR04jPMujQDTBVRnqxpLLpx2tkn7iPn+Hmsc0Gnb79yvRWOkvqFOx3Z7P7VxiRIR22c4eGSNj87OBQ==
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.22.5"
    "@babel/helper-create-class-features-plugin" "^7.24.5"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.1.tgz"
  integrity sha512-LetvD7CrHmEx0G442gOomRr66d7q8HzzGGr4PMHGr+5YIm6++Yke+jxj246rpvsbyhJwCLxcTn6zW1P1BSenqA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-regenerator@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.1.tgz"
  integrity sha512-sJwZBCzIBE4t+5Q4IGLaaun5ExVMRY0lYwos/jNecjMrVCygCdph3IKv0tkP5Fc87e/1+bebAmEAGBfnRD+cnw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-reserved-words@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.1.tgz"
  integrity sha512-JAclqStUfIwKN15HrsQADFgeZt+wexNQ0uLhuqvqAUFoqPMjEcFCYZBhq0LUdz6dZK/mD+rErhW71fbx8RYElg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-runtime@^7.12.15":
  version "7.24.3"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.24.3.tgz"
  integrity sha512-J0BuRPNlNqlMTRJ72eVptpt9VcInbxO6iP3jaxr+1NPhC0UkKL+6oeX6VXMEYdADnuqmMmsBspt4d5w8Y/TCbQ==
  dependencies:
    "@babel/helper-module-imports" "^7.24.3"
    "@babel/helper-plugin-utils" "^7.24.0"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.1"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.1.tgz"
  integrity sha512-LyjVB1nsJ6gTTUKRjRWx9C1s9hE7dLfP/knKdrfeH9UPtAGjYGgxIbFfx7xyLIEWs7Xe1Gnf8EWiUqfjLhInZA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-spread@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.1.tgz"
  integrity sha512-KjmcIM+fxgY+KxPVbjelJC6hrH1CgtPmTvdXAfn3/a9CnWGSTY7nH4zm5+cjmWJybdcPSsD0++QssDsjcpe47g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.22.5"

"@babel/plugin-transform-sticky-regex@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.1.tgz"
  integrity sha512-9v0f1bRXgPVcPrngOQvLXeGNNVLc8UjMVfebo9ka0WF3/7+aVUHmaJVT3sa0XCzEFioPfPHZiOcYG9qOsH63cw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-template-literals@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.24.1.tgz"
  integrity sha512-WRkhROsNzriarqECASCNu/nojeXCDTE/F2HmRgOzi7NGvyfYGq1NEjKBK3ckLfRgGc6/lPAqP0vDOSw3YtG34g==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-typeof-symbol@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.5.tgz"
  integrity sha512-UTGnhYVZtTAjdwOTzT+sCyXmTn8AhaxOS/MjG9REclZ6ULHWF9KoCZur0HSGU7hk8PdBFKKbYe6+gqdXWz84Jg==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.5"

"@babel/plugin-transform-unicode-escapes@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.1.tgz"
  integrity sha512-RlkVIcWT4TLI96zM660S877E7beKlQw7Ig+wqkKBiWfj0zH5Q4h50q6er4wzZKRNSYpfo6ILJ+hrJAGSX2qcNw==
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-property-regex@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.1.tgz"
  integrity sha512-Ss4VvlfYV5huWApFsF8/Sq0oXnGO+jB+rijFEFugTd3cwSObUSnUi88djgR5528Csl0uKlrI331kRqe56Ov2Ng==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-regex@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.1.tgz"
  integrity sha512-2A/94wgZgxfTsiLaQ2E36XAOdcZmGAaEEgVmxQWwZXWkGhvoHbaqXcKnU8zny4ycpu3vNqg0L/PcCiYtHtA13g==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/plugin-transform-unicode-sets-regex@^7.24.1":
  version "7.24.1"
  resolved "https://registry.npmmirror.com/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.1.tgz"
  integrity sha512-fqj4WuzzS+ukpgerpAoOnMfQXwUHFxXUZUE84oL2Kao2N8uSlvcpnAidKASgsNgzZHBsHWvcm8s9FPWUhAb8fA==
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.22.15"
    "@babel/helper-plugin-utils" "^7.24.0"

"@babel/preset-env@^7.12.16":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/preset-env/-/preset-env-7.24.5.tgz"
  integrity sha512-UGK2ifKtcC8i5AI4cH+sbLLuLc2ktYSFJgBAXorKAsHUZmrQ1q6aQ6i3BvU24wWs2AAKqQB6kq3N9V9Gw1HiMQ==
  dependencies:
    "@babel/compat-data" "^7.24.4"
    "@babel/helper-compilation-targets" "^7.23.6"
    "@babel/helper-plugin-utils" "^7.24.5"
    "@babel/helper-validator-option" "^7.23.5"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.24.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.24.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.24.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.24.1"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-import-assertions" "^7.24.1"
    "@babel/plugin-syntax-import-attributes" "^7.24.1"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.24.1"
    "@babel/plugin-transform-async-generator-functions" "^7.24.3"
    "@babel/plugin-transform-async-to-generator" "^7.24.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.24.1"
    "@babel/plugin-transform-block-scoping" "^7.24.5"
    "@babel/plugin-transform-class-properties" "^7.24.1"
    "@babel/plugin-transform-class-static-block" "^7.24.4"
    "@babel/plugin-transform-classes" "^7.24.5"
    "@babel/plugin-transform-computed-properties" "^7.24.1"
    "@babel/plugin-transform-destructuring" "^7.24.5"
    "@babel/plugin-transform-dotall-regex" "^7.24.1"
    "@babel/plugin-transform-duplicate-keys" "^7.24.1"
    "@babel/plugin-transform-dynamic-import" "^7.24.1"
    "@babel/plugin-transform-exponentiation-operator" "^7.24.1"
    "@babel/plugin-transform-export-namespace-from" "^7.24.1"
    "@babel/plugin-transform-for-of" "^7.24.1"
    "@babel/plugin-transform-function-name" "^7.24.1"
    "@babel/plugin-transform-json-strings" "^7.24.1"
    "@babel/plugin-transform-literals" "^7.24.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.24.1"
    "@babel/plugin-transform-member-expression-literals" "^7.24.1"
    "@babel/plugin-transform-modules-amd" "^7.24.1"
    "@babel/plugin-transform-modules-commonjs" "^7.24.1"
    "@babel/plugin-transform-modules-systemjs" "^7.24.1"
    "@babel/plugin-transform-modules-umd" "^7.24.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.22.5"
    "@babel/plugin-transform-new-target" "^7.24.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.24.1"
    "@babel/plugin-transform-numeric-separator" "^7.24.1"
    "@babel/plugin-transform-object-rest-spread" "^7.24.5"
    "@babel/plugin-transform-object-super" "^7.24.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.24.1"
    "@babel/plugin-transform-optional-chaining" "^7.24.5"
    "@babel/plugin-transform-parameters" "^7.24.5"
    "@babel/plugin-transform-private-methods" "^7.24.1"
    "@babel/plugin-transform-private-property-in-object" "^7.24.5"
    "@babel/plugin-transform-property-literals" "^7.24.1"
    "@babel/plugin-transform-regenerator" "^7.24.1"
    "@babel/plugin-transform-reserved-words" "^7.24.1"
    "@babel/plugin-transform-shorthand-properties" "^7.24.1"
    "@babel/plugin-transform-spread" "^7.24.1"
    "@babel/plugin-transform-sticky-regex" "^7.24.1"
    "@babel/plugin-transform-template-literals" "^7.24.1"
    "@babel/plugin-transform-typeof-symbol" "^7.24.5"
    "@babel/plugin-transform-unicode-escapes" "^7.24.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.24.1"
    "@babel/plugin-transform-unicode-regex" "^7.24.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.24.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.4"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.31.0"
    semver "^6.3.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "https://registry.npmmirror.com/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz"
  integrity sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/regjsgen@^0.8.0":
  version "0.8.0"
  resolved "https://registry.npmmirror.com/@babel/regjsgen/-/regjsgen-0.8.0.tgz"
  integrity sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==

"@babel/runtime@^7.12.13", "@babel/runtime@^7.8.4":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/runtime/-/runtime-7.24.5.tgz"
  integrity sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.22.15", "@babel/template@^7.23.9", "@babel/template@^7.24.0":
  version "7.24.0"
  resolved "https://registry.npmmirror.com/@babel/template/-/template-7.24.0.tgz"
  integrity sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/parser" "^7.24.0"
    "@babel/types" "^7.24.0"

"@babel/traverse@^7.23.9", "@babel/traverse@^7.24.5":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/traverse/-/traverse-7.24.5.tgz"
  integrity sha512-7aaBLeDQ4zYcUFDUD41lJc1fG8+5IU9DaNSJAgal866FGvmD5EbWQgnEC6kO1gGLsX0esNkfnJSndbTXA3r7UA==
  dependencies:
    "@babel/code-frame" "^7.24.2"
    "@babel/generator" "^7.24.5"
    "@babel/helper-environment-visitor" "^7.22.20"
    "@babel/helper-function-name" "^7.23.0"
    "@babel/helper-hoist-variables" "^7.22.5"
    "@babel/helper-split-export-declaration" "^7.24.5"
    "@babel/parser" "^7.24.5"
    "@babel/types" "^7.24.5"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.22.15", "@babel/types@^7.22.5", "@babel/types@^7.23.0", "@babel/types@^7.23.9", "@babel/types@^7.24.0", "@babel/types@^7.24.5", "@babel/types@^7.4.4":
  version "7.24.5"
  resolved "https://registry.npmmirror.com/@babel/types/-/types-7.24.5.tgz"
  integrity sha512-6mQNsaLeXTw0nxYUYu+NSa4Hx4BlF1x1x8/PMFbiR+GBSr+2DkECc69b8hgy2frEodNcvPffeH8YfWd3LI6jhQ==
  dependencies:
    "@babel/helper-string-parser" "^7.24.1"
    "@babel/helper-validator-identifier" "^7.24.5"
    to-fast-properties "^2.0.0"

"@discoveryjs/json-ext@0.5.7":
  version "0.5.7"
  resolved "https://registry.npmmirror.com/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz"
  integrity sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==

"@eslint/eslintrc@^0.4.3":
  version "0.4.3"
  resolved "https://registry.npmmirror.com/@eslint/eslintrc/-/eslintrc-0.4.3.tgz"
  integrity sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==
  dependencies:
    ajv "^6.12.4"
    debug "^4.1.1"
    espree "^7.3.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^3.13.1"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@hapi/hoek@^9.0.0", "@hapi/hoek@^9.3.0":
  version "9.3.0"
  resolved "https://registry.npmmirror.com/@hapi/hoek/-/hoek-9.3.0.tgz"
  integrity sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==

"@hapi/topo@^5.1.0":
  version "5.1.0"
  resolved "https://registry.npmmirror.com/@hapi/topo/-/topo-5.1.0.tgz"
  integrity sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  version "0.5.0"
  resolved "https://registry.npmmirror.com/@humanwhocodes/config-array/-/config-array-0.5.0.tgz"
  integrity sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "https://registry.npmmirror.com/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  integrity sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==

"@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmmirror.com/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmmirror.com/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmmirror.com/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "https://registry.npmmirror.com/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  integrity sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.4.15"
  resolved "https://registry.npmmirror.com/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  integrity sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==

"@jridgewell/trace-mapping@^0.3.20", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmmirror.com/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@leichtgewicht/ip-codec@^2.0.1":
  version "2.0.5"
  resolved "https://registry.npmmirror.com/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz"
  integrity sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  version "5.1.1-v1"
  resolved "https://registry.npmmirror.com/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz"
  integrity sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==
  dependencies:
    eslint-scope "5.1.1"

"@node-ipc/js-queue@2.0.3":
  version "2.0.3"
  resolved "https://registry.npmmirror.com/@node-ipc/js-queue/-/js-queue-2.0.3.tgz"
  integrity sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw==
  dependencies:
    easy-stack "1.0.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmmirror.com/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmmirror.com/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.25"
  resolved "https://registry.npmmirror.com/@polka/url/-/url-1.0.0-next.25.tgz"
  integrity sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==

"@sideway/address@^4.1.5":
  version "4.1.5"
  resolved "https://registry.npmmirror.com/@sideway/address/-/address-4.1.5.tgz"
  integrity sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.1":
  version "3.0.1"
  resolved "https://registry.npmmirror.com/@sideway/formula/-/formula-3.0.1.tgz"
  integrity sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==

"@sideway/pinpoint@^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmmirror.com/@sideway/pinpoint/-/pinpoint-2.0.0.tgz"
  integrity sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==

"@soda/friendly-errors-webpack-plugin@^1.8.0":
  version "1.8.1"
  resolved "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz"
  integrity sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==
  dependencies:
    chalk "^3.0.0"
    error-stack-parser "^2.0.6"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

"@soda/get-current-script@^1.0.2":
  version "1.0.2"
  resolved "https://registry.npmmirror.com/@soda/get-current-script/-/get-current-script-1.0.2.tgz"
  integrity sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w==

"@trysound/sax@0.2.0":
  version "0.2.0"
  resolved "https://registry.npmmirror.com/@trysound/sax/-/sax-0.2.0.tgz"
  integrity sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==

"@types/body-parser@*":
  version "1.19.5"
  resolved "https://registry.npmmirror.com/@types/body-parser/-/body-parser-1.19.5.tgz"
  integrity sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@^3.5.9":
  version "3.5.13"
  resolved "https://registry.npmmirror.com/@types/bonjour/-/bonjour-3.5.13.tgz"
  integrity sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@^1.3.5":
  version "1.5.4"
  resolved "https://registry.npmmirror.com/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz"
  integrity sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  version "3.4.38"
  resolved "https://registry.npmmirror.com/@types/connect/-/connect-3.4.38.tgz"
  integrity sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==
  dependencies:
    "@types/node" "*"

"@types/eslint-scope@^3.7.3":
  version "3.7.7"
  resolved "https://registry.npmmirror.com/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  integrity sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^7.29.0 || ^8.4.1":
  version "8.56.10"
  resolved "https://registry.npmmirror.com/@types/eslint/-/eslint-8.56.10.tgz"
  integrity sha512-Shavhk87gCtY2fhXDctcfS3e6FdxWkCx1iUZ9eEUbh7rTqlZT0/IzOkCOVt0fCjcFuZ9FPYfuezTBImfHCDBGQ==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.5":
  version "1.0.5"
  resolved "https://registry.npmmirror.com/@types/estree/-/estree-1.0.5.tgz"
  integrity sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.33":
  version "4.19.0"
  resolved "https://registry.npmmirror.com/@types/express-serve-static-core/-/express-serve-static-core-4.19.0.tgz"
  integrity sha512-bGyep3JqPCRry1wq+O5n7oiBgGWmeIJXPjXXCo8EK0u8duZGSYar7cGqd3ML2JUsLGeB7fmc06KYo9fLGWqPvQ==
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"
    "@types/send" "*"

"@types/express@*", "@types/express@^4.17.13":
  version "4.17.21"
  resolved "https://registry.npmmirror.com/@types/express/-/express-4.17.21.tgz"
  integrity sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.33"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/html-minifier-terser@^6.0.0":
  version "6.1.0"
  resolved "https://registry.npmmirror.com/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==

"@types/http-errors@*":
  version "2.0.4"
  resolved "https://registry.npmmirror.com/@types/http-errors/-/http-errors-2.0.4.tgz"
  integrity sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==

"@types/http-proxy@^1.17.8":
  version "1.17.14"
  resolved "https://registry.npmmirror.com/@types/http-proxy/-/http-proxy-1.17.14.tgz"
  integrity sha512-SSrD0c1OQzlFX7pGu1eXxSEjemej64aaNPRhhVYUGqXh0BtldAAx37MG8btcumvpgKyZp1F5Gn3JkktdxiFv6w==
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "https://registry.npmmirror.com/@types/json-schema/-/json-schema-7.0.15.tgz"
  integrity sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==

"@types/mime@^1":
  version "1.3.5"
  resolved "https://registry.npmmirror.com/@types/mime/-/mime-1.3.5.tgz"
  integrity sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==

"@types/minimist@^1.2.0":
  version "1.2.5"
  resolved "https://registry.npmmirror.com/@types/minimist/-/minimist-1.2.5.tgz"
  integrity sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==

"@types/node-forge@^1.3.0":
  version "1.3.11"
  resolved "https://registry.npmmirror.com/@types/node-forge/-/node-forge-1.3.11.tgz"
  integrity sha512-FQx220y22OKNTqaByeBGqHWYz4cl94tpcxeFdvBo3wjG6XPBuZ0BNgNZRV5J5TFmmcsJ4IzsLkmGRiQbnYsBEQ==
  dependencies:
    "@types/node" "*"

"@types/node@*":
  version "20.12.11"
  resolved "https://registry.npmmirror.com/@types/node/-/node-20.12.11.tgz"
  integrity sha512-vDg9PZ/zi+Nqp6boSOT7plNuthRugEKixDv5sFTIpkE89MmNtEArAShI4mxuX2+UrLEe9pxC1vm2cjm9YlWbJw==
  dependencies:
    undici-types "~5.26.4"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "https://registry.npmmirror.com/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz"
  integrity sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://registry.npmmirror.com/@types/parse-json/-/parse-json-4.0.2.tgz"
  integrity sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==

"@types/qs@*":
  version "6.9.15"
  resolved "https://registry.npmmirror.com/@types/qs/-/qs-6.9.15.tgz"
  integrity sha512-uXHQKES6DQKKCLh441Xv/dwxOq1TVS3JPUMlEqoEglvlhR6Mxnlew/Xq/LRVHpLyk7iK3zODe1qYHIMltO7XGg==

"@types/range-parser@*":
  version "1.2.7"
  resolved "https://registry.npmmirror.com/@types/range-parser/-/range-parser-1.2.7.tgz"
  integrity sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==

"@types/retry@0.12.0":
  version "0.12.0"
  resolved "https://registry.npmmirror.com/@types/retry/-/retry-0.12.0.tgz"
  integrity sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==

"@types/send@*":
  version "0.17.4"
  resolved "https://registry.npmmirror.com/@types/send/-/send-0.17.4.tgz"
  integrity sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/serve-index@^1.9.1":
  version "1.9.4"
  resolved "https://registry.npmmirror.com/@types/serve-index/-/serve-index-1.9.4.tgz"
  integrity sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==
  dependencies:
    "@types/express" "*"

"@types/serve-static@*", "@types/serve-static@^1.13.10":
  version "1.15.7"
  resolved "https://registry.npmmirror.com/@types/serve-static/-/serve-static-1.15.7.tgz"
  integrity sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==
  dependencies:
    "@types/http-errors" "*"
    "@types/node" "*"
    "@types/send" "*"

"@types/sockjs@^0.3.33":
  version "0.3.36"
  resolved "https://registry.npmmirror.com/@types/sockjs/-/sockjs-0.3.36.tgz"
  integrity sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==
  dependencies:
    "@types/node" "*"

"@types/ws@^8.5.5":
  version "8.5.10"
  resolved "https://registry.npmmirror.com/@types/ws/-/ws-8.5.10.tgz"
  integrity sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==
  dependencies:
    "@types/node" "*"

"@vue/babel-helper-vue-jsx-merge-props@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz"
  integrity sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==

"@vue/babel-helper-vue-transform-on@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.2.2.tgz"
  integrity sha512-nOttamHUR3YzdEqdM/XXDyCSdxMA9VizUKoroLX6yTyRtggzQMHXcmwh8a7ZErcJttIBIc9s68a1B8GZ+Dmvsw==

"@vue/babel-plugin-jsx@^1.0.3":
  version "1.2.2"
  resolved "https://registry.npmmirror.com/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.2.2.tgz"
  integrity sha512-nYTkZUVTu4nhP199UoORePsql0l+wj7v/oyQjtThUVhJl1U+6qHuoVhIvR3bf7eVKjbCK+Cs2AWd7mi9Mpz9rA==
  dependencies:
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/template" "^7.23.9"
    "@babel/traverse" "^7.23.9"
    "@babel/types" "^7.23.9"
    "@vue/babel-helper-vue-transform-on" "1.2.2"
    "@vue/babel-plugin-resolve-type" "1.2.2"
    camelcase "^6.3.0"
    html-tags "^3.3.1"
    svg-tags "^1.0.0"

"@vue/babel-plugin-resolve-type@1.2.2":
  version "1.2.2"
  resolved "https://registry.npmmirror.com/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.2.2.tgz"
  integrity sha512-EntyroPwNg5IPVdUJupqs0CFzuf6lUrVvCspmv2J1FITLeGnUCuoGNNk78dgCusxEiYj6RMkTJflGSxk5aIC4A==
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/parser" "^7.23.9"
    "@vue/compiler-sfc" "^3.4.15"

"@vue/babel-plugin-transform-vue-jsx@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz"
  integrity sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    html-tags "^2.0.0"
    lodash.kebabcase "^4.1.1"
    svg-tags "^1.0.0"

"@vue/babel-preset-app@^5.0.8":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/babel-preset-app/-/babel-preset-app-5.0.8.tgz"
  integrity sha512-yl+5qhpjd8e1G4cMXfORkkBlvtPCIgmRf3IYCWYDKIQ7m+PPa5iTm4feiNmCMD6yGqQWMhhK/7M3oWGL9boKwg==
  dependencies:
    "@babel/core" "^7.12.16"
    "@babel/helper-compilation-targets" "^7.12.16"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-proposal-class-properties" "^7.12.13"
    "@babel/plugin-proposal-decorators" "^7.12.13"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/plugin-transform-runtime" "^7.12.15"
    "@babel/preset-env" "^7.12.16"
    "@babel/runtime" "^7.12.13"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.1.2"
    babel-plugin-dynamic-import-node "^2.3.3"
    core-js "^3.8.3"
    core-js-compat "^3.8.3"
    semver "^7.3.4"

"@vue/babel-preset-jsx@^1.1.2":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz"
  integrity sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA==
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    "@vue/babel-sugar-composition-api-inject-h" "^1.4.0"
    "@vue/babel-sugar-composition-api-render-instance" "^1.4.0"
    "@vue/babel-sugar-functional-vue" "^1.4.0"
    "@vue/babel-sugar-inject-h" "^1.4.0"
    "@vue/babel-sugar-v-model" "^1.4.0"
    "@vue/babel-sugar-v-on" "^1.4.0"

"@vue/babel-sugar-composition-api-inject-h@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz"
  integrity sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz"
  integrity sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz"
  integrity sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz"
  integrity sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz"
  integrity sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.4.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"
    html-tags "^2.0.0"
    svg-tags "^1.0.0"

"@vue/babel-sugar-v-on@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz"
  integrity sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.4.0"
    camelcase "^5.0.0"

"@vue/cli-overlay@^5.0.8":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-overlay/-/cli-overlay-5.0.8.tgz"
  integrity sha512-KmtievE/B4kcXp6SuM2gzsnSd8WebkQpg3XaB6GmFh1BJGRqa1UiW9up7L/Q67uOdTigHxr5Ar2lZms4RcDjwQ==

"@vue/cli-plugin-babel@~5.0.0":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-babel/-/cli-plugin-babel-5.0.8.tgz"
  integrity sha512-a4qqkml3FAJ3auqB2kN2EMPocb/iu0ykeELwed+9B1c1nQ1HKgslKMHMPavYx3Cd/QAx2mBD4hwKBqZXEI/CsQ==
  dependencies:
    "@babel/core" "^7.12.16"
    "@vue/babel-preset-app" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    babel-loader "^8.2.2"
    thread-loader "^3.0.0"
    webpack "^5.54.0"

"@vue/cli-plugin-eslint@~5.0.0":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.8.tgz"
  integrity sha512-d11+I5ONYaAPW1KyZj9GlrV/E6HZePq5L5eAF5GgoVdu6sxr6bDgEoxzhcS1Pk2eh8rn1MxG/FyyR+eCBj/CNg==
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"
    eslint-webpack-plugin "^3.1.0"
    globby "^11.0.2"
    webpack "^5.54.0"
    yorkie "^2.0.0"

"@vue/cli-plugin-router@^5.0.8":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-router/-/cli-plugin-router-5.0.8.tgz"
  integrity sha512-Gmv4dsGdAsWPqVijz3Ux2OS2HkMrWi1ENj2cYL75nUeL+Xj5HEstSqdtfZ0b1q9NCce+BFB6QnHfTBXc/fCvMg==
  dependencies:
    "@vue/cli-shared-utils" "^5.0.8"

"@vue/cli-plugin-vuex@^5.0.8":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-plugin-vuex/-/cli-plugin-vuex-5.0.8.tgz"
  integrity sha512-HSYWPqrunRE5ZZs8kVwiY6oWcn95qf/OQabwLfprhdpFWAGtLStShjsGED2aDpSSeGAskQETrtR/5h7VqgIlBA==

"@vue/cli-service@^3.0.0 || ^4.0.0 || ^5.0.0-0", "@vue/cli-service@~5.0.0":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-service/-/cli-service-5.0.8.tgz"
  integrity sha512-nV7tYQLe7YsTtzFrfOMIHc5N2hp5lHG2rpYr0aNja9rNljdgcPZLyQRb2YRivTHqTv7lI962UXFURcpStHgyFw==
  dependencies:
    "@babel/helper-compilation-targets" "^7.12.16"
    "@soda/friendly-errors-webpack-plugin" "^1.8.0"
    "@soda/get-current-script" "^1.0.2"
    "@types/minimist" "^1.2.0"
    "@vue/cli-overlay" "^5.0.8"
    "@vue/cli-plugin-router" "^5.0.8"
    "@vue/cli-plugin-vuex" "^5.0.8"
    "@vue/cli-shared-utils" "^5.0.8"
    "@vue/component-compiler-utils" "^3.3.0"
    "@vue/vue-loader-v15" "npm:vue-loader@^15.9.7"
    "@vue/web-component-wrapper" "^1.3.0"
    acorn "^8.0.5"
    acorn-walk "^8.0.2"
    address "^1.1.2"
    autoprefixer "^10.2.4"
    browserslist "^4.16.3"
    case-sensitive-paths-webpack-plugin "^2.3.0"
    cli-highlight "^2.1.10"
    clipboardy "^2.3.0"
    cliui "^7.0.4"
    copy-webpack-plugin "^9.0.1"
    css-loader "^6.5.0"
    css-minimizer-webpack-plugin "^3.0.2"
    cssnano "^5.0.0"
    debug "^4.1.1"
    default-gateway "^6.0.3"
    dotenv "^10.0.0"
    dotenv-expand "^5.1.0"
    fs-extra "^9.1.0"
    globby "^11.0.2"
    hash-sum "^2.0.0"
    html-webpack-plugin "^5.1.0"
    is-file-esm "^1.0.0"
    launch-editor-middleware "^2.2.1"
    lodash.defaultsdeep "^4.6.1"
    lodash.mapvalues "^4.6.0"
    mini-css-extract-plugin "^2.5.3"
    minimist "^1.2.5"
    module-alias "^2.2.2"
    portfinder "^1.0.26"
    postcss "^8.2.6"
    postcss-loader "^6.1.1"
    progress-webpack-plugin "^1.0.12"
    ssri "^8.0.1"
    terser-webpack-plugin "^5.1.1"
    thread-loader "^3.0.0"
    vue-loader "^17.0.0"
    vue-style-loader "^4.1.3"
    webpack "^5.54.0"
    webpack-bundle-analyzer "^4.4.0"
    webpack-chain "^6.5.1"
    webpack-dev-server "^4.7.3"
    webpack-merge "^5.7.3"
    webpack-virtual-modules "^0.4.2"
    whatwg-fetch "^3.6.2"

"@vue/cli-shared-utils@^5.0.8":
  version "5.0.8"
  resolved "https://registry.npmmirror.com/@vue/cli-shared-utils/-/cli-shared-utils-5.0.8.tgz"
  integrity sha512-uK2YB7bBVuQhjOJF+O52P9yFMXeJVj7ozqJkwYE9PlMHL1LMHjtCYm4cSdOebuPzyP+/9p0BimM/OqxsevIopQ==
  dependencies:
    "@achrinza/node-ipc" "^9.2.5"
    chalk "^4.1.2"
    execa "^1.0.0"
    joi "^17.4.0"
    launch-editor "^2.2.1"
    lru-cache "^6.0.0"
    node-fetch "^2.6.7"
    open "^8.0.2"
    ora "^5.3.0"
    read-pkg "^5.1.1"
    semver "^7.3.4"
    strip-ansi "^6.0.0"

"@vue/compiler-core@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/compiler-core/-/compiler-core-3.4.27.tgz"
  integrity sha512-E+RyqY24KnyDXsCuQrI+mlcdW3ALND6U7Gqa/+bVwbcpcR3BRRIckFoz7Qyd4TTlnugtwuI7YgjbvsLmxb+yvg==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/shared" "3.4.27"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/compiler-dom/-/compiler-dom-3.4.27.tgz"
  integrity sha512-kUTvochG/oVgE1w5ViSr3KUBh9X7CWirebA3bezTbB5ZKBQZwR2Mwj9uoSKRMFcz4gSMzzLXBPD6KpCLb9nvWw==
  dependencies:
    "@vue/compiler-core" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/compiler-sfc@^3.4.15", "@vue/compiler-sfc@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.4.27.tgz"
  integrity sha512-nDwntUEADssW8e0rrmE0+OrONwmRlegDA1pD6QhVeXxjIytV03yDqTey9SBDiALsvAd5U4ZrEKbMyVXhX6mCGA==
  dependencies:
    "@babel/parser" "^7.24.4"
    "@vue/compiler-core" "3.4.27"
    "@vue/compiler-dom" "3.4.27"
    "@vue/compiler-ssr" "3.4.27"
    "@vue/shared" "3.4.27"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.38"
    source-map-js "^1.2.0"

"@vue/compiler-ssr@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/compiler-ssr/-/compiler-ssr-3.4.27.tgz"
  integrity sha512-CVRzSJIltzMG5FcidsW0jKNQnNRYC8bT21VegyMMtHmhW3UOI7knmUehzswXLrExDLE6lQCZdrhD4ogI7c+vuw==
  dependencies:
    "@vue/compiler-dom" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.3.0":
  version "3.3.0"
  resolved "https://registry.npmmirror.com/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz"
  integrity sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==
  dependencies:
    consolidate "^0.15.1"
    hash-sum "^1.0.2"
    lru-cache "^4.1.2"
    merge-source-map "^1.1.0"
    postcss "^7.0.36"
    postcss-selector-parser "^6.0.2"
    source-map "~0.6.1"
    vue-template-es2015-compiler "^1.9.0"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/reactivity@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.4.27.tgz"
  integrity sha512-kK0g4NknW6JX2yySLpsm2jlunZJl2/RJGZ0H9ddHdfBVHcNzxmQ0sS0b09ipmBoQpY8JM2KmUw+a6sO8Zo+zIA==
  dependencies:
    "@vue/shared" "3.4.27"

"@vue/runtime-core@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/runtime-core/-/runtime-core-3.4.27.tgz"
  integrity sha512-7aYA9GEbOOdviqVvcuweTLe5Za4qBZkUY7SvET6vE8kyypxVgaT1ixHLg4urtOlrApdgcdgHoTZCUuTGap/5WA==
  dependencies:
    "@vue/reactivity" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/runtime-dom@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/runtime-dom/-/runtime-dom-3.4.27.tgz"
  integrity sha512-ScOmP70/3NPM+TW9hvVAz6VWWtZJqkbdf7w6ySsws+EsqtHvkhxaWLecrTorFxsawelM5Ys9FnDEMt6BPBDS0Q==
  dependencies:
    "@vue/runtime-core" "3.4.27"
    "@vue/shared" "3.4.27"
    csstype "^3.1.3"

"@vue/server-renderer@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/server-renderer/-/server-renderer-3.4.27.tgz"
  integrity sha512-dlAMEuvmeA3rJsOMJ2J1kXU7o7pOxgsNHVr9K8hB3ImIkSuBrIdy0vF66h8gf8Tuinf1TK3mPAz2+2sqyf3KzA==
  dependencies:
    "@vue/compiler-ssr" "3.4.27"
    "@vue/shared" "3.4.27"

"@vue/shared@3.4.27":
  version "3.4.27"
  resolved "https://registry.npmmirror.com/@vue/shared/-/shared-3.4.27.tgz"
  integrity sha512-DL3NmY2OFlqmYYrzp39yi3LDkKxa5vZVwxWdQ3rG0ekuWscHraeIbnI8t+aZK7qhYqEqWKTUdijadunb9pnrgA==

"@vue/vue-loader-v15@npm:vue-loader@^15.9.7":
  version "15.11.1"
  resolved "https://registry.npmmirror.com/vue-loader/-/vue-loader-15.11.1.tgz"
  integrity sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    hash-sum "^1.0.2"
    loader-utils "^1.1.0"
    vue-hot-reload-api "^2.3.0"
    vue-style-loader "^4.1.0"

"@vue/web-component-wrapper@^1.3.0":
  version "1.3.0"
  resolved "https://registry.npmmirror.com/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz"
  integrity sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA==

"@webassemblyjs/ast@^1.12.1", "@webassemblyjs/ast@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/ast/-/ast-1.12.1.tgz"
  integrity sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"

"@webassemblyjs/floating-point-hex-parser@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.6.tgz"
  integrity sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==

"@webassemblyjs/helper-api-error@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.6.tgz"
  integrity sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==

"@webassemblyjs/helper-buffer@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/-/helper-buffer-1.12.1.tgz"
  integrity sha512-nzJwQw99DNDKr9BVCOZcLuJJUlqkJh+kVzVl6Fmq/tI5ZtEyWT1KZMyOXltXLZJmDtvLCDgwsyrkohEtopTXCw==

"@webassemblyjs/helper-numbers@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.6.tgz"
  integrity sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.6"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.6.tgz"
  integrity sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==

"@webassemblyjs/helper-wasm-section@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.12.1.tgz"
  integrity sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/wasm-gen" "1.12.1"

"@webassemblyjs/ieee754@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/ieee754/-/ieee754-1.11.6.tgz"
  integrity sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/leb128/-/leb128-1.11.6.tgz"
  integrity sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.6":
  version "1.11.6"
  resolved "https://registry.npmmirror.com/@webassemblyjs/utf8/-/utf8-1.11.6.tgz"
  integrity sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==

"@webassemblyjs/wasm-edit@^1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/-/wasm-edit-1.12.1.tgz"
  integrity sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/helper-wasm-section" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-opt" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"
    "@webassemblyjs/wast-printer" "1.12.1"

"@webassemblyjs/wasm-gen@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/-/wasm-gen-1.12.1.tgz"
  integrity sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wasm-opt@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/-/wasm-opt-1.12.1.tgz"
  integrity sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-buffer" "1.12.1"
    "@webassemblyjs/wasm-gen" "1.12.1"
    "@webassemblyjs/wasm-parser" "1.12.1"

"@webassemblyjs/wasm-parser@^1.12.1", "@webassemblyjs/wasm-parser@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/-/wasm-parser-1.12.1.tgz"
  integrity sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@webassemblyjs/helper-api-error" "1.11.6"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.6"
    "@webassemblyjs/ieee754" "1.11.6"
    "@webassemblyjs/leb128" "1.11.6"
    "@webassemblyjs/utf8" "1.11.6"

"@webassemblyjs/wast-printer@1.12.1":
  version "1.12.1"
  resolved "https://registry.npmmirror.com/@webassemblyjs/wast-printer/-/wast-printer-1.12.1.tgz"
  integrity sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==
  dependencies:
    "@webassemblyjs/ast" "1.12.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmmirror.com/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmmirror.com/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.8:
  version "1.3.8"
  resolved "https://registry.npmmirror.com/accepts/-/accepts-1.3.8.tgz"
  integrity sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

acorn-import-assertions@^1.9.0:
  version "1.9.0"
  resolved "https://registry.npmmirror.com/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz"
  integrity sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==

acorn-jsx@^5.3.1, acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmmirror.com/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

acorn-walk@^8.0.0, acorn-walk@^8.0.2:
  version "8.3.2"
  resolved "https://registry.npmmirror.com/acorn-walk/-/acorn-walk-8.3.2.tgz"
  integrity sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8, acorn@^8.0.4, acorn@^8.0.5, acorn@^8.7.1, acorn@^8.8.2, acorn@^8.9.0:
  version "8.11.3"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-8.11.3.tgz"
  integrity sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==

acorn@^7.4.0:
  version "7.4.1"
  resolved "https://registry.npmmirror.com/acorn/-/acorn-7.4.1.tgz"
  integrity sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==

address@^1.1.2:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/address/-/address-1.2.2.tgz"
  integrity sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ajv-formats/-/ajv-formats-2.1.1.tgz"
  integrity sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv-keywords@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/ajv-keywords/-/ajv-keywords-5.1.0.tgz"
  integrity sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.5, ajv@^6.9.1:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.8.2, ajv@^8.9.0:
  version "8.13.0"
  resolved "https://registry.npmmirror.com/ajv/-/ajv-8.13.0.tgz"
  integrity sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==
  dependencies:
    fast-deep-equal "^3.1.3"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.4.1"

ajv@^8.0.1:
  version "8.13.0"
  resolved "https://registry.npmmirror.com/ajv/-/ajv-8.13.0.tgz"
  integrity sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==
  dependencies:
    fast-deep-equal "^3.1.3"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.4.1"

ansi-colors@^4.1.1:
  version "4.1.3"
  resolved "https://registry.npmmirror.com/ansi-colors/-/ansi-colors-4.1.3.tgz"
  integrity sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==

ansi-escapes@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/ansi-escapes/-/ansi-escapes-3.2.0.tgz"
  integrity sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://registry.npmmirror.com/ansi-html-community/-/ansi-html-community-0.0.8.tgz"
  integrity sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-3.0.1.tgz"
  integrity sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arch@^2.1.1:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/arch/-/arch-2.2.0.tgz"
  integrity sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==

argparse@^1.0.7:
  version "1.0.10"
  resolved "https://registry.npmmirror.com/argparse/-/argparse-1.0.10.tgz"
  integrity sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==
  dependencies:
    sprintf-js "~1.0.2"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/array-flatten/-/array-flatten-1.1.1.tgz"
  integrity sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/astral-regex/-/astral-regex-2.0.0.tgz"
  integrity sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==

async@^2.6.4:
  version "2.6.4"
  resolved "https://registry.npmmirror.com/async/-/async-2.6.4.tgz"
  integrity sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==
  dependencies:
    lodash "^4.17.14"

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/at-least-node/-/at-least-node-1.0.0.tgz"
  integrity sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==

autoprefixer@^10.2.4:
  version "10.4.19"
  resolved "https://registry.npmmirror.com/autoprefixer/-/autoprefixer-10.4.19.tgz"
  integrity sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==
  dependencies:
    browserslist "^4.23.0"
    caniuse-lite "^1.0.30001599"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

babel-loader@^8.2.2:
  version "8.3.0"
  resolved "https://registry.npmmirror.com/babel-loader/-/babel-loader-8.3.0.tgz"
  integrity sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q==
  dependencies:
    find-cache-dir "^3.3.1"
    loader-utils "^2.0.0"
    make-dir "^3.1.0"
    schema-utils "^2.6.5"

babel-plugin-dynamic-import-node@^2.3.3:
  version "2.3.3"
  resolved "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz"
  integrity sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==
  dependencies:
    object.assign "^4.1.0"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.11"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.11.tgz"
  integrity sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q==
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.1, babel-plugin-polyfill-corejs3@^0.10.4:
  version "0.10.4"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.4.tgz"
  integrity sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.1"
    core-js-compat "^3.36.1"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.2"
  resolved "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.2.tgz"
  integrity sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg==
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-js@^1.3.1:
  version "1.5.1"
  resolved "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz"
  integrity sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/batch/-/batch-0.6.1.tgz"
  integrity sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

bl@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/bl/-/bl-4.1.0.tgz"
  integrity sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.1.1:
  version "3.7.2"
  resolved "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz"
  integrity sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==

body-parser@1.20.2:
  version "1.20.2"
  resolved "https://registry.npmmirror.com/body-parser/-/body-parser-1.20.2.tgz"
  integrity sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.11.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

bonjour-service@^1.0.11:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/bonjour-service/-/bonjour-service-1.2.1.tgz"
  integrity sha512-oSzCS2zV14bh2kji6vNe7vrpJYCHGvcZnlffFQ1MEoX/WOeQ/teD8SYWKR942OI3INjq8OMNJlbPK5LLLUxFDw==
  dependencies:
    fast-deep-equal "^3.1.3"
    multicast-dns "^7.2.5"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz"
  integrity sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmmirror.com/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/braces/-/braces-3.0.2.tgz"
  integrity sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.0.0, browserslist@^4.16.3, browserslist@^4.21.10, browserslist@^4.21.4, browserslist@^4.22.2, browserslist@^4.23.0, "browserslist@>= 4.21.0":
  version "4.23.0"
  resolved "https://registry.npmmirror.com/browserslist/-/browserslist-4.23.0.tgz"
  integrity sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==
  dependencies:
    caniuse-lite "^1.0.30001587"
    electron-to-chromium "^1.4.668"
    node-releases "^2.0.14"
    update-browserslist-db "^1.0.13"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer@^5.5.0:
  version "5.7.1"
  resolved "https://registry.npmmirror.com/buffer/-/buffer-5.7.1.tgz"
  integrity sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/bytes/-/bytes-3.0.0.tgz"
  integrity sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==

bytes@3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/bytes/-/bytes-3.1.2.tgz"
  integrity sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==

call-bind@^1.0.5, call-bind@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/call-bind/-/call-bind-1.0.7.tgz"
  integrity sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/camel-case/-/camel-case-4.1.2.tgz"
  integrity sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

camelcase@^5.0.0:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-5.3.1.tgz"
  integrity sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==

camelcase@^6.3.0:
  version "6.3.0"
  resolved "https://registry.npmmirror.com/camelcase/-/camelcase-6.3.0.tgz"
  integrity sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/caniuse-api/-/caniuse-api-3.0.0.tgz"
  integrity sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001587, caniuse-lite@^1.0.30001599:
  version "1.0.30001617"
  resolved "https://registry.npmmirror.com/caniuse-lite/-/caniuse-lite-1.0.30001617.tgz"
  integrity sha512-mLyjzNI9I+Pix8zwcrpxEbGlfqOkF9kM3ptzmKNw5tizSyYwMe+nGLTqMK9cO+0E+Bh6TsBxNAaHWEM8xwSsmA==

case-sensitive-paths-webpack-plugin@^2.3.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  integrity sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw==

chalk@^2.1.0:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-3.0.0.tgz"
  integrity sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.5.3:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz"
  integrity sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==

ci-info@^1.5.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/ci-info/-/ci-info-1.6.0.tgz"
  integrity sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==

clean-css@^5.2.2:
  version "5.3.3"
  resolved "https://registry.npmmirror.com/clean-css/-/clean-css-5.3.3.tgz"
  integrity sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==
  dependencies:
    source-map "~0.6.0"

cli-cursor@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-2.1.0.tgz"
  integrity sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/cli-cursor/-/cli-cursor-3.1.0.tgz"
  integrity sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==
  dependencies:
    restore-cursor "^3.1.0"

cli-highlight@^2.1.10:
  version "2.1.11"
  resolved "https://registry.npmmirror.com/cli-highlight/-/cli-highlight-2.1.11.tgz"
  integrity sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==
  dependencies:
    chalk "^4.0.0"
    highlight.js "^10.7.1"
    mz "^2.4.0"
    parse5 "^5.1.1"
    parse5-htmlparser2-tree-adapter "^6.0.0"
    yargs "^16.0.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "https://registry.npmmirror.com/cli-spinners/-/cli-spinners-2.9.2.tgz"
  integrity sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==

clipboardy@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/clipboardy/-/clipboardy-2.3.0.tgz"
  integrity sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==
  dependencies:
    arch "^2.1.1"
    execa "^1.0.0"
    is-wsl "^2.1.1"

cliui@^7.0.2, cliui@^7.0.4:
  version "7.0.4"
  resolved "https://registry.npmmirror.com/cliui/-/cliui-7.0.4.tgz"
  integrity sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/clone-deep/-/clone-deep-4.0.1.tgz"
  integrity sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/clone/-/clone-1.0.4.tgz"
  integrity sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmmirror.com/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

colord@^2.9.1:
  version "2.9.3"
  resolved "https://registry.npmmirror.com/colord/-/colord-2.9.3.tgz"
  integrity sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==

colorette@^2.0.10:
  version "2.0.20"
  resolved "https://registry.npmmirror.com/colorette/-/colorette-2.0.20.tgz"
  integrity sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^7.2.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/commander/-/commander-7.2.0.tgz"
  integrity sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==

commander@^8.3.0:
  version "8.3.0"
  resolved "https://registry.npmmirror.com/commander/-/commander-8.3.0.tgz"
  integrity sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/commondir/-/commondir-1.0.1.tgz"
  integrity sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.npmmirror.com/compressible/-/compressible-2.0.18.tgz"
  integrity sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npmmirror.com/compression/-/compression-1.7.4.tgz"
  integrity sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmmirror.com/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

connect-history-api-fallback@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz"
  integrity sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==

consolidate@^0.15.1:
  version "0.15.1"
  resolved "https://registry.npmmirror.com/consolidate/-/consolidate-0.15.1.tgz"
  integrity sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==
  dependencies:
    bluebird "^3.1.1"

content-disposition@0.5.4:
  version "0.5.4"
  resolved "https://registry.npmmirror.com/content-disposition/-/content-disposition-0.5.4.tgz"
  integrity sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/content-type/-/content-type-1.0.5.tgz"
  integrity sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/convert-source-map/-/convert-source-map-2.0.0.tgz"
  integrity sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/cookie-signature/-/cookie-signature-1.0.6.tgz"
  integrity sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==

cookie@0.6.0:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/cookie/-/cookie-0.6.0.tgz"
  integrity sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==

copy-webpack-plugin@^9.0.1:
  version "9.1.0"
  resolved "https://registry.npmmirror.com/copy-webpack-plugin/-/copy-webpack-plugin-9.1.0.tgz"
  integrity sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA==
  dependencies:
    fast-glob "^3.2.7"
    glob-parent "^6.0.1"
    globby "^11.0.3"
    normalize-path "^3.0.0"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"

core-js-compat@^3.31.0, core-js-compat@^3.36.1, core-js-compat@^3.8.3:
  version "3.37.0"
  resolved "https://registry.npmmirror.com/core-js-compat/-/core-js-compat-3.37.0.tgz"
  integrity sha512-vYq4L+T8aS5UuFg4UwDhc7YNRWVeVZwltad9C/jV3R2LgVOpS9BDr7l/WL6BN0dbV3k1XejPTHqqEzJgsa0frA==
  dependencies:
    browserslist "^4.23.0"

core-js@^3.8.3:
  version "3.37.0"
  resolved "https://registry.npmmirror.com/core-js/-/core-js-3.37.0.tgz"
  integrity sha512-fu5vHevQ8ZG4og+LXug8ulUtVxjOcEYvifJr7L5Bfq9GOztVqsKd9/59hUk2ZSbCrS3BqUr3EpaYGIYzq7g3Ug==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmmirror.com/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-spawn@^5.0.1:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-5.1.0.tgz"
  integrity sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^6.0.0:
  version "6.0.5"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-6.0.5.tgz"
  integrity sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.npmmirror.com/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-declaration-sorter@^6.3.1:
  version "6.4.1"
  resolved "https://registry.npmmirror.com/css-declaration-sorter/-/css-declaration-sorter-6.4.1.tgz"
  integrity sha512-rtdthzxKuyq6IzqX6jEcIzQF/YqccluefyCYheovBOLhFT/drQA9zj/UbRAa9J7C0o6EG6u3E6g+vKkay7/k3g==

css-loader@*, css-loader@^6.5.0:
  version "6.11.0"
  resolved "https://registry.npmmirror.com/css-loader/-/css-loader-6.11.0.tgz"
  integrity sha512-CTJ+AEQJjq5NzLga5pE39qdiSV56F8ywCIsqNIRF0r7BDgWsN25aazToqAFg7ZrtA/U016xudB3ffgweORxX7g==
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.4.33"
    postcss-modules-extract-imports "^3.1.0"
    postcss-modules-local-by-default "^4.0.5"
    postcss-modules-scope "^3.2.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.2.0"
    semver "^7.5.4"

css-minimizer-webpack-plugin@^3.0.2:
  version "3.4.1"
  resolved "https://registry.npmmirror.com/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz"
  integrity sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==
  dependencies:
    cssnano "^5.0.6"
    jest-worker "^27.0.2"
    postcss "^8.3.5"
    schema-utils "^4.0.0"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"

css-select@^4.1.3:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/css-select/-/css-select-4.3.0.tgz"
  integrity sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.0.1"
    domhandler "^4.3.1"
    domutils "^2.8.0"
    nth-check "^2.0.1"

css-tree@^1.1.2, css-tree@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmmirror.com/css-tree/-/css-tree-1.1.3.tgz"
  integrity sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==
  dependencies:
    mdn-data "2.0.14"
    source-map "^0.6.1"

css-what@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/css-what/-/css-what-6.1.0.tgz"
  integrity sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

cssnano-preset-default@^5.2.14:
  version "5.2.14"
  resolved "https://registry.npmmirror.com/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz"
  integrity sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A==
  dependencies:
    css-declaration-sorter "^6.3.1"
    cssnano-utils "^3.1.0"
    postcss-calc "^8.2.3"
    postcss-colormin "^5.3.1"
    postcss-convert-values "^5.1.3"
    postcss-discard-comments "^5.1.2"
    postcss-discard-duplicates "^5.1.0"
    postcss-discard-empty "^5.1.1"
    postcss-discard-overridden "^5.1.0"
    postcss-merge-longhand "^5.1.7"
    postcss-merge-rules "^5.1.4"
    postcss-minify-font-values "^5.1.0"
    postcss-minify-gradients "^5.1.1"
    postcss-minify-params "^5.1.4"
    postcss-minify-selectors "^5.2.1"
    postcss-normalize-charset "^5.1.0"
    postcss-normalize-display-values "^5.1.0"
    postcss-normalize-positions "^5.1.1"
    postcss-normalize-repeat-style "^5.1.1"
    postcss-normalize-string "^5.1.0"
    postcss-normalize-timing-functions "^5.1.0"
    postcss-normalize-unicode "^5.1.1"
    postcss-normalize-url "^5.1.0"
    postcss-normalize-whitespace "^5.1.1"
    postcss-ordered-values "^5.1.3"
    postcss-reduce-initial "^5.1.2"
    postcss-reduce-transforms "^5.1.0"
    postcss-svgo "^5.1.0"
    postcss-unique-selectors "^5.1.1"

cssnano-utils@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/cssnano-utils/-/cssnano-utils-3.1.0.tgz"
  integrity sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==

cssnano@^5.0.0, cssnano@^5.0.6:
  version "5.1.15"
  resolved "https://registry.npmmirror.com/cssnano/-/cssnano-5.1.15.tgz"
  integrity sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw==
  dependencies:
    cssnano-preset-default "^5.2.14"
    lilconfig "^2.0.3"
    yaml "^1.10.2"

csso@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/csso/-/csso-4.2.0.tgz"
  integrity sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==
  dependencies:
    css-tree "^1.1.2"

csstype@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/csstype/-/csstype-3.1.3.tgz"
  integrity sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==

debounce@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/debounce/-/debounce-1.2.1.tgz"
  integrity sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.0.1, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2:
  version "4.3.4"
  resolved "https://registry.npmmirror.com/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

debug@2.6.9:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmmirror.com/deepmerge/-/deepmerge-1.5.2.tgz"
  integrity sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==

default-gateway@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/default-gateway/-/default-gateway-6.0.3.tgz"
  integrity sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==
  dependencies:
    execa "^5.0.0"

defaults@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/defaults/-/defaults-1.0.4.tgz"
  integrity sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmmirror.com/define-data-property/-/define-data-property-1.1.4.tgz"
  integrity sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz"
  integrity sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==

define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/depd/-/depd-1.1.2.tgz"
  integrity sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==

depd@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/depd/-/depd-2.0.0.tgz"
  integrity sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==

destroy@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/destroy/-/destroy-1.2.0.tgz"
  integrity sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/detect-node/-/detect-node-2.1.0.tgz"
  integrity sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

dns-packet@^5.2.2:
  version "5.6.1"
  resolved "https://registry.npmmirror.com/dns-packet/-/dns-packet-5.6.1.tgz"
  integrity sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==
  dependencies:
    "@leichtgewicht/ip-codec" "^2.0.1"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/dom-converter/-/dom-converter-0.2.0.tgz"
  integrity sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-1.4.1.tgz"
  integrity sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/domelementtype/-/domelementtype-2.3.0.tgz"
  integrity sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==

domhandler@^4.0.0, domhandler@^4.2.0, domhandler@^4.3.1:
  version "4.3.1"
  resolved "https://registry.npmmirror.com/domhandler/-/domhandler-4.3.1.tgz"
  integrity sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==
  dependencies:
    domelementtype "^2.2.0"

domutils@^2.5.2, domutils@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/domutils/-/domutils-2.8.0.tgz"
  integrity sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/dot-case/-/dot-case-3.0.4.tgz"
  integrity sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv-expand@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/dotenv-expand/-/dotenv-expand-5.1.0.tgz"
  integrity sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==

dotenv@^10.0.0:
  version "10.0.0"
  resolved "https://registry.npmmirror.com/dotenv/-/dotenv-10.0.0.tgz"
  integrity sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==

duplexer@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/duplexer/-/duplexer-0.1.2.tgz"
  integrity sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==

easy-stack@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/easy-stack/-/easy-stack-1.0.1.tgz"
  integrity sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/ee-first/-/ee-first-1.1.1.tgz"
  integrity sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==

electron-to-chromium@^1.4.668:
  version "1.4.765"
  resolved "https://registry.npmmirror.com/electron-to-chromium/-/electron-to-chromium-1.4.765.tgz"
  integrity sha512-70APzI2AGyJgcWVSnfJCytP2Gejptk6cIm0t5uuUfwdKN63xBIZBzD0N5l/s0hWr8tj0w/p6UaPz+hLAm+Orjw==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/encodeurl/-/encodeurl-1.0.2.tgz"
  integrity sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.npmmirror.com/end-of-stream/-/end-of-stream-1.4.4.tgz"
  integrity sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==
  dependencies:
    once "^1.4.0"

enhanced-resolve@^5.16.0:
  version "5.16.1"
  resolved "https://registry.npmmirror.com/enhanced-resolve/-/enhanced-resolve-5.16.1.tgz"
  integrity sha512-4U5pNsuDl0EhuZpq46M5xPslstkviJuhrdobaRDBk2Jy2KO37FDAJl4lb2KlNabxT0m4MTK2UHNrsAcphE8nyw==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/enquirer/-/enquirer-2.4.1.tgz"
  integrity sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/entities/-/entities-2.2.0.tgz"
  integrity sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==

entities@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/entities/-/entities-4.5.0.tgz"
  integrity sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmmirror.com/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser@^2.0.6:
  version "2.1.4"
  resolved "https://registry.npmmirror.com/error-stack-parser/-/error-stack-parser-2.1.4.tgz"
  integrity sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==
  dependencies:
    stackframe "^1.3.4"

es-define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/es-define-property/-/es-define-property-1.0.0.tgz"
  integrity sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==
  dependencies:
    get-intrinsic "^1.2.4"

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/es-errors/-/es-errors-1.3.0.tgz"
  integrity sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==

es-module-lexer@^1.2.1:
  version "1.5.2"
  resolved "https://registry.npmmirror.com/es-module-lexer/-/es-module-lexer-1.5.2.tgz"
  integrity sha512-l60ETUTmLqbVbVHv1J4/qj+M8nq7AwMzEcg3kmJDt9dCNrTk+yHcYFf/Kw75pMDwd9mPcIGCG5LcS20SxYRzFA==

escalade@^3.1.1, escalade@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/escalade/-/escalade-3.1.2.tgz"
  integrity sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/escape-html/-/escape-html-1.0.3.tgz"
  integrity sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-plugin-vue@^8.0.3:
  version "8.7.1"
  resolved "https://registry.npmmirror.com/eslint-plugin-vue/-/eslint-plugin-vue-8.7.1.tgz"
  integrity sha512-28sbtm4l4cOzoO1LtzQPxfxhQABararUb1JtqusQqObJpWX2e/gmVyeYVfepizPFne0Q5cILkYGiBoV36L12Wg==
  dependencies:
    eslint-utils "^3.0.0"
    natural-compare "^1.4.0"
    nth-check "^2.0.1"
    postcss-selector-parser "^6.0.9"
    semver "^7.3.5"
    vue-eslint-parser "^8.0.1"

eslint-scope@^5.1.1, eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.0.0:
  version "7.2.2"
  resolved "https://registry.npmmirror.com/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-2.1.0.tgz"
  integrity sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==
  dependencies:
    eslint-visitor-keys "^1.1.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/eslint-utils/-/eslint-utils-3.0.0.tgz"
  integrity sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^1.1.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==

eslint-visitor-keys@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz"
  integrity sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==

eslint-visitor-keys@^2.0.0, eslint-visitor-keys@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.1.0, eslint-visitor-keys@^3.4.1:
  version "3.4.3"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint-webpack-plugin@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz"
  integrity sha512-avrKcGncpPbPSUHX6B3stNGzkKFto3eL+DKM4+VyMrVnhPc3vRczVlCq3uhuFOdRvDHTVXuzwk1ZKUrqDQHQ9w==
  dependencies:
    "@types/eslint" "^7.29.0 || ^8.4.1"
    jest-worker "^28.0.2"
    micromatch "^4.0.5"
    normalize-path "^3.0.0"
    schema-utils "^4.0.0"

"eslint@^6.2.0 || ^7.0.0 || ^8.0.0", "eslint@^7.0.0 || ^8.0.0", eslint@^7.32.0, "eslint@^7.5.0 || ^8.0.0 || ^9.0.0", eslint@>=5, eslint@>=6.0.0, eslint@>=7.5.0:
  version "7.32.0"
  resolved "https://registry.npmmirror.com/eslint/-/eslint-7.32.0.tgz"
  integrity sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.0.1"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^5.1.1"
    eslint-utils "^2.1.0"
    eslint-visitor-keys "^2.0.0"
    espree "^7.3.1"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.1.2"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.1.0"
    semver "^7.2.1"
    strip-ansi "^6.0.0"
    strip-json-comments "^3.1.0"
    table "^6.0.9"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^7.3.0, espree@^7.3.1:
  version "7.3.1"
  resolved "https://registry.npmmirror.com/espree/-/espree-7.3.1.tgz"
  integrity sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==
  dependencies:
    acorn "^7.4.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^1.3.0"

espree@^9.0.0:
  version "9.6.1"
  resolved "https://registry.npmmirror.com/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/esprima/-/esprima-4.0.1.tgz"
  integrity sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==

esquery@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmmirror.com/esquery/-/esquery-1.5.0.tgz"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/estree-walker/-/estree-walker-2.0.2.tgz"
  integrity sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/etag/-/etag-1.8.1.tgz"
  integrity sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==

event-pubsub@4.3.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/event-pubsub/-/event-pubsub-4.3.0.tgz"
  integrity sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.npmmirror.com/eventemitter3/-/eventemitter3-4.0.7.tgz"
  integrity sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==

events@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

execa@^0.8.0:
  version "0.8.0"
  resolved "https://registry.npmmirror.com/execa/-/execa-0.8.0.tgz"
  integrity sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==
  dependencies:
    cross-spawn "^5.0.1"
    get-stream "^3.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/execa/-/execa-1.0.0.tgz"
  integrity sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/-/execa-5.1.1.tgz"
  integrity sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

express@^4.17.3:
  version "4.19.2"
  resolved "https://registry.npmmirror.com/express/-/express-4.19.2.tgz"
  integrity sha512-5T6nhjsT+EOMzuck8JjBHARTHfMht0POzlA60WV2pMD3gyXw2LZnZ+ueGdNxG+0calOJcWKbpFcuzLZ91YWq9Q==
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.2"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.6.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.2.0"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.7"
    qs "6.11.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.18.0"
    serve-static "1.15.0"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-glob@^3.2.7, fast-glob@^3.2.9:
  version "3.3.2"
  resolved "https://registry.npmmirror.com/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastq@^1.6.0:
  version "1.17.1"
  resolved "https://registry.npmmirror.com/fastq/-/fastq-1.17.1.tgz"
  integrity sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://registry.npmmirror.com/faye-websocket/-/faye-websocket-0.11.4.tgz"
  integrity sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==
  dependencies:
    websocket-driver ">=0.5.1"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/figures/-/figures-2.0.0.tgz"
  integrity sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/fill-range/-/fill-range-7.0.1.tgz"
  integrity sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/finalhandler/-/finalhandler-1.2.0.tgz"
  integrity sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

find-cache-dir@^3.3.1:
  version "3.3.2"
  resolved "https://registry.npmmirror.com/find-cache-dir/-/find-cache-dir-3.3.2.tgz"
  integrity sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/-/find-up-4.1.0.tgz"
  integrity sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/flat-cache/-/flat-cache-3.2.0.tgz"
  integrity sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmmirror.com/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

flatted@^3.2.9:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/flatted/-/flatted-3.3.1.tgz"
  integrity sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==

follow-redirects@^1.0.0:
  version "1.15.6"
  resolved "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.15.6.tgz"
  integrity sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/forwarded/-/forwarded-0.2.0.tgz"
  integrity sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://registry.npmmirror.com/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npmmirror.com/fresh/-/fresh-0.5.2.tgz"
  integrity sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==

fs-extra@^9.1.0:
  version "9.1.0"
  resolved "https://registry.npmmirror.com/fs-extra/-/fs-extra-9.1.0.tgz"
  integrity sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-monkey@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/fs-monkey/-/fs-monkey-1.0.6.tgz"
  integrity sha512-b1FMfwetIKymC0eioW7mTywihSQE4oLzQn1dB6rZB5fx/3NpNEdAWeCSMB+60/AeT0TCXsxzAlcYVEFCTAksWg==

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://registry.npmmirror.com/gensync/-/gensync-1.0.0-beta.2.tgz"
  integrity sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/get-caller-file/-/get-caller-file-2.0.5.tgz"
  integrity sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/get-intrinsic/-/get-intrinsic-1.2.4.tgz"
  integrity sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-3.0.0.tgz"
  integrity sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==

get-stream@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-4.1.0.tgz"
  integrity sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/get-stream/-/get-stream-6.0.1.tgz"
  integrity sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^7.1.3:
  version "7.2.3"
  resolved "https://registry.npmmirror.com/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://registry.npmmirror.com/globals/-/globals-11.12.0.tgz"
  integrity sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==

globals@^13.6.0:
  version "13.24.0"
  resolved "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globals@^13.9.0:
  version "13.24.0"
  resolved "https://registry.npmmirror.com/globals/-/globals-13.24.0.tgz"
  integrity sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.2, globby@^11.0.3:
  version "11.1.0"
  resolved "https://registry.npmmirror.com/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.11, graceful-fs@^4.2.4, graceful-fs@^4.2.6:
  version "4.2.11"
  resolved "https://registry.npmmirror.com/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

gzip-size@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/gzip-size/-/gzip-size-6.0.0.tgz"
  integrity sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==
  dependencies:
    duplexer "^0.1.2"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/handle-thing/-/handle-thing-2.0.1.tgz"
  integrity sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz"
  integrity sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has-proto/-/has-proto-1.0.3.tgz"
  integrity sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==

has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/hash-sum/-/hash-sum-1.0.2.tgz"
  integrity sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==

hash-sum@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/hash-sum/-/hash-sum-2.0.0.tgz"
  integrity sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==

hasown@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/hasown/-/hasown-2.0.2.tgz"
  integrity sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/he/-/he-1.2.0.tgz"
  integrity sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==

highlight.js@^10.7.1:
  version "10.7.3"
  resolved "https://registry.npmmirror.com/highlight.js/-/highlight.js-10.7.3.tgz"
  integrity sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.npmmirror.com/hosted-git-info/-/hosted-git-info-2.8.9.tgz"
  integrity sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmmirror.com/hpack.js/-/hpack.js-2.1.6.tgz"
  integrity sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.3.2:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/html-entities/-/html-entities-2.5.2.tgz"
  integrity sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==

html-escaper@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/html-escaper/-/html-escaper-2.0.2.tgz"
  integrity sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==

html-minifier-terser@^6.0.2:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz"
  integrity sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.2.2"
    commander "^8.3.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.10.0"

html-tags@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/html-tags/-/html-tags-2.0.0.tgz"
  integrity sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==

html-tags@^3.3.1:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/html-tags/-/html-tags-3.3.1.tgz"
  integrity sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==

html-webpack-plugin@^5.1.0:
  version "5.6.0"
  resolved "https://registry.npmmirror.com/html-webpack-plugin/-/html-webpack-plugin-5.6.0.tgz"
  integrity sha512-iwaY4wzbe48AfKLZ/Cc8k0L+FKG6oSNRaZ8x5A/T/IVDGyXcbHncM9TdDa93wn0FsSm82FhTKW7f3vS61thXAw==
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-6.1.0.tgz"
  integrity sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmmirror.com/http-deceiver/-/http-deceiver-1.2.7.tgz"
  integrity sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmmirror.com/http-errors/-/http-errors-1.6.3.tgz"
  integrity sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/http-errors/-/http-errors-2.0.0.tgz"
  integrity sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-parser-js@>=0.5.1:
  version "0.5.8"
  resolved "https://registry.npmmirror.com/http-parser-js/-/http-parser-js-0.5.8.tgz"
  integrity sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==

http-proxy-middleware@^2.0.3:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz"
  integrity sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==
  dependencies:
    "@types/http-proxy" "^1.17.8"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.npmmirror.com/http-proxy/-/http-proxy-1.18.1.tgz"
  integrity sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/human-signals/-/human-signals-2.1.0.tgz"
  integrity sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.4.24.tgz"
  integrity sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/icss-utils/-/icss-utils-5.1.0.tgz"
  integrity sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==

ieee754@^1.1.13:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz"
  integrity sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==

ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/-/ignore-4.0.6.tgz"
  integrity sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==

ignore@^5.2.0:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/ignore/-/ignore-5.3.1.tgz"
  integrity sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/inherits/-/inherits-2.0.3.tgz"
  integrity sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==

ipaddr.js@^2.0.1:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-2.2.0.tgz"
  integrity sha512-Ag3wB2o37wslZS19hZqorUnrnzSkpOVy+IiiDEiTqNubEYpYuHWIf6K4psgN2ZWKExS4xhVCrRVfb/wfW8fWJA==

ipaddr.js@1.9.1:
  version "1.9.1"
  resolved "https://registry.npmmirror.com/ipaddr.js/-/ipaddr.js-1.9.1.tgz"
  integrity sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-ci@^1.0.10:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/is-ci/-/is-ci-1.2.1.tgz"
  integrity sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==
  dependencies:
    ci-info "^1.5.0"

is-core-module@^2.13.0:
  version "2.13.1"
  resolved "https://registry.npmmirror.com/is-core-module/-/is-core-module-2.13.1.tgz"
  integrity sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==
  dependencies:
    hasown "^2.0.0"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/is-docker/-/is-docker-2.2.1.tgz"
  integrity sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-file-esm@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-file-esm/-/is-file-esm-1.0.0.tgz"
  integrity sha512-rZlaNKb4Mr8WlRu2A9XdeoKgnO5aA53XdPHgCKVyCrQ/rWi89RET1+bq37Ru46obaQXeiX4vmFIm1vks41hoSA==
  dependencies:
    read-pkg-up "^7.0.1"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz"
  integrity sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/is-interactive/-/is-interactive-1.0.0.tgz"
  integrity sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/is-plain-obj/-/is-plain-obj-3.0.0.tgz"
  integrity sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==

is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz"
  integrity sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==
  dependencies:
    isobject "^3.0.1"

is-stream@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/is-stream/-/is-stream-1.1.0.tgz"
  integrity sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/is-stream/-/is-stream-2.0.1.tgz"
  integrity sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz"
  integrity sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==

is-wsl@^2.1.1, is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/is-wsl/-/is-wsl-2.2.0.tgz"
  integrity sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==
  dependencies:
    is-docker "^2.0.0"

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/isobject/-/isobject-3.0.1.tgz"
  integrity sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==

javascript-stringify@^2.0.1:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/javascript-stringify/-/javascript-stringify-2.1.0.tgz"
  integrity sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==

jest-worker@^27.0.2, jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://registry.npmmirror.com/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest-worker@^28.0.2:
  version "28.1.3"
  resolved "https://registry.npmmirror.com/jest-worker/-/jest-worker-28.1.3.tgz"
  integrity sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

joi@^17.4.0:
  version "17.13.1"
  resolved "https://registry.npmmirror.com/joi/-/joi-17.13.1.tgz"
  integrity sha512-vaBlIKCyo4FCUtCm7Eu4QZd/q02bWcxfUO6YSXAZOWF6gzcLBeba8kwotUdYJjDLW8Cz8RywsSOqiNJZW0mNvg==
  dependencies:
    "@hapi/hoek" "^9.3.0"
    "@hapi/topo" "^5.1.0"
    "@sideway/address" "^4.1.5"
    "@sideway/formula" "^3.0.1"
    "@sideway/pinpoint" "^2.0.0"

js-message@1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/js-message/-/js-message-1.0.7.tgz"
  integrity sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA==

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "https://registry.npmmirror.com/js-yaml/-/js-yaml-3.14.1.tgz"
  integrity sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/jsesc/-/jsesc-2.5.2.tgz"
  integrity sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==

jsesc@~0.5.0:
  version "0.5.0"
  resolved "https://registry.npmmirror.com/jsesc/-/jsesc-0.5.0.tgz"
  integrity sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz"
  integrity sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz"
  integrity sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json5@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://registry.npmmirror.com/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonfile@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/jsonfile/-/jsonfile-6.1.0.tgz"
  integrity sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.npmmirror.com/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/kind-of/-/kind-of-6.0.3.tgz"
  integrity sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==

klona@^2.0.5:
  version "2.0.6"
  resolved "https://registry.npmmirror.com/klona/-/klona-2.0.6.tgz"
  integrity sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==

launch-editor-middleware@^2.2.1:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/launch-editor-middleware/-/launch-editor-middleware-2.6.1.tgz"
  integrity sha512-Fg/xYhf7ARmRp40n18wIfJyuAMEjXo67Yull7uF7d0OJ3qA4EYJISt1XfPPn69IIJ5jKgQwzcg6DqHYo95LL/g==
  dependencies:
    launch-editor "^2.6.1"

launch-editor@^2.2.1, launch-editor@^2.6.0, launch-editor@^2.6.1:
  version "2.6.1"
  resolved "https://registry.npmmirror.com/launch-editor/-/launch-editor-2.6.1.tgz"
  integrity sha512-eB/uXmFVpY4zezmGp5XtU21kwo7GBbKB+EQ+UZeWtGb9yAM5xt/Evk+lYH3eRNAtId+ej4u7TYPFZ07w4s7rRw==
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmmirror.com/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^2.0.3:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/lilconfig/-/lilconfig-2.1.0.tgz"
  integrity sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmmirror.com/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

loader-runner@^4.1.0, loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==

loader-utils@^1.0.2, loader-utils@^1.1.0:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/loader-utils/-/loader-utils-1.4.2.tgz"
  integrity sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^1.0.1"

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/loader-utils/-/loader-utils-2.0.4.tgz"
  integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/locate-path/-/locate-path-5.0.0.tgz"
  integrity sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==
  dependencies:
    p-locate "^4.1.0"

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmmirror.com/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.defaultsdeep@^4.6.1:
  version "4.6.1"
  resolved "https://registry.npmmirror.com/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz"
  integrity sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA==

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  integrity sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==

lodash.mapvalues@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmmirror.com/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz"
  integrity sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ==

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "https://registry.npmmirror.com/lodash.memoize/-/lodash.memoize-4.1.2.tgz"
  integrity sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmmirror.com/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npmmirror.com/lodash.truncate/-/lodash.truncate-4.4.2.tgz"
  integrity sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/lodash.uniq/-/lodash.uniq-4.5.0.tgz"
  integrity sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==

lodash@^4.17.14, lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.npmmirror.com/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/log-symbols/-/log-symbols-4.1.0.tgz"
  integrity sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/log-update/-/log-update-2.3.0.tgz"
  integrity sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg==
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/lower-case/-/lower-case-2.0.2.tgz"
  integrity sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==
  dependencies:
    tslib "^2.0.3"

lru-cache@^4.0.1, lru-cache@^4.1.2:
  version "4.1.5"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-4.1.5.tgz"
  integrity sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-5.1.1.tgz"
  integrity sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

magic-string@^0.30.10:
  version "0.30.10"
  resolved "https://registry.npmmirror.com/magic-string/-/magic-string-0.30.10.tgz"
  integrity sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

make-dir@^3.0.2, make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

mdn-data@2.0.14:
  version "2.0.14"
  resolved "https://registry.npmmirror.com/mdn-data/-/mdn-data-2.0.14.tgz"
  integrity sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.npmmirror.com/media-typer/-/media-typer-0.3.0.tgz"
  integrity sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==

memfs@^3.4.3:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/memfs/-/memfs-3.6.0.tgz"
  integrity sha512-EGowvkkgbMcIChjMTMkESFDbZeSh8xZ7kNSF0hAiAN4Jh6jgHCRS0Ga/+C8y6Au+oqpezRHCfPsmJ2+DwAgiwQ==
  dependencies:
    fs-monkey "^1.0.4"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz"
  integrity sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==

merge-source-map@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/merge-source-map/-/merge-source-map-1.1.0.tgz"
  integrity sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==
  dependencies:
    source-map "^0.6.1"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmmirror.com/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/methods/-/methods-1.1.2.tgz"
  integrity sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==

micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5:
  version "4.0.5"
  resolved "https://registry.npmmirror.com/micromatch/-/micromatch-4.0.5.tgz"
  integrity sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

"mime-db@>= 1.43.0 < 2", mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmmirror.com/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  resolved "https://registry.npmmirror.com/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/-/mime-1.6.0.tgz"
  integrity sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-1.2.0.tgz"
  integrity sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/mimic-fn/-/mimic-fn-2.1.0.tgz"
  integrity sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==

mini-css-extract-plugin@^2.5.3:
  version "2.9.0"
  resolved "https://registry.npmmirror.com/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.0.tgz"
  integrity sha512-Zs1YsZVfemekSZG+44vBsYTLQORkPMwnlv+aehcxK/NLKC+EGhDB39/YePYYqx/sTk6NnYpuqikhSn7+JIevTA==
  dependencies:
    schema-utils "^4.0.0"
    tapable "^2.2.1"

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz"
  integrity sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==

minimatch@^3.0.4, minimatch@^3.1.1:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6:
  version "1.2.8"
  resolved "https://registry.npmmirror.com/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^3.1.1:
  version "3.3.6"
  resolved "https://registry.npmmirror.com/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

mkdirp@^0.5.6:
  version "0.5.6"
  resolved "https://registry.npmmirror.com/mkdirp/-/mkdirp-0.5.6.tgz"
  integrity sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==
  dependencies:
    minimist "^1.2.6"

module-alias@^2.2.2:
  version "2.2.3"
  resolved "https://registry.npmmirror.com/module-alias/-/module-alias-2.2.3.tgz"
  integrity sha512-23g5BFj4zdQL/b6tor7Ji+QY4pEfNH784BMslY9Qb0UnJWRAt+lQGLYmRaM0KDBwIG23ffEBELhZDP2rhi9f/Q==

mrmime@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/mrmime/-/mrmime-2.0.0.tgz"
  integrity sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==

ms@^2.1.1, ms@2.1.3:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

multicast-dns@^7.2.5:
  version "7.2.5"
  resolved "https://registry.npmmirror.com/multicast-dns/-/multicast-dns-7.2.5.tgz"
  integrity sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==
  dependencies:
    dns-packet "^5.2.2"
    thunky "^1.0.2"

mz@^2.4.0:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://registry.npmmirror.com/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

negotiator@0.6.3:
  version "0.6.3"
  resolved "https://registry.npmmirror.com/negotiator/-/negotiator-0.6.3.tgz"
  integrity sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmmirror.com/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npmmirror.com/nice-try/-/nice-try-1.0.5.tgz"
  integrity sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/no-case/-/no-case-3.0.4.tgz"
  integrity sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmmirror.com/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^1:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/node-forge/-/node-forge-1.3.1.tgz"
  integrity sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==

node-releases@^2.0.14:
  version "2.0.14"
  resolved "https://registry.npmmirror.com/node-releases/-/node-releases-2.0.14.tgz"
  integrity sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/normalize-package-data/-/normalize-package-data-2.5.0.tgz"
  integrity sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/normalize-path/-/normalize-path-1.0.0.tgz"
  integrity sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA==

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmmirror.com/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/normalize-url/-/normalize-url-6.1.0.tgz"
  integrity sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-2.0.2.tgz"
  integrity sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/-/npm-run-path-4.0.1.tgz"
  integrity sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/nth-check/-/nth-check-2.1.1.tgz"
  integrity sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==
  dependencies:
    boolbase "^1.0.0"

object-assign@^4.0.1:
  version "4.1.1"
  resolved "https://registry.npmmirror.com/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-inspect@^1.13.1:
  version "1.13.1"
  resolved "https://registry.npmmirror.com/object-inspect/-/object-inspect-1.13.1.tgz"
  integrity sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object.assign@^4.1.0:
  version "4.1.5"
  resolved "https://registry.npmmirror.com/object.assign/-/object.assign-4.1.5.tgz"
  integrity sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==
  dependencies:
    call-bind "^1.0.5"
    define-properties "^1.2.1"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/obuf/-/obuf-1.1.2.tgz"
  integrity sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==

on-finished@2.4.1:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/on-finished/-/on-finished-2.4.1.tgz"
  integrity sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/on-headers/-/on-headers-1.0.2.tgz"
  integrity sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmmirror.com/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/onetime/-/onetime-2.0.1.tgz"
  integrity sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/onetime/-/onetime-5.1.2.tgz"
  integrity sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==
  dependencies:
    mimic-fn "^2.1.0"

open@^8.0.2, open@^8.0.9:
  version "8.4.2"
  resolved "https://registry.npmmirror.com/open/-/open-8.4.2.tgz"
  integrity sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

opener@^1.5.2:
  version "1.5.2"
  resolved "https://registry.npmmirror.com/opener/-/opener-1.5.2.tgz"
  integrity sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==

optionator@^0.9.1:
  version "0.9.4"
  resolved "https://registry.npmmirror.com/optionator/-/optionator-0.9.4.tgz"
  integrity sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.3.0:
  version "5.4.1"
  resolved "https://registry.npmmirror.com/ora/-/ora-5.4.1.tgz"
  integrity sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/p-finally/-/p-finally-1.0.0.tgz"
  integrity sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.npmmirror.com/p-limit/-/p-limit-2.3.0.tgz"
  integrity sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/p-locate/-/p-locate-4.1.0.tgz"
  integrity sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==
  dependencies:
    p-limit "^2.2.0"

p-retry@^4.5.0:
  version "4.6.2"
  resolved "https://registry.npmmirror.com/p-retry/-/p-retry-4.6.2.tgz"
  integrity sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==
  dependencies:
    "@types/retry" "0.12.0"
    retry "^0.13.1"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/-/p-try-2.2.0.tgz"
  integrity sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/param-case/-/param-case-3.0.4.tgz"
  integrity sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  integrity sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==
  dependencies:
    parse5 "^6.0.1"

parse5@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/parse5/-/parse5-5.1.1.tgz"
  integrity sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==

parse5@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/parse5/-/parse5-6.0.1.tgz"
  integrity sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.npmmirror.com/parseurl/-/parseurl-1.3.3.tgz"
  integrity sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmmirror.com/pascal-case/-/pascal-case-3.1.2.tgz"
  integrity sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-exists/-/path-exists-4.0.0.tgz"
  integrity sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-2.0.1.tgz"
  integrity sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==

path-key@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmmirror.com/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npmmirror.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz"
  integrity sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

picocolors@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/picocolors/-/picocolors-0.2.1.tgz"
  integrity sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/-/picocolors-1.0.0.tgz"
  integrity sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pkg-dir@^4.1.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/-/pkg-dir-4.2.0.tgz"
  integrity sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==
  dependencies:
    find-up "^4.0.0"

portfinder@^1.0.26:
  version "1.0.32"
  resolved "https://registry.npmmirror.com/portfinder/-/portfinder-1.0.32.tgz"
  integrity sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==
  dependencies:
    async "^2.6.4"
    debug "^3.2.7"
    mkdirp "^0.5.6"

postcss-calc@^8.2.3:
  version "8.2.4"
  resolved "https://registry.npmmirror.com/postcss-calc/-/postcss-calc-8.2.4.tgz"
  integrity sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==
  dependencies:
    postcss-selector-parser "^6.0.9"
    postcss-value-parser "^4.2.0"

postcss-colormin@^5.3.1:
  version "5.3.1"
  resolved "https://registry.npmmirror.com/postcss-colormin/-/postcss-colormin-5.3.1.tgz"
  integrity sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    colord "^2.9.1"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^5.1.3:
  version "5.1.3"
  resolved "https://registry.npmmirror.com/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz"
  integrity sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-discard-comments@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz"
  integrity sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==

postcss-discard-duplicates@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz"
  integrity sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==

postcss-discard-empty@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz"
  integrity sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==

postcss-discard-overridden@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz"
  integrity sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==

postcss-loader@^6.1.1:
  version "6.2.1"
  resolved "https://registry.npmmirror.com/postcss-loader/-/postcss-loader-6.2.1.tgz"
  integrity sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q==
  dependencies:
    cosmiconfig "^7.0.0"
    klona "^2.0.5"
    semver "^7.3.5"

postcss-merge-longhand@^5.1.7:
  version "5.1.7"
  resolved "https://registry.npmmirror.com/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz"
  integrity sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^5.1.1"

postcss-merge-rules@^5.1.4:
  version "5.1.4"
  resolved "https://registry.npmmirror.com/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz"
  integrity sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"
    cssnano-utils "^3.1.0"
    postcss-selector-parser "^6.0.5"

postcss-minify-font-values@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz"
  integrity sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz"
  integrity sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==
  dependencies:
    colord "^2.9.1"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^5.1.4:
  version "5.1.4"
  resolved "https://registry.npmmirror.com/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz"
  integrity sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==
  dependencies:
    browserslist "^4.21.4"
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^5.2.1:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz"
  integrity sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-modules-extract-imports@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz"
  integrity sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==

postcss-modules-local-by-default@^4.0.5:
  version "4.0.5"
  resolved "https://registry.npmmirror.com/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.5.tgz"
  integrity sha512-6MieY7sIfTK0hYfafw1OMEG+2bg8Q1ocHCpoWLqOKj3JXlKu4G7btkmM/B7lFubYkYWmRSPLZi5chid63ZaZYw==
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/postcss-modules-scope/-/postcss-modules-scope-3.2.0.tgz"
  integrity sha512-oq+g1ssrsZOsx9M96c5w8laRmvEu9C3adDSjI8oTcbfkrTE8hx/zfyobUoWIxaKPO8bt6S62kxpw5GqypEw1QQ==
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz"
  integrity sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==
  dependencies:
    icss-utils "^5.0.0"

postcss-normalize-charset@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz"
  integrity sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==

postcss-normalize-display-values@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz"
  integrity sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz"
  integrity sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz"
  integrity sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz"
  integrity sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz"
  integrity sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz"
  integrity sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==
  dependencies:
    browserslist "^4.21.4"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz"
  integrity sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==
  dependencies:
    normalize-url "^6.0.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz"
  integrity sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-ordered-values@^5.1.3:
  version "5.1.3"
  resolved "https://registry.npmmirror.com/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz"
  integrity sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==
  dependencies:
    cssnano-utils "^3.1.0"
    postcss-value-parser "^4.2.0"

postcss-reduce-initial@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz"
  integrity sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg==
  dependencies:
    browserslist "^4.21.4"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz"
  integrity sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4, postcss-selector-parser@^6.0.5, postcss-selector-parser@^6.0.9:
  version "6.0.16"
  resolved "https://registry.npmmirror.com/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz"
  integrity sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^5.1.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/postcss-svgo/-/postcss-svgo-5.1.0.tgz"
  integrity sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^2.7.0"

postcss-unique-selectors@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz"
  integrity sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==
  dependencies:
    postcss-selector-parser "^6.0.5"

postcss-value-parser@^4.1.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

"postcss@^7.0.0 || ^8.0.1", postcss@^8.0.9, postcss@^8.1.0, postcss@^8.2.15, postcss@^8.2.2, postcss@^8.2.6, postcss@^8.3.5, postcss@^8.4.33, postcss@^8.4.38:
  version "8.4.38"
  resolved "https://registry.npmmirror.com/postcss/-/postcss-8.4.38.tgz"
  integrity sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.0"
    source-map-js "^1.2.0"

postcss@^7.0.36:
  version "7.0.39"
  resolved "https://registry.npmmirror.com/postcss/-/postcss-7.0.39.tgz"
  integrity sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==
  dependencies:
    picocolors "^0.2.1"
    source-map "^0.6.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

"prettier@^1.18.2 || ^2.0.0":
  version "2.8.8"
  resolved "https://registry.npmmirror.com/prettier/-/prettier-2.8.8.tgz"
  integrity sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/pretty-error/-/pretty-error-4.0.0.tgz"
  integrity sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

progress-webpack-plugin@^1.0.12:
  version "1.0.16"
  resolved "https://registry.npmmirror.com/progress-webpack-plugin/-/progress-webpack-plugin-1.0.16.tgz"
  integrity sha512-sdiHuuKOzELcBANHfrupYo+r99iPRyOnw15qX+rNlVUqXGfjXdH4IgxriKwG1kNJwVswKQHMdj1hYZMcb9jFaA==
  dependencies:
    chalk "^2.1.0"
    figures "^2.0.0"
    log-update "^2.3.0"

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.npmmirror.com/progress/-/progress-2.0.3.tgz"
  integrity sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==

proxy-addr@~2.0.7:
  version "2.0.7"
  resolved "https://registry.npmmirror.com/proxy-addr/-/proxy-addr-2.0.7.tgz"
  integrity sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/pseudomap/-/pseudomap-1.0.2.tgz"
  integrity sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/pump/-/pump-3.0.0.tgz"
  integrity sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://registry.npmmirror.com/punycode/-/punycode-2.3.1.tgz"
  integrity sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==

qs@6.11.0:
  version "6.11.0"
  resolved "https://registry.npmmirror.com/qs/-/qs-6.11.0.tgz"
  integrity sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==
  dependencies:
    side-channel "^1.0.4"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmmirror.com/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npmmirror.com/range-parser/-/range-parser-1.2.1.tgz"
  integrity sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==

raw-body@2.5.2:
  version "2.5.2"
  resolved "https://registry.npmmirror.com/raw-body/-/raw-body-2.5.2.tgz"
  integrity sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/read-pkg-up/-/read-pkg-up-7.0.1.tgz"
  integrity sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.1.1, read-pkg@^5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/read-pkg/-/read-pkg-5.2.0.tgz"
  integrity sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.1:
  version "2.3.8"
  resolved "https://registry.npmmirror.com/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.0.6, readable-stream@^3.4.0:
  version "3.6.2"
  resolved "https://registry.npmmirror.com/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmmirror.com/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

regenerate-unicode-properties@^10.1.0:
  version "10.1.1"
  resolved "https://registry.npmmirror.com/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.1.tgz"
  integrity sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "https://registry.npmmirror.com/regenerate/-/regenerate-1.4.2.tgz"
  integrity sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"
  integrity sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "https://registry.npmmirror.com/regenerator-transform/-/regenerator-transform-0.15.2.tgz"
  integrity sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==
  dependencies:
    "@babel/runtime" "^7.8.4"

regexpp@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/regexpp/-/regexpp-3.2.0.tgz"
  integrity sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==

regexpu-core@^5.3.1:
  version "5.3.2"
  resolved "https://registry.npmmirror.com/regexpu-core/-/regexpu-core-5.3.2.tgz"
  integrity sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.1.0"
    regjsparser "^0.9.1"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsparser@^0.9.1:
  version "0.9.1"
  resolved "https://registry.npmmirror.com/regjsparser/-/regjsparser-0.9.1.tgz"
  integrity sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==
  dependencies:
    jsesc "~0.5.0"

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://registry.npmmirror.com/relateurl/-/relateurl-0.2.7.tgz"
  integrity sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/renderkid/-/renderkid-3.0.0.tgz"
  integrity sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/require-directory/-/require-directory-2.1.1.tgz"
  integrity sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/require-from-string/-/require-from-string-2.0.2.tgz"
  integrity sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/requires-port/-/requires-port-1.0.0.tgz"
  integrity sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.10.0, resolve@^1.14.2:
  version "1.22.8"
  resolved "https://registry.npmmirror.com/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-2.0.0.tgz"
  integrity sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/restore-cursor/-/restore-cursor-3.1.0.tgz"
  integrity sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.npmmirror.com/retry/-/retry-0.13.1.tgz"
  integrity sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmmirror.com/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.1.0, safe-buffer@>=5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"
  resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-buffer@5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npmmirror.com/safer-buffer/-/safer-buffer-2.1.2.tgz"
  integrity sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==

schema-utils@^2.6.5:
  version "2.7.1"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-2.7.1.tgz"
  integrity sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==
  dependencies:
    "@types/json-schema" "^7.0.5"
    ajv "^6.12.4"
    ajv-keywords "^3.5.2"

schema-utils@^3.0.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^3.1.1:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/schema-utils/-/schema-utils-4.2.0.tgz"
  integrity sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.9.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.1.0"

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/select-hose/-/select-hose-2.0.0.tgz"
  integrity sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==

selfsigned@^2.1.1:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/selfsigned/-/selfsigned-2.4.1.tgz"
  integrity sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==
  dependencies:
    "@types/node-forge" "^1.3.0"
    node-forge "^1"

semver@^5.5.0:
  version "5.7.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

semver@^6.0.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmmirror.com/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.2.1:
  version "7.6.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.6.2.tgz"
  integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==

semver@^7.3.4:
  version "7.6.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.6.2.tgz"
  integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==

semver@^7.3.5:
  version "7.6.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.6.2.tgz"
  integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==

semver@^7.5.4:
  version "7.6.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-7.6.2.tgz"
  integrity sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==

"semver@2 || 3 || 4 || 5":
  version "5.7.2"
  resolved "https://registry.npmmirror.com/semver/-/semver-5.7.2.tgz"
  integrity sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==

send@0.18.0:
  version "0.18.0"
  resolved "https://registry.npmmirror.com/send/-/send-0.18.0.tgz"
  integrity sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.0, serialize-javascript@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  integrity sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.npmmirror.com/serve-index/-/serve-index-1.9.1.tgz"
  integrity sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.15.0:
  version "1.15.0"
  resolved "https://registry.npmmirror.com/serve-static/-/serve-static-1.15.0.tgz"
  integrity sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

set-function-length@^1.2.1:
  version "1.2.2"
  resolved "https://registry.npmmirror.com/set-function-length/-/set-function-length-1.2.2.tgz"
  integrity sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.1.0.tgz"
  integrity sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==

setprototypeof@1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/setprototypeof/-/setprototypeof-1.2.0.tgz"
  integrity sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/shallow-clone/-/shallow-clone-3.0.1.tgz"
  integrity sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-1.2.0.tgz"
  integrity sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-1.0.0.tgz"
  integrity sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

shell-quote@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmmirror.com/shell-quote/-/shell-quote-1.8.1.tgz"
  integrity sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==

side-channel@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmmirror.com/side-channel/-/side-channel-1.0.6.tgz"
  integrity sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.7"
  resolved "https://registry.npmmirror.com/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

sirv@^2.0.3:
  version "2.0.4"
  resolved "https://registry.npmmirror.com/sirv/-/sirv-2.0.4.tgz"
  integrity sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/slice-ansi/-/slice-ansi-4.0.0.tgz"
  integrity sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

sockjs@^0.3.24:
  version "0.3.24"
  resolved "https://registry.npmmirror.com/sockjs/-/sockjs-0.3.24.tgz"
  integrity sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^8.3.2"
    websocket-driver "^0.7.4"

source-map-js@^1.2.0:
  version "1.2.0"
  resolved "https://registry.npmmirror.com/source-map-js/-/source-map-js-1.2.0.tgz"
  integrity sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmmirror.com/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://registry.npmmirror.com/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/spdx-correct/-/spdx-correct-3.2.0.tgz"
  integrity sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "https://registry.npmmirror.com/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz"
  integrity sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz"
  integrity sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.17"
  resolved "https://registry.npmmirror.com/spdx-license-ids/-/spdx-license-ids-3.0.17.tgz"
  integrity sha512-sh8PWc/ftMqAAdFiBu6Fy6JUOYjqDJBJvIhpfDMyHrr0Rbp5liZqd4TjtQ/RgfLjKFZb+LMx5hpml5qOWy0qvg==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/spdy-transport/-/spdy-transport-3.0.0.tgz"
  integrity sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmmirror.com/spdy/-/spdy-4.0.2.tgz"
  integrity sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.npmmirror.com/sprintf-js/-/sprintf-js-1.0.3.tgz"
  integrity sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==

ssri@^8.0.1:
  version "8.0.1"
  resolved "https://registry.npmmirror.com/ssri/-/ssri-8.0.1.tgz"
  integrity sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==
  dependencies:
    minipass "^3.1.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "https://registry.npmmirror.com/stable/-/stable-0.1.8.tgz"
  integrity sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==

stackframe@^1.3.4:
  version "1.3.4"
  resolved "https://registry.npmmirror.com/stackframe/-/stackframe-1.3.4.tgz"
  integrity sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==

"statuses@>= 1.4.0 < 2":
  version "1.5.0"
  resolved "https://registry.npmmirror.com/statuses/-/statuses-1.5.0.tgz"
  integrity sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==

statuses@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/statuses/-/statuses-2.0.1.tgz"
  integrity sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmmirror.com/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/string-width/-/string-width-2.1.1.tgz"
  integrity sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-4.0.0.tgz"
  integrity sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/strip-eof/-/strip-eof-1.0.0.tgz"
  integrity sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-final-newline/-/strip-final-newline-2.0.0.tgz"
  integrity sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/strip-indent/-/strip-indent-2.0.0.tgz"
  integrity sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA==

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

stylehacks@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/stylehacks/-/stylehacks-5.1.1.tgz"
  integrity sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==
  dependencies:
    browserslist "^4.21.4"
    postcss-selector-parser "^6.0.4"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmmirror.com/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/svg-tags/-/svg-tags-1.0.0.tgz"
  integrity sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==

svgo@^2.7.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/svgo/-/svgo-2.8.0.tgz"
  integrity sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^4.1.3"
    css-tree "^1.1.3"
    csso "^4.2.0"
    picocolors "^1.0.0"
    stable "^0.1.8"

table@^6.0.9:
  version "6.8.2"
  resolved "https://registry.npmmirror.com/table/-/table-6.8.2.tgz"
  integrity sha512-w2sfv80nrAh2VCbqR5AK27wswXhqcck2AhfnNW76beQXskGZ1V12GwS//yYVa3d3fcvAip2OUnbDAjW2k3v9fA==
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0, tapable@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmmirror.com/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

terser-webpack-plugin@^5.1.1, terser-webpack-plugin@^5.3.10:
  version "5.3.10"
  resolved "https://registry.npmmirror.com/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz"
  integrity sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.20"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.26.0"

terser@^5.10.0, terser@^5.26.0:
  version "5.31.0"
  resolved "https://registry.npmmirror.com/terser/-/terser-5.31.0.tgz"
  integrity sha512-Q1JFAoUKE5IMfI4Z/lkE/E6+SwgzO+x4tq4v1AyBLRj8VSYvRO6A/rQrPg1yud4g0En9EKI1TvFRF2tQFcoUkg==
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmmirror.com/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

thread-loader@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/thread-loader/-/thread-loader-3.0.4.tgz"
  integrity sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==
  dependencies:
    json-parse-better-errors "^1.0.2"
    loader-runner "^4.1.0"
    loader-utils "^2.0.0"
    neo-async "^2.6.2"
    schema-utils "^3.0.0"

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.npmmirror.com/thunky/-/thunky-1.1.0.tgz"
  integrity sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmmirror.com/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/toidentifier/-/toidentifier-1.0.1.tgz"
  integrity sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==

totalist@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/totalist/-/totalist-3.0.1.tgz"
  integrity sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmmirror.com/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

tslib@^2.0.3:
  version "2.6.2"
  resolved "https://registry.npmmirror.com/tslib/-/tslib-2.6.2.tgz"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.6.0.tgz"
  integrity sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://registry.npmmirror.com/type-fest/-/type-fest-0.8.1.tgz"
  integrity sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==

type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.npmmirror.com/type-is/-/type-is-1.6.18.tgz"
  integrity sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

undici-types@~5.26.4:
  version "5.26.5"
  resolved "https://registry.npmmirror.com/undici-types/-/undici-types-5.26.5.tgz"
  integrity sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  integrity sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz"
  integrity sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz"
  integrity sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz"
  integrity sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/universalify/-/universalify-2.0.1.tgz"
  integrity sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/unpipe/-/unpipe-1.0.0.tgz"
  integrity sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==

update-browserslist-db@^1.0.13:
  version "1.0.15"
  resolved "https://registry.npmmirror.com/update-browserslist-db/-/update-browserslist-db-1.0.15.tgz"
  integrity sha512-K9HWH62x3/EalU1U6sjSZiylm9C8tgq2mSvshZpqc7QE69RaA2qjhkW2HlNA0tFpEbtyFz7HTqbSdN4MSwUodA==
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.0"

uri-js@^4.2.2, uri-js@^4.4.1:
  version "4.4.1"
  resolved "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.npmmirror.com/utila/-/utila-0.4.0.tgz"
  integrity sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/utils-merge/-/utils-merge-1.0.1.tgz"
  integrity sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==

uuid@^8.3.2:
  version "8.3.2"
  resolved "https://registry.npmmirror.com/uuid/-/uuid-8.3.2.tgz"
  integrity sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==

v8-compile-cache@^2.0.3:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz"
  integrity sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.npmmirror.com/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz"
  integrity sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/vary/-/vary-1.1.2.tgz"
  integrity sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==

vue-eslint-parser@^8.0.1:
  version "8.3.0"
  resolved "https://registry.npmmirror.com/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz"
  integrity sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==
  dependencies:
    debug "^4.3.2"
    eslint-scope "^7.0.0"
    eslint-visitor-keys "^3.1.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.5"

vue-hot-reload-api@^2.3.0:
  version "2.3.4"
  resolved "https://registry.npmmirror.com/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  integrity sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==

vue-loader@^17.0.0:
  version "17.4.2"
  resolved "https://registry.npmmirror.com/vue-loader/-/vue-loader-17.4.2.tgz"
  integrity sha512-yTKOA4R/VN4jqjw4y5HrynFL8AK0Z3/Jt7eOJXEitsm0GMRHDBjCfCiuTiLP7OESvsZYo2pATCWhDqxC5ZrM6w==
  dependencies:
    chalk "^4.1.0"
    hash-sum "^2.0.0"
    watchpack "^2.4.0"

vue-style-loader@^4.1.0, vue-style-loader@^4.1.3:
  version "4.1.3"
  resolved "https://registry.npmmirror.com/vue-style-loader/-/vue-style-loader-4.1.3.tgz"
  integrity sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==
  dependencies:
    hash-sum "^1.0.2"
    loader-utils "^1.0.2"

vue-template-es2015-compiler@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmmirror.com/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz"
  integrity sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==

vue@*, "vue@^2 || ^3.2.13", vue@^3.2.13, vue@3.4.27:
  version "3.4.27"
  resolved "https://registry.npmmirror.com/vue/-/vue-3.4.27.tgz"
  integrity sha512-8s/56uK6r01r1icG/aEOHqyMVxd1bkYcSe9j8HcKtr/xTOFWvnzIVTehNW+5Yt89f+DLBe4A569pnZLS5HzAMA==
  dependencies:
    "@vue/compiler-dom" "3.4.27"
    "@vue/compiler-sfc" "3.4.27"
    "@vue/runtime-dom" "3.4.27"
    "@vue/server-renderer" "3.4.27"
    "@vue/shared" "3.4.27"

watchpack@^2.4.0, watchpack@^2.4.1:
  version "2.4.1"
  resolved "https://registry.npmmirror.com/watchpack/-/watchpack-2.4.1.tgz"
  integrity sha512-8wrBCMtVhqcXP2Sup1ctSkga6uc2Bx0IIvKyT7yTFier5AXHooSI+QyQQAtTb7+E0IUCCKyTFmXqdqgum2XWGg==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npmmirror.com/wbuf/-/wbuf-1.7.3.tgz"
  integrity sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==
  dependencies:
    minimalistic-assert "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/wcwidth/-/wcwidth-1.0.1.tgz"
  integrity sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webpack-bundle-analyzer@^4.4.0:
  version "4.10.2"
  resolved "https://registry.npmmirror.com/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.10.2.tgz"
  integrity sha512-vJptkMm9pk5si4Bv922ZbKLV8UTT4zib4FPgXMhgzUny0bfDDkLXAVQs3ly3fS4/TN9ROFtb0NFrm04UXFE/Vw==
  dependencies:
    "@discoveryjs/json-ext" "0.5.7"
    acorn "^8.0.4"
    acorn-walk "^8.0.0"
    commander "^7.2.0"
    debounce "^1.2.1"
    escape-string-regexp "^4.0.0"
    gzip-size "^6.0.0"
    html-escaper "^2.0.2"
    opener "^1.5.2"
    picocolors "^1.0.0"
    sirv "^2.0.3"
    ws "^7.3.1"

webpack-chain@^6.5.1:
  version "6.5.1"
  resolved "https://registry.npmmirror.com/webpack-chain/-/webpack-chain-6.5.1.tgz"
  integrity sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA==
  dependencies:
    deepmerge "^1.5.2"
    javascript-stringify "^2.0.1"

webpack-dev-middleware@^5.3.4:
  version "5.3.4"
  resolved "https://registry.npmmirror.com/webpack-dev-middleware/-/webpack-dev-middleware-5.3.4.tgz"
  integrity sha512-BVdTqhhs+0IfoeAf7EoH5WE+exCmqGerHfDM0IL096Px60Tq2Mn9MAbnaGUe6HiMa41KMCYF19gyzZmBcq/o4Q==
  dependencies:
    colorette "^2.0.10"
    memfs "^3.4.3"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.7.3:
  version "4.15.2"
  resolved "https://registry.npmmirror.com/webpack-dev-server/-/webpack-dev-server-4.15.2.tgz"
  integrity sha512-0XavAZbNJ5sDrCbkpWL8mia0o5WPOd2YGtxrEiZkBK9FjLppIUK2TgxK6qGD2P3hUXTJNNPVibrerKcx5WkR1g==
  dependencies:
    "@types/bonjour" "^3.5.9"
    "@types/connect-history-api-fallback" "^1.3.5"
    "@types/express" "^4.17.13"
    "@types/serve-index" "^1.9.1"
    "@types/serve-static" "^1.13.10"
    "@types/sockjs" "^0.3.33"
    "@types/ws" "^8.5.5"
    ansi-html-community "^0.0.8"
    bonjour-service "^1.0.11"
    chokidar "^3.5.3"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^2.0.0"
    default-gateway "^6.0.3"
    express "^4.17.3"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.3"
    ipaddr.js "^2.0.1"
    launch-editor "^2.6.0"
    open "^8.0.9"
    p-retry "^4.5.0"
    rimraf "^3.0.2"
    schema-utils "^4.0.0"
    selfsigned "^2.1.1"
    serve-index "^1.9.1"
    sockjs "^0.3.24"
    spdy "^4.0.2"
    webpack-dev-middleware "^5.3.4"
    ws "^8.13.0"

webpack-merge@^5.7.3:
  version "5.10.0"
  resolved "https://registry.npmmirror.com/webpack-merge/-/webpack-merge-5.10.0.tgz"
  integrity sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==
  dependencies:
    clone-deep "^4.0.1"
    flat "^5.0.2"
    wildcard "^2.0.0"

webpack-sources@*, webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmmirror.com/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

webpack-virtual-modules@^0.4.2:
  version "0.4.6"
  resolved "https://registry.npmmirror.com/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz"
  integrity sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==

"webpack@^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "webpack@^3.0.0 || ^4.1.0 || ^5.0.0-0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.1.0 || ^5.0.0-0", "webpack@^4.27.0 || ^5.0.0", "webpack@^4.37.0 || ^5.0.0", webpack@^5.0.0, webpack@^5.1.0, webpack@^5.20.0, webpack@^5.54.0, webpack@>=2:
  version "5.91.0"
  resolved "https://registry.npmmirror.com/webpack/-/webpack-5.91.0.tgz"
  integrity sha512-rzVwlLeBWHJbmgTC/8TvAcu5vpJNII+MelQpylD4jNERPwpBJOE2lEcko1zJX3QJeLjTTAnQxn/OJ8bjDzVQaw==
  dependencies:
    "@types/eslint-scope" "^3.7.3"
    "@types/estree" "^1.0.5"
    "@webassemblyjs/ast" "^1.12.1"
    "@webassemblyjs/wasm-edit" "^1.12.1"
    "@webassemblyjs/wasm-parser" "^1.12.1"
    acorn "^8.7.1"
    acorn-import-assertions "^1.9.0"
    browserslist "^4.21.10"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.16.0"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

websocket-driver@^0.7.4, websocket-driver@>=0.5.1:
  version "0.7.4"
  resolved "https://registry.npmmirror.com/websocket-driver/-/websocket-driver-0.7.4.tgz"
  integrity sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/websocket-extensions/-/websocket-extensions-0.1.4.tgz"
  integrity sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==

whatwg-fetch@^3.6.2:
  version "3.6.20"
  resolved "https://registry.npmmirror.com/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz"
  integrity sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.npmmirror.com/which/-/which-1.3.1.tgz"
  integrity sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmmirror.com/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/wildcard/-/wildcard-2.0.1.tgz"
  integrity sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://registry.npmmirror.com/word-wrap/-/word-wrap-1.2.5.tgz"
  integrity sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-3.0.1.tgz"
  integrity sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmmirror.com/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmmirror.com/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

ws@^7.3.1:
  version "7.5.9"
  resolved "https://registry.npmmirror.com/ws/-/ws-7.5.9.tgz"
  integrity sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==

ws@^8.13.0:
  version "8.17.0"
  resolved "https://registry.npmmirror.com/ws/-/ws-8.17.0.tgz"
  integrity sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://registry.npmmirror.com/y18n/-/y18n-5.0.8.tgz"
  integrity sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/yallist/-/yallist-2.1.2.tgz"
  integrity sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/yallist/-/yallist-3.1.1.tgz"
  integrity sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0, yaml@^1.10.2:
  version "1.10.2"
  resolved "https://registry.npmmirror.com/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://registry.npmmirror.com/yargs-parser/-/yargs-parser-20.2.9.tgz"
  integrity sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==

yargs@^16.0.0:
  version "16.2.0"
  resolved "https://registry.npmmirror.com/yargs/-/yargs-16.2.0.tgz"
  integrity sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yorkie@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/yorkie/-/yorkie-2.0.0.tgz"
  integrity sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw==
  dependencies:
    execa "^0.8.0"
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/**\n * Converts `iterator` to an array.\n *\n * @private\n * @param {Object} iterator The iterator to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction iteratorToArray(iterator) {\n  var data,\n    result = [];\n  while (!(data = iterator.next()).done) {\n    result.push(data.value);\n  }\n  return result;\n}\nexport default iteratorToArray;", "map": {"version": 3, "names": ["iteratorToArray", "iterator", "data", "result", "next", "done", "push", "value"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_iteratorToArray.js"], "sourcesContent": ["/**\n * Converts `iterator` to an array.\n *\n * @private\n * @param {Object} iterator The iterator to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction iteratorToArray(iterator) {\n  var data,\n      result = [];\n\n  while (!(data = iterator.next()).done) {\n    result.push(data.value);\n  }\n  return result;\n}\n\nexport default iteratorToArray;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,QAAQ,EAAE;EACjC,IAAIC,IAAI;IACJC,MAAM,GAAG,EAAE;EAEf,OAAO,CAAC,CAACD,IAAI,GAAGD,QAAQ,CAACG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;IACrCF,MAAM,CAACG,IAAI,CAACJ,IAAI,CAACK,KAAK,CAAC;EACzB;EACA,OAAOJ,MAAM;AACf;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
server:
  port: 8080
  servlet:
    context-path: /
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB

spring:
  application:
    name: scanonweb-server
  
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 100MB
      file-size-threshold: 2KB
      location: ${java.io.tmpdir}
  
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
      allow-credentials: false

# 应用配置
app:
  upload:
    # 上传文件存储路径
    path: ./uploads
    # 允许的文件类型
    allowed-types:
      - pdf
      - tiff
      - tif
      - jpg
      - jpeg
      - png
    # 最大文件大小 (50MB)
    max-size: 52428800

# 日志配置
logging:
  level:
    com.scanonweb: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/scanonweb-server.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

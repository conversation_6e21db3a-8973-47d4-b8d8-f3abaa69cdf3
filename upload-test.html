<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫描控件上传测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409eff;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #303133;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status.success {
            background-color: #f0f9ff;
            border-color: #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border-color: #f56c6c;
            color: #f56c6c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 扫描控件上传功能测试</h1>
        
        <div class="test-section">
            <h3>📡 控件连接状态</h3>
            <div id="connection-status" class="status error">
                <strong>❌ 扫描控件未连接</strong>
            </div>
            <button onclick="initScanService()">初始化扫描服务</button>
        </div>

        <div class="test-section">
            <h3>🖥️ 设备管理</h3>
            <button onclick="loadDevices()">获取设备列表</button>
            <button onclick="startScan()">开始扫描</button>
            <div id="device-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📤 上传测试</h3>
            <p>测试控件内置的上传功能</p>
            <button onclick="testPdfUpload()">上传为PDF</button>
            <button onclick="testTiffUpload()">上传为TIFF</button>
            <button onclick="testJpgUpload()">上传为JPG</button>
            <div id="upload-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 上传记录</h3>
            <button onclick="getUploadedFiles()">查看上传文件</button>
            <div id="files-result" class="result"></div>
        </div>
    </div>

    <script src="scanonweb.js"></script>
    <script>
        let scanonweb = null;
        let isConnected = false;

        // 初始化扫描服务
        function initScanService() {
            const statusDiv = document.getElementById('connection-status');
            const resultDiv = document.getElementById('device-result');
            
            try {
                scanonweb = new ScanOnWeb();
                
                // 设置事件回调
                scanonweb.onGetDevicesListEvent = function(msg) {
                    isConnected = true;
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = '<strong>✅ 扫描控件已连接</strong>';
                    
                    resultDiv.textContent = `设备列表获取成功:\n设备数量: ${msg.devices.length}\n当前设备: ${msg.currentIndex}\n设备列表: ${msg.devices.join(', ')}`;
                };

                scanonweb.onScanFinishedEvent = function(msg) {
                    resultDiv.textContent += `\n扫描完成: 共 ${msg.imageAfterCount} 张图像`;
                };

                scanonweb.onUploadAllImageAsPdfToUrlEvent = function(msg) {
                    const uploadResult = document.getElementById('upload-result');
                    if (msg.success) {
                        uploadResult.textContent = `✅ PDF上传成功!\n响应: ${JSON.stringify(msg, null, 2)}`;
                    } else {
                        uploadResult.textContent = `❌ PDF上传失败!\n错误: ${msg.message || '未知错误'}`;
                    }
                };

                scanonweb.onUploadAllImageAsTiffToUrlEvent = function(msg) {
                    const uploadResult = document.getElementById('upload-result');
                    if (msg.success) {
                        uploadResult.textContent = `✅ TIFF上传成功!\n响应: ${JSON.stringify(msg, null, 2)}`;
                    } else {
                        uploadResult.textContent = `❌ TIFF上传失败!\n错误: ${msg.message || '未知错误'}`;
                    }
                };

                scanonweb.uploadJpgImageByIndexEvent = function(msg) {
                    const uploadResult = document.getElementById('upload-result');
                    if (msg.success) {
                        uploadResult.textContent = `✅ JPG上传成功!\n响应: ${JSON.stringify(msg, null, 2)}`;
                    } else {
                        uploadResult.textContent = `❌ JPG上传失败!\n错误: ${msg.message || '未知错误'}`;
                    }
                };

                // 自动加载设备列表
                setTimeout(() => {
                    loadDevices();
                }, 1000);

            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<strong>❌ 扫描控件初始化失败</strong>';
                resultDiv.textContent = `初始化失败: ${error.message}`;
            }
        }

        // 加载设备列表
        function loadDevices() {
            if (!scanonweb) {
                alert('请先初始化扫描服务');
                return;
            }
            
            const resultDiv = document.getElementById('device-result');
            resultDiv.textContent = '正在获取设备列表...';
            
            try {
                scanonweb.loadDevices();
            } catch (error) {
                resultDiv.textContent = `获取设备列表失败: ${error.message}`;
            }
        }

        // 开始扫描
        function startScan() {
            if (!scanonweb || !isConnected) {
                alert('请先初始化扫描服务并连接设备');
                return;
            }
            
            const resultDiv = document.getElementById('device-result');
            resultDiv.textContent += '\n开始扫描...';
            
            try {
                scanonweb.startScan();
            } catch (error) {
                resultDiv.textContent += `\n扫描失败: ${error.message}`;
            }
        }

        // 测试PDF上传
        function testPdfUpload() {
            if (!scanonweb || !isConnected) {
                alert('请先扫描一些图像');
                return;
            }
            
            const uploadResult = document.getElementById('upload-result');
            uploadResult.textContent = '正在上传PDF文档...';
            
            const uploadUrl = 'http://localhost:8080/upload';
            const id = 'test_pdf_' + Date.now();
            const description = 'PDF上传测试';
            
            try {
                scanonweb.uploadAllImageAsPdfToUrl(uploadUrl, id, description);
            } catch (error) {
                uploadResult.textContent = `PDF上传失败: ${error.message}`;
            }
        }

        // 测试TIFF上传
        function testTiffUpload() {
            if (!scanonweb || !isConnected) {
                alert('请先扫描一些图像');
                return;
            }
            
            const uploadResult = document.getElementById('upload-result');
            uploadResult.textContent = '正在上传TIFF文档...';
            
            const uploadUrl = 'http://localhost:8080/upload-tiff';
            const id = 'test_tiff_' + Date.now();
            const description = 'TIFF上传测试';
            
            try {
                scanonweb.uploadAllImageAsTiffToUrl(uploadUrl, id, description);
            } catch (error) {
                uploadResult.textContent = `TIFF上传失败: ${error.message}`;
            }
        }

        // 测试JPG上传
        function testJpgUpload() {
            if (!scanonweb || !isConnected) {
                alert('请先扫描一些图像');
                return;
            }
            
            const uploadResult = document.getElementById('upload-result');
            uploadResult.textContent = '正在上传JPG图像...';
            
            const uploadUrl = 'http://localhost:8080/upload-jpg';
            const id = 'test_jpg_' + Date.now();
            const description = 'JPG上传测试';
            const index = 0; // 上传第一张图像
            
            try {
                scanonweb.uploadJpgImageByIndex(uploadUrl, id, description, index);
            } catch (error) {
                uploadResult.textContent = `JPG上传失败: ${error.message}`;
            }
        }

        // 获取已上传文件列表
        async function getUploadedFiles() {
            const filesResult = document.getElementById('files-result');
            filesResult.textContent = '正在获取文件列表...';
            
            try {
                const response = await fetch('http://localhost:8080/files');
                const data = await response.json();
                
                if (data.success) {
                    filesResult.textContent = `文件列表 (共 ${data.files.length} 个文件):\n\n`;
                    data.files.forEach((file, index) => {
                        filesResult.textContent += `${index + 1}. ${file.name}\n   大小: ${formatFileSize(file.size)}\n   修改时间: ${file.modTime}\n   URL: ${file.url}\n\n`;
                    });
                } else {
                    filesResult.textContent = `获取文件列表失败: ${data.message}`;
                }
            } catch (error) {
                filesResult.textContent = `获取文件列表失败: ${error.message}`;
            }
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 页面加载时自动初始化
        window.onload = function() {
            setTimeout(() => {
                initScanService();
            }, 500);
        };
    </script>
</body>
</html>

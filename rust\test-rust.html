<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rust后台测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
        }
        .section h3 {
            color: #303133;
            margin-top: 0;
        }
        button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #c0392b;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #e74c3c;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left-color: #27ae60;
            background-color: #f0f9ff;
        }
        .error {
            border-left-color: #e74c3c;
            background-color: #fef0f0;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .file-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 10px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #ebeef5;
        }
        .file-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦀 Rust后台服务测试</h1>
        <p style="text-align: center; color: #666;">高性能Rust后台服务，完全兼容Go、SpringBoot、Servlet版本API</p>
        
        <!-- 健康检查 -->
        <div class="section">
            <h3>📊 健康检查</h3>
            <button onclick="testHealth()">检查服务状态</button>
            <div id="health-result" class="result">点击按钮检查Rust服务状态...</div>
        </div>

        <!-- 文件上传测试 -->
        <div class="section">
            <h3>📤 文件上传测试</h3>
            <div>
                <label>选择文件: </label>
                <input type="file" id="fileInput" accept=".pdf,.tiff,.tif,.jpg,.jpeg,.png">
            </div>
            <div>
                <button onclick="uploadFile('pdf')">上传为PDF</button>
                <button onclick="uploadFile('tiff')">上传为TIFF</button>
                <button onclick="uploadFile('jpg')">上传为JPG</button>
            </div>
            <div id="upload-result" class="result">选择文件后点击上传按钮...</div>
        </div>

        <!-- JSON上传测试 -->
        <div class="section">
            <h3>📝 JSON上传测试</h3>
            <button onclick="testJsonUpload('pdf')">测试PDF JSON上传</button>
            <button onclick="testJsonUpload('tiff')">测试TIFF JSON上传</button>
            <button onclick="testJsonUpload('jpg')">测试JPG JSON上传</button>
            <div id="json-result" class="result">点击按钮测试JSON格式上传...</div>
        </div>

        <!-- 文件管理 -->
        <div class="section">
            <h3>📁 文件管理</h3>
            <button onclick="getFileList()">获取文件列表</button>
            <button onclick="clearFileList()">清空显示</button>
            <div id="file-list" class="file-list">点击"获取文件列表"查看已上传的文件...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        // 健康检查
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.textContent = '正在检查服务状态...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Rust服务运行正常\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 服务异常\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 连接失败\n错误: ${error.message}\n\n请确保Rust服务已启动在端口8080`;
            }
        }

        // 文件上传
        async function uploadFile(format) {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('upload-result');
            
            if (!fileInput.files[0]) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先选择文件';
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('image', file);
            formData.append('id', 'test_' + format + '_' + Date.now());
            formData.append('desc', `Rust ${format.toUpperCase()} 上传测试`);
            
            resultDiv.textContent = `正在上传 ${format.toUpperCase()} 文件...`;
            resultDiv.className = 'result';
            
            try {
                let url = `${API_BASE}/upload`;
                if (format === 'tiff') url = `${API_BASE}/upload-tiff`;
                if (format === 'jpg') url = `${API_BASE}/upload-jpg`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${format.toUpperCase()} 文件上传成功!\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${format.toUpperCase()} 文件上传失败!\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${format.toUpperCase()} 文件上传失败!\n错误: ${error.message}`;
            }
        }

        // JSON上传测试
        async function testJsonUpload(format) {
            const resultDiv = document.getElementById('json-result');
            resultDiv.textContent = `正在测试 ${format.toUpperCase()} JSON上传...`;
            resultDiv.className = 'result';
            
            // 创建一个简单的测试图像数据（1x1像素的PNG）
            const testImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            
            const requestData = {
                id: `test_json_${format}_${Date.now()}`,
                desc: `Rust ${format.toUpperCase()} JSON上传测试`,
                imageData: testImageData
            };
            
            if (format === 'jpg') {
                requestData.index = 0;
            }
            
            try {
                let url = `${API_BASE}/upload`;
                if (format === 'tiff') url = `${API_BASE}/upload-tiff`;
                if (format === 'jpg') url = `${API_BASE}/upload-jpg`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${format.toUpperCase()} JSON上传成功!\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${format.toUpperCase()} JSON上传失败!\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${format.toUpperCase()} JSON上传失败!\n错误: ${error.message}`;
            }
        }

        // 获取文件列表
        async function getFileList() {
            const listDiv = document.getElementById('file-list');
            listDiv.innerHTML = '正在获取文件列表...';
            
            try {
                const response = await fetch(`${API_BASE}/files`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    if (data.files.length === 0) {
                        listDiv.innerHTML = '📂 暂无上传文件';
                    } else {
                        let html = `<div style="margin-bottom: 10px;"><strong>📂 文件列表 (共 ${data.count} 个文件)</strong></div>`;
                        data.files.forEach(file => {
                            html += `
                                <div class="file-item">
                                    <div>
                                        <strong>${file.name}</strong><br>
                                        <small>大小: ${file.formattedSize} | 类型: ${file.type} | 修改时间: ${file.modTime}</small>
                                    </div>
                                    <div>
                                        <button onclick="downloadFile('${file.name}')" style="margin: 2px; padding: 5px 10px; font-size: 12px;">下载</button>
                                        <button onclick="deleteFile('${file.name}')" style="margin: 2px; padding: 5px 10px; font-size: 12px; background-color: #e74c3c;">删除</button>
                                    </div>
                                </div>
                            `;
                        });
                        listDiv.innerHTML = html;
                    }
                } else {
                    listDiv.innerHTML = `❌ 获取文件列表失败: ${data.message || '未知错误'}`;
                }
            } catch (error) {
                listDiv.innerHTML = `❌ 获取文件列表失败: ${error.message}`;
            }
        }

        // 下载文件
        function downloadFile(filename) {
            window.open(`${API_BASE}/uploads/${filename}`, '_blank');
        }

        // 删除文件
        async function deleteFile(filename) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/files/${filename}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    alert('✅ 文件删除成功');
                    getFileList(); // 刷新文件列表
                } else {
                    alert(`❌ 文件删除失败: ${data.message}`);
                }
            } catch (error) {
                alert(`❌ 文件删除失败: ${error.message}`);
            }
        }

        // 清空文件列表显示
        function clearFileList() {
            document.getElementById('file-list').innerHTML = '点击"获取文件列表"查看已上传的文件...';
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            setTimeout(testHealth, 500);
        };
    </script>
</body>
</html>

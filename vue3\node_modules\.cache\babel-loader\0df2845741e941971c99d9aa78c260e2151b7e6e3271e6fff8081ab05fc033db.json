{"ast": null, "code": "import baseWrapperValue from './_baseWrapperValue.js';\n\n/**\n * Executes the chain sequence to resolve the unwrapped value.\n *\n * @name value\n * @memberOf _\n * @since 0.1.0\n * @alias toJSON, valueOf\n * @category Seq\n * @returns {*} Returns the resolved unwrapped value.\n * @example\n *\n * _([1, 2, 3]).value();\n * // => [1, 2, 3]\n */\nfunction wrapperValue() {\n  return baseWrapperValue(this.__wrapped__, this.__actions__);\n}\nexport default wrapperValue;", "map": {"version": 3, "names": ["baseWrapperValue", "wrapperValue", "__wrapped__", "__actions__"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/wrapperValue.js"], "sourcesContent": ["import baseWrapperValue from './_baseWrapperValue.js';\n\n/**\n * Executes the chain sequence to resolve the unwrapped value.\n *\n * @name value\n * @memberOf _\n * @since 0.1.0\n * @alias toJSON, valueOf\n * @category Seq\n * @returns {*} Returns the resolved unwrapped value.\n * @example\n *\n * _([1, 2, 3]).value();\n * // => [1, 2, 3]\n */\nfunction wrapperValue() {\n  return baseWrapperValue(this.__wrapped__, this.__actions__);\n}\n\nexport default wrapperValue;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,OAAOD,gBAAgB,CAAC,IAAI,CAACE,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;AAC7D;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
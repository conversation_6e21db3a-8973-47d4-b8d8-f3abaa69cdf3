{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted, onUnmounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Camera, Setting, Picture, Refresh } from '@element-plus/icons-vue';\nimport ScanOnWeb from '@/scanonweb.js';\nimport { utils } from '@/services/api.js';\nexport default {\n  name: 'ScanOnWeb',\n  components: {\n    Camera,\n    Setting,\n    Picture,\n    Refresh\n  },\n  setup() {\n    // 响应式数据\n    const scanonweb = ref(null);\n    const isConnected = ref(false);\n    const devices = ref([]);\n    const selectedDevice = ref(-1);\n    const images = ref([]);\n    const viewMode = ref('grid');\n\n    // 扫描配置\n    const config = reactive({\n      dpi_x: 300,\n      dpi_y: 300,\n      colorMode: 'RGB',\n      showDialog: false,\n      autoFeedEnable: true,\n      autoFeed: false,\n      dupxMode: false,\n      autoDeskew: false,\n      autoBorderDetection: false\n    });\n\n    // 加载状态\n    const loading = reactive({\n      devices: false,\n      scan: false,\n      images: false,\n      upload: false\n    });\n\n    // 上传对话框\n    const uploadDialog = reactive({\n      visible: false,\n      form: {\n        id: '',\n        description: '',\n        format: 'pdf'\n      }\n    });\n\n    // 预览对话框\n    const previewDialog = reactive({\n      visible: false,\n      currentIndex: 0,\n      currentImage: null\n    });\n\n    // 初始化扫描服务\n    const initScanService = () => {\n      try {\n        scanonweb.value = new ScanOnWeb();\n\n        // 设置事件回调\n        scanonweb.value.onGetDevicesListEvent = msg => {\n          devices.value = msg.devices || [];\n          // 自动选择第一个设备，如果没有当前设备则选择第一个\n          if (devices.value.length > 0) {\n            selectedDevice.value = msg.currentIndex >= 0 ? msg.currentIndex : 0;\n            // 如果自动选择了设备，调用设备选择事件\n            if (selectedDevice.value >= 0) {\n              setTimeout(() => {\n                onDeviceChange(selectedDevice.value);\n              }, 100);\n            }\n          } else {\n            selectedDevice.value = -1;\n          }\n          loading.devices = false;\n          isConnected.value = true;\n          ElMessage.success(`发现 ${devices.value.length} 个扫描设备${selectedDevice.value >= 0 ? '，已自动选择第一个设备' : ''}`);\n        };\n        scanonweb.value.onScanFinishedEvent = msg => {\n          loading.scan = false;\n          ElMessage.success(`扫描完成！共扫描 ${msg.imageAfterCount} 张图像`);\n          getAllImage();\n        };\n        scanonweb.value.onGetAllImageEvent = msg => {\n          loading.images = false;\n          if (msg.images && msg.images.length > 0) {\n            images.value = msg.images.map((image, index) => ({\n              src: `data:image/jpg;base64,${image}`,\n              index: index,\n              base64: image\n            }));\n            ElMessage.success(`获取到 ${images.value.length} 张图像`);\n          } else {\n            ElMessage.info('暂无扫描图像');\n          }\n        };\n        scanonweb.value.onGetImageByIdEvent = msg => {\n          if (msg.imageBase64) {\n            addImage(msg.imageBase64);\n          }\n        };\n        scanonweb.value.onImageEditedEvent = msg => {\n          ElMessage.info(`图像 ${msg.imageIndex + 1} 已编辑`);\n          if (msg.imageBase64) {\n            editImage(msg.imageIndex, msg.imageBase64);\n          }\n        };\n        scanonweb.value.onUploadEvent = () => {\n          ElMessage.info('用户点击了上传按钮');\n          showUploadDialog();\n        };\n\n        // 检查连接状态\n        setTimeout(() => {\n          if (devices.value.length === 0) {\n            isConnected.value = false;\n            ElMessage.warning('扫描服务连接失败，请检查服务是否启动');\n          }\n        }, 3000);\n      } catch (error) {\n        console.error('初始化扫描服务失败:', error);\n        isConnected.value = false;\n        ElMessage.error('初始化扫描服务失败');\n      }\n    };\n\n    // 加载设备列表\n    const loadDevices = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化');\n        return;\n      }\n      loading.devices = true;\n      try {\n        scanonweb.value.loadDevices();\n      } catch (error) {\n        loading.devices = false;\n        ElMessage.error('获取设备列表失败');\n      }\n    };\n\n    // 设备选择变化\n    const onDeviceChange = deviceIndex => {\n      if (scanonweb.value) {\n        scanonweb.value.selectScanDevice(deviceIndex);\n        ElMessage.info(`已选择设备: ${devices.value[deviceIndex]}`);\n      }\n    };\n\n    // 开始扫描\n    const startScan = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化');\n        return;\n      }\n      if (selectedDevice.value === -1) {\n        ElMessage.warning('请先选择扫描设备');\n        return;\n      }\n      loading.scan = true;\n      try {\n        // 更新扫描配置\n        scanonweb.value.scaner_work_config = {\n          ...scanonweb.value.scaner_work_config,\n          ...config,\n          deviceIndex: selectedDevice.value\n        };\n        scanonweb.value.startScan();\n        ElMessage.info('开始扫描...');\n      } catch (error) {\n        loading.scan = false;\n        ElMessage.error('启动扫描失败');\n      }\n    };\n\n    // 获取所有图像\n    const getAllImage = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化');\n        return;\n      }\n      loading.images = true;\n      try {\n        scanonweb.value.getAllImage();\n      } catch (error) {\n        loading.images = false;\n        ElMessage.error('获取图像失败');\n      }\n    };\n\n    // 清空所有图像\n    const clearAll = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有扫描结果吗？', '确认操作', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        if (scanonweb.value) {\n          scanonweb.value.clearAll();\n          images.value = [];\n          ElMessage.success('已清空所有扫描结果');\n        }\n      } catch {\n        // 用户取消操作\n      }\n    };\n\n    // 添加图像\n    const addImage = imageBase64 => {\n      images.value.push({\n        src: `data:image/jpg;base64,${imageBase64}`,\n        index: images.value.length,\n        base64: imageBase64\n      });\n    };\n\n    // 编辑图像\n    const editImage = (index, imageBase64) => {\n      if (index >= 0 && index < images.value.length) {\n        images.value[index] = {\n          src: `data:image/jpg;base64,${imageBase64}`,\n          index: index,\n          base64: imageBase64\n        };\n      }\n    };\n\n    // 显示上传对话框\n    const showUploadDialog = () => {\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像');\n        return;\n      }\n      uploadDialog.form.id = utils.generateId();\n      uploadDialog.form.description = `扫描文档_${new Date().toLocaleString()}`;\n      uploadDialog.visible = true;\n    };\n\n    // 确认上传\n    const confirmUpload = async () => {\n      if (!uploadDialog.form.id.trim()) {\n        ElMessage.warning('请输入文档ID');\n        return;\n      }\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化');\n        return;\n      }\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像');\n        return;\n      }\n      loading.upload = true;\n      try {\n        const {\n          id,\n          description,\n          format\n        } = uploadDialog.form;\n        const uploadUrl = 'http://localhost:8080/upload';\n\n        // 设置上传完成回调\n        scanonweb.value.onUploadAllImageAsPdfToUrlEvent = msg => {\n          console.log('PDF上传回调:', msg);\n          loading.upload = false;\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string' ? JSON.parse(msg.uploadResult) : msg.uploadResult;\n            }\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`);\n              uploadDialog.visible = false;\n              console.log('上传详情:', uploadResult);\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误';\n              ElMessage.error(`PDF上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg);\n            ElMessage.error(`PDF上传失败: 响应解析错误`);\n          }\n        };\n        scanonweb.value.onUploadAllImageAsTiffToUrlEvent = msg => {\n          console.log('TIFF上传回调:', msg);\n          loading.upload = false;\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string' ? JSON.parse(msg.uploadResult) : msg.uploadResult;\n            }\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`);\n              uploadDialog.visible = false;\n              console.log('上传详情:', uploadResult);\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误';\n              ElMessage.error(`TIFF上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg);\n            ElMessage.error(`TIFF上传失败: 响应解析错误`);\n          }\n        };\n        scanonweb.value.uploadJpgImageByIndexEvent = msg => {\n          console.log('JPG上传回调:', msg);\n          loading.upload = false;\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string' ? JSON.parse(msg.uploadResult) : msg.uploadResult;\n            }\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`JPG图像上传成功！`);\n              uploadDialog.visible = false;\n              console.log('上传详情:', uploadResult);\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误';\n              ElMessage.error(`JPG上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg);\n            ElMessage.error(`JPG上传失败: 响应解析错误`);\n          }\n        };\n\n        // 调用控件内置的上传方法\n        if (format === 'pdf') {\n          ElMessage.info('开始上传PDF文档...');\n          scanonweb.value.uploadAllImageAsPdfToUrl(uploadUrl, id, description);\n        } else if (format === 'tiff') {\n          ElMessage.info('开始上传TIFF文档...');\n          const tiffUploadUrl = 'http://localhost:8080/upload-tiff';\n          scanonweb.value.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description);\n        } else if (format === 'jpg') {\n          ElMessage.info('开始上传JPG图像...');\n          const jpgUploadUrl = 'http://localhost:8080/upload-jpg';\n          // 上传第一张图像作为示例，实际可以循环上传所有图像\n          scanonweb.value.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0);\n        }\n      } catch (error) {\n        loading.upload = false;\n        console.error('上传失败:', error);\n        ElMessage.error(`上传失败: ${error.message || '未知错误'}`);\n      }\n    };\n\n    // 预览图像\n    const previewImage = index => {\n      if (index >= 0 && index < images.value.length) {\n        previewDialog.currentIndex = index;\n        previewDialog.currentImage = images.value[index];\n        previewDialog.visible = true;\n      }\n    };\n\n    // 上一张图像\n    const prevImage = () => {\n      if (previewDialog.currentIndex > 0) {\n        previewDialog.currentIndex--;\n        previewDialog.currentImage = images.value[previewDialog.currentIndex];\n      }\n    };\n\n    // 下一张图像\n    const nextImage = () => {\n      if (previewDialog.currentIndex < images.value.length - 1) {\n        previewDialog.currentIndex++;\n        previewDialog.currentImage = images.value[previewDialog.currentIndex];\n      }\n    };\n\n    // 下载图像\n    const downloadImage = index => {\n      if (index >= 0 && index < images.value.length) {\n        const image = images.value[index];\n        const filename = `scan_image_${index + 1}.jpg`;\n        utils.downloadBase64Image(image.base64, filename);\n        ElMessage.success(`图像 ${index + 1} 下载成功`);\n      }\n    };\n\n    // 下载当前预览图像\n    const downloadCurrentImage = () => {\n      downloadImage(previewDialog.currentIndex);\n    };\n\n    // 删除图像\n    const deleteImage = async index => {\n      try {\n        await ElMessageBox.confirm(`确定要删除图像 ${index + 1} 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        images.value.splice(index, 1);\n        // 重新设置索引\n        images.value.forEach((img, idx) => {\n          img.index = idx;\n        });\n        ElMessage.success('图像删除成功');\n      } catch {\n        // 用户取消操作\n      }\n    };\n\n    // 本地保存\n    const saveAs = () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化');\n        return;\n      }\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可保存的图像');\n        return;\n      }\n      try {\n        const filename = `d:/scan_${Date.now()}.pdf`;\n        scanonweb.value.saveAllImageToLocal(filename);\n        ElMessage.success('文件保存成功');\n      } catch (error) {\n        ElMessage.error('保存失败');\n      }\n    };\n\n    // 生命周期钩子\n    onMounted(() => {\n      initScanService();\n      // 自动加载设备列表\n      setTimeout(() => {\n        loadDevices();\n      }, 1000);\n    });\n    onUnmounted(() => {\n      // 清理资源\n      if (scanonweb.value && scanonweb.value.h5socket) {\n        scanonweb.value.h5socket.close();\n      }\n    });\n\n    // 返回模板需要的数据和方法\n    return {\n      // 响应式数据\n      isConnected,\n      devices,\n      selectedDevice,\n      images,\n      viewMode,\n      config,\n      loading,\n      uploadDialog,\n      previewDialog,\n      // 方法\n      loadDevices,\n      onDeviceChange,\n      startScan,\n      getAllImage,\n      clearAll,\n      showUploadDialog,\n      confirmUpload,\n      previewImage,\n      prevImage,\n      nextImage,\n      downloadImage,\n      downloadCurrentImage,\n      deleteImage,\n      saveAs\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "onUnmounted", "ElMessage", "ElMessageBox", "Camera", "Setting", "Picture", "Refresh", "ScanOnWeb", "utils", "name", "components", "setup", "scanonweb", "isConnected", "devices", "selected<PERSON><PERSON><PERSON>", "images", "viewMode", "config", "dpi_x", "dpi_y", "colorMode", "showDialog", "autoFeedEnable", "autoFeed", "dupxMode", "autoDeskew", "autoBorderDetection", "loading", "scan", "upload", "uploadDialog", "visible", "form", "id", "description", "format", "previewDialog", "currentIndex", "currentImage", "initScanService", "value", "onGetDevicesListEvent", "msg", "length", "setTimeout", "onDeviceChange", "success", "onScanFinishedEvent", "imageAfterCount", "getAllImage", "onGetAllImageEvent", "map", "image", "index", "src", "base64", "info", "onGetImageByIdEvent", "imageBase64", "addImage", "onImageEditedEvent", "imageIndex", "editImage", "onUploadEvent", "showUploadDialog", "warning", "error", "console", "loadDevices", "deviceIndex", "selectScanDevice", "startScan", "scaner_work_config", "clearAll", "confirm", "confirmButtonText", "cancelButtonText", "type", "push", "generateId", "Date", "toLocaleString", "confirmUpload", "trim", "uploadUrl", "onUploadAllImageAsPdfToUrlEvent", "log", "uploadResult", "JSON", "parse", "imageCount", "errorMsg", "message", "onUploadAllImageAsTiffToUrlEvent", "uploadJpgImageByIndexEvent", "uploadAllImageAsPdfToUrl", "tiffUploadUrl", "uploadAllImageAsTiffToUrl", "jpgUploadUrl", "uploadJpgImageByIndex", "previewImage", "prevImage", "nextImage", "downloadImage", "filename", "downloadBase64Image", "downloadCurrentImage", "deleteImage", "splice", "for<PERSON>ach", "img", "idx", "saveAs", "now", "saveAllImageToLocal", "h5socket", "close"], "sources": ["D:\\peihexian\\scanonwebh5_demo\\vue3\\src\\components\\ScanOnWebNew.vue"], "sourcesContent": ["<!-- eslint-disable vue/no-unused-components, no-unused-vars -->\n<template>\n  <div class=\"scan-container\">\n    <!-- 页面标题 -->\n    <el-card class=\"header-card\">\n      <h1 class=\"page-title\">\n        <el-icon><Camera /></el-icon>\n        文档扫描系统\n      </h1>\n      <p class=\"page-subtitle\">专业的文档扫描与管理解决方案</p>\n    </el-card>\n\n    <!-- 连接状态指示器 -->\n    <el-alert\n      v-if=\"!isConnected\"\n      title=\"扫描服务未连接\"\n      description=\"请确保扫描服务程序已启动并正在运行\"\n      type=\"warning\"\n      :closable=\"false\"\n      show-icon\n      class=\"connection-alert\"\n    />\n\n    <!-- 扫描配置区域 -->\n    <el-card class=\"config-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Setting /></el-icon>\n          <span>扫描配置</span>\n        </div>\n      </template>\n\n      <el-row :gutter=\"20\">\n        <!-- 设备选择 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"扫描设备\">\n            <el-select \n              v-model=\"selectedDevice\" \n              @change=\"onDeviceChange\"\n              placeholder=\"请选择扫描设备\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"(device, index) in devices\"\n                :key=\"index\"\n                :label=\"device\"\n                :value=\"index\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 色彩模式 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"色彩模式\">\n            <el-select v-model=\"config.colorMode\" style=\"width: 100%\">\n              <el-option label=\"彩色\" value=\"RGB\" />\n              <el-option label=\"灰度\" value=\"GRAY\" />\n              <el-option label=\"黑白\" value=\"BW\" />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 分辨率设置 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"分辨率 (DPI)\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"11\">\n                <el-input-number \n                  v-model=\"config.dpi_x\" \n                  :min=\"75\" \n                  :max=\"1200\" \n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n              <el-col :span=\"2\" class=\"dpi-separator\">×</el-col>\n              <el-col :span=\"11\">\n                <el-input-number \n                  v-model=\"config.dpi_y\" \n                  :min=\"75\" \n                  :max=\"1200\" \n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n            </el-row>\n          </el-form-item>\n        </el-col>\n\n        <!-- 高级选项 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"高级选项\">\n            <div class=\"advanced-options-compact\">\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.showDialog\">设备对话框</el-checkbox>\n                <el-checkbox v-model=\"config.autoFeedEnable\">自动进纸</el-checkbox>\n                <el-checkbox v-model=\"config.dupxMode\">双面扫描</el-checkbox>\n              </div>\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.autoDeskew\">自动纠偏</el-checkbox>\n                <el-checkbox v-model=\"config.autoBorderDetection\">边框检测</el-checkbox>\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 操作按钮区域 -->\n    <el-card class=\"action-card\">\n      <el-row :gutter=\"15\">\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"loadDevices\"\n            :loading=\"loading.devices\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Refresh /></el-icon>\n            刷新设备\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"success\"\n            @click=\"startScan\"\n            :loading=\"loading.scan\"\n            :disabled=\"selectedDevice === -1 || !isConnected\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Camera /></el-icon>\n            开始扫描\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"info\"\n            @click=\"getAllImage\"\n            :loading=\"loading.images\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Picture /></el-icon>\n            获取图像\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"warning\"\n            @click=\"clearAll\"\n            style=\"width: 100%\"\n          >\n            🗑️ 清空结果\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"showUploadDialog\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            📤 上传文档\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"default\"\n            @click=\"saveAs\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            💾 本地保存\n          </el-button>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 扫描结果展示区域 -->\n    <el-card class=\"result-card\" v-if=\"images.length > 0\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Picture /></el-icon>\n          <span>扫描结果 ({{ images.length }} 张图像)</span>\n          <div class=\"header-actions\">\n            <el-button-group>\n              <el-button\n                :type=\"viewMode === 'grid' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'grid'\"\n                size=\"small\"\n              >\n                🔲 网格\n              </el-button>\n              <el-button\n                :type=\"viewMode === 'list' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'list'\"\n                size=\"small\"\n              >\n                📋 列表\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n      </template>\n\n      <!-- 网格视图 -->\n      <div v-if=\"viewMode === 'grid'\" class=\"image-grid\">\n        <div \n          v-for=\"(image, index) in images\" \n          :key=\"index\" \n          class=\"image-item\"\n        >\n          <div class=\"image-wrapper\">\n            <img \n              :src=\"image.src\" \n              :alt=\"`扫描图像 ${index + 1}`\"\n              @click=\"previewImage(index)\"\n              class=\"scan-image\"\n            />\n            <div class=\"image-overlay\">\n              <el-button-group>\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"previewImage(index)\"\n                >\n                  👁️\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"downloadImage(index)\"\n                >\n                  💾\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  @click=\"deleteImage(index)\"\n                >\n                  🗑️\n                </el-button>\n              </el-button-group>\n            </div>\n          </div>\n          <div class=\"image-info\">\n            <span class=\"image-index\">图像 {{ index + 1 }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 列表视图 -->\n      <el-table v-else :data=\"images\" stripe>\n        <el-table-column label=\"预览\" width=\"120\">\n          <template #default=\"{ row, $index }\">\n            <img \n              :src=\"row.src\" \n              class=\"table-thumbnail\"\n              @click=\"previewImage($index)\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"图像编号\" prop=\"index\" width=\"100\">\n          <template #default=\"{ $index }\">\n            图像 {{ $index + 1 }}\n          </template>\n        </el-table-column>\n        <el-table-column label=\"格式\" width=\"100\">\n          <template #default>\n            <el-tag type=\"info\">JPEG</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"{ $index }\">\n            <el-button-group>\n              <el-button\n                type=\"primary\"\n                size=\"small\"\n                @click=\"previewImage($index)\"\n              >\n                👁️ 预览\n              </el-button>\n              <el-button\n                type=\"success\"\n                size=\"small\"\n                @click=\"downloadImage($index)\"\n              >\n                💾 下载\n              </el-button>\n              <el-button\n                type=\"danger\"\n                size=\"small\"\n                @click=\"deleteImage($index)\"\n              >\n                🗑️ 删除\n              </el-button>\n            </el-button-group>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 空状态 -->\n    <el-empty \n      v-else \n      description=\"暂无扫描结果\"\n      class=\"empty-state\"\n    >\n      <el-button \n        type=\"primary\" \n        @click=\"startScan\"\n        :disabled=\"selectedDevice === -1 || !isConnected\"\n      >\n        开始扫描\n      </el-button>\n    </el-empty>\n  </div>\n\n  <!-- 上传对话框 -->\n  <el-dialog\n    v-model=\"uploadDialog.visible\"\n    title=\"上传文档\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n  >\n    <el-form :model=\"uploadDialog.form\" label-width=\"100px\">\n      <el-form-item label=\"文档ID\">\n        <el-input v-model=\"uploadDialog.form.id\" placeholder=\"请输入文档ID\" />\n      </el-form-item>\n      <el-form-item label=\"文档描述\">\n        <el-input\n          v-model=\"uploadDialog.form.description\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入文档描述\"\n        />\n      </el-form-item>\n      <el-form-item label=\"上传格式\">\n        <el-radio-group v-model=\"uploadDialog.form.format\">\n          <el-radio label=\"pdf\">PDF文档</el-radio>\n          <el-radio label=\"tiff\">TIFF图像</el-radio>\n          <el-radio label=\"jpg\">JPG图像</el-radio>\n        </el-radio-group>\n      </el-form-item>\n    </el-form>\n\n    <template #footer>\n      <el-button @click=\"uploadDialog.visible = false\">取消</el-button>\n      <el-button\n        type=\"primary\"\n        @click=\"confirmUpload\"\n        :loading=\"loading.upload\"\n      >\n        确认上传\n      </el-button>\n    </template>\n  </el-dialog>\n\n  <!-- 图像预览对话框 -->\n  <el-dialog\n    v-model=\"previewDialog.visible\"\n    title=\"图像预览\"\n    width=\"80%\"\n    :close-on-click-modal=\"false\"\n  >\n    <div class=\"preview-container\" v-if=\"previewDialog.currentImage\">\n      <img\n        :src=\"previewDialog.currentImage.src\"\n        class=\"preview-image\"\n        :alt=\"`预览图像 ${previewDialog.currentIndex + 1}`\"\n      />\n    </div>\n    <template #footer>\n      <div class=\"preview-footer\">\n        <div class=\"preview-info\">\n          图像 {{ previewDialog.currentIndex + 1 }} / {{ images.length }}\n        </div>\n        <div class=\"preview-actions\">\n          <el-button\n            @click=\"prevImage\"\n            :disabled=\"previewDialog.currentIndex <= 0\"\n          >\n            ⬅️ 上一张\n          </el-button>\n          <el-button\n            @click=\"nextImage\"\n            :disabled=\"previewDialog.currentIndex >= images.length - 1\"\n          >\n            下一张 ➡️\n          </el-button>\n          <el-button\n            type=\"success\"\n            @click=\"downloadCurrentImage\"\n          >\n            💾 下载\n          </el-button>\n          <el-button @click=\"previewDialog.visible = false\">关闭</el-button>\n        </div>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Camera, Setting, Picture, Refresh } from '@element-plus/icons-vue'\nimport ScanOnWeb from '@/scanonweb.js'\nimport { utils } from '@/services/api.js'\n\nexport default {\n  name: 'ScanOnWeb',\n  components: {\n    Camera,\n    Setting,\n    Picture,\n    Refresh\n  },\n  setup() {\n    // 响应式数据\n    const scanonweb = ref(null)\n    const isConnected = ref(false)\n    const devices = ref([])\n    const selectedDevice = ref(-1)\n    const images = ref([])\n    const viewMode = ref('grid')\n\n    // 扫描配置\n    const config = reactive({\n      dpi_x: 300,\n      dpi_y: 300,\n      colorMode: 'RGB',\n      showDialog: false,\n      autoFeedEnable: true,\n      autoFeed: false,\n      dupxMode: false,\n      autoDeskew: false,\n      autoBorderDetection: false\n    })\n\n    // 加载状态\n    const loading = reactive({\n      devices: false,\n      scan: false,\n      images: false,\n      upload: false\n    })\n\n    // 上传对话框\n    const uploadDialog = reactive({\n      visible: false,\n      form: {\n        id: '',\n        description: '',\n        format: 'pdf'\n      }\n    })\n\n    // 预览对话框\n    const previewDialog = reactive({\n      visible: false,\n      currentIndex: 0,\n      currentImage: null\n    })\n\n    // 初始化扫描服务\n    const initScanService = () => {\n      try {\n        scanonweb.value = new ScanOnWeb()\n\n        // 设置事件回调\n        scanonweb.value.onGetDevicesListEvent = (msg) => {\n          devices.value = msg.devices || []\n          // 自动选择第一个设备，如果没有当前设备则选择第一个\n          if (devices.value.length > 0) {\n            selectedDevice.value = msg.currentIndex >= 0 ? msg.currentIndex : 0\n            // 如果自动选择了设备，调用设备选择事件\n            if (selectedDevice.value >= 0) {\n              setTimeout(() => {\n                onDeviceChange(selectedDevice.value)\n              }, 100)\n            }\n          } else {\n            selectedDevice.value = -1\n          }\n          loading.devices = false\n          isConnected.value = true\n          ElMessage.success(`发现 ${devices.value.length} 个扫描设备${selectedDevice.value >= 0 ? '，已自动选择第一个设备' : ''}`)\n        }\n\n        scanonweb.value.onScanFinishedEvent = (msg) => {\n          loading.scan = false\n          ElMessage.success(`扫描完成！共扫描 ${msg.imageAfterCount} 张图像`)\n          getAllImage()\n        }\n\n        scanonweb.value.onGetAllImageEvent = (msg) => {\n          loading.images = false\n          if (msg.images && msg.images.length > 0) {\n            images.value = msg.images.map((image, index) => ({\n              src: `data:image/jpg;base64,${image}`,\n              index: index,\n              base64: image\n            }))\n            ElMessage.success(`获取到 ${images.value.length} 张图像`)\n          } else {\n            ElMessage.info('暂无扫描图像')\n          }\n        }\n\n        scanonweb.value.onGetImageByIdEvent = (msg) => {\n          if (msg.imageBase64) {\n            addImage(msg.imageBase64)\n          }\n        }\n\n        scanonweb.value.onImageEditedEvent = (msg) => {\n          ElMessage.info(`图像 ${msg.imageIndex + 1} 已编辑`)\n          if (msg.imageBase64) {\n            editImage(msg.imageIndex, msg.imageBase64)\n          }\n        }\n\n        scanonweb.value.onUploadEvent = () => {\n          ElMessage.info('用户点击了上传按钮')\n          showUploadDialog()\n        }\n\n        // 检查连接状态\n        setTimeout(() => {\n          if (devices.value.length === 0) {\n            isConnected.value = false\n            ElMessage.warning('扫描服务连接失败，请检查服务是否启动')\n          }\n        }, 3000)\n\n      } catch (error) {\n        console.error('初始化扫描服务失败:', error)\n        isConnected.value = false\n        ElMessage.error('初始化扫描服务失败')\n      }\n    }\n\n    // 加载设备列表\n    const loadDevices = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      loading.devices = true\n      try {\n        scanonweb.value.loadDevices()\n      } catch (error) {\n        loading.devices = false\n        ElMessage.error('获取设备列表失败')\n      }\n    }\n\n    // 设备选择变化\n    const onDeviceChange = (deviceIndex) => {\n      if (scanonweb.value) {\n        scanonweb.value.selectScanDevice(deviceIndex)\n        ElMessage.info(`已选择设备: ${devices.value[deviceIndex]}`)\n      }\n    }\n\n    // 开始扫描\n    const startScan = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (selectedDevice.value === -1) {\n        ElMessage.warning('请先选择扫描设备')\n        return\n      }\n\n      loading.scan = true\n      try {\n        // 更新扫描配置\n        scanonweb.value.scaner_work_config = {\n          ...scanonweb.value.scaner_work_config,\n          ...config,\n          deviceIndex: selectedDevice.value\n        }\n\n        scanonweb.value.startScan()\n        ElMessage.info('开始扫描...')\n      } catch (error) {\n        loading.scan = false\n        ElMessage.error('启动扫描失败')\n      }\n    }\n\n    // 获取所有图像\n    const getAllImage = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      loading.images = true\n      try {\n        scanonweb.value.getAllImage()\n      } catch (error) {\n        loading.images = false\n        ElMessage.error('获取图像失败')\n      }\n    }\n\n    // 清空所有图像\n    const clearAll = async () => {\n      try {\n        await ElMessageBox.confirm('确定要清空所有扫描结果吗？', '确认操作', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        if (scanonweb.value) {\n          scanonweb.value.clearAll()\n          images.value = []\n          ElMessage.success('已清空所有扫描结果')\n        }\n      } catch {\n        // 用户取消操作\n      }\n    }\n\n    // 添加图像\n    const addImage = (imageBase64) => {\n      images.value.push({\n        src: `data:image/jpg;base64,${imageBase64}`,\n        index: images.value.length,\n        base64: imageBase64\n      })\n    }\n\n    // 编辑图像\n    const editImage = (index, imageBase64) => {\n      if (index >= 0 && index < images.value.length) {\n        images.value[index] = {\n          src: `data:image/jpg;base64,${imageBase64}`,\n          index: index,\n          base64: imageBase64\n        }\n      }\n    }\n\n    // 显示上传对话框\n    const showUploadDialog = () => {\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像')\n        return\n      }\n\n      uploadDialog.form.id = utils.generateId()\n      uploadDialog.form.description = `扫描文档_${new Date().toLocaleString()}`\n      uploadDialog.visible = true\n    }\n\n    // 确认上传\n    const confirmUpload = async () => {\n      if (!uploadDialog.form.id.trim()) {\n        ElMessage.warning('请输入文档ID')\n        return\n      }\n\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可上传的图像')\n        return\n      }\n\n      loading.upload = true\n      try {\n        const { id, description, format } = uploadDialog.form\n        const uploadUrl = 'http://localhost:8080/upload'\n\n        // 设置上传完成回调\n        scanonweb.value.onUploadAllImageAsPdfToUrlEvent = (msg) => {\n          console.log('PDF上传回调:', msg)\n          loading.upload = false\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string'\n                ? JSON.parse(msg.uploadResult)\n                : msg.uploadResult\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`)\n              uploadDialog.visible = false\n              console.log('上传详情:', uploadResult)\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误'\n              ElMessage.error(`PDF上传失败: ${errorMsg}`)\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg)\n            ElMessage.error(`PDF上传失败: 响应解析错误`)\n          }\n        }\n\n        scanonweb.value.onUploadAllImageAsTiffToUrlEvent = (msg) => {\n          console.log('TIFF上传回调:', msg)\n          loading.upload = false\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string'\n                ? JSON.parse(msg.uploadResult)\n                : msg.uploadResult\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`)\n              uploadDialog.visible = false\n              console.log('上传详情:', uploadResult)\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误'\n              ElMessage.error(`TIFF上传失败: ${errorMsg}`)\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg)\n            ElMessage.error(`TIFF上传失败: 响应解析错误`)\n          }\n        }\n\n        scanonweb.value.uploadJpgImageByIndexEvent = (msg) => {\n          console.log('JPG上传回调:', msg)\n          loading.upload = false\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null\n            if (msg.uploadResult) {\n              uploadResult = typeof msg.uploadResult === 'string'\n                ? JSON.parse(msg.uploadResult)\n                : msg.uploadResult\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`JPG图像上传成功！`)\n              uploadDialog.visible = false\n              console.log('上传详情:', uploadResult)\n            } else {\n              const errorMsg = uploadResult?.message || msg.message || '未知错误'\n              ElMessage.error(`JPG上传失败: ${errorMsg}`)\n            }\n          } catch (error) {\n            console.error('解析上传结果失败:', error, msg)\n            ElMessage.error(`JPG上传失败: 响应解析错误`)\n          }\n        }\n\n        // 调用控件内置的上传方法\n        if (format === 'pdf') {\n          ElMessage.info('开始上传PDF文档...')\n          scanonweb.value.uploadAllImageAsPdfToUrl(uploadUrl, id, description)\n        } else if (format === 'tiff') {\n          ElMessage.info('开始上传TIFF文档...')\n          const tiffUploadUrl = 'http://localhost:8080/upload-tiff'\n          scanonweb.value.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description)\n        } else if (format === 'jpg') {\n          ElMessage.info('开始上传JPG图像...')\n          const jpgUploadUrl = 'http://localhost:8080/upload-jpg'\n          // 上传第一张图像作为示例，实际可以循环上传所有图像\n          scanonweb.value.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0)\n        }\n\n      } catch (error) {\n        loading.upload = false\n        console.error('上传失败:', error)\n        ElMessage.error(`上传失败: ${error.message || '未知错误'}`)\n      }\n    }\n\n    // 预览图像\n    const previewImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        previewDialog.currentIndex = index\n        previewDialog.currentImage = images.value[index]\n        previewDialog.visible = true\n      }\n    }\n\n    // 上一张图像\n    const prevImage = () => {\n      if (previewDialog.currentIndex > 0) {\n        previewDialog.currentIndex--\n        previewDialog.currentImage = images.value[previewDialog.currentIndex]\n      }\n    }\n\n    // 下一张图像\n    const nextImage = () => {\n      if (previewDialog.currentIndex < images.value.length - 1) {\n        previewDialog.currentIndex++\n        previewDialog.currentImage = images.value[previewDialog.currentIndex]\n      }\n    }\n\n    // 下载图像\n    const downloadImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        const image = images.value[index]\n        const filename = `scan_image_${index + 1}.jpg`\n        utils.downloadBase64Image(image.base64, filename)\n        ElMessage.success(`图像 ${index + 1} 下载成功`)\n      }\n    }\n\n    // 下载当前预览图像\n    const downloadCurrentImage = () => {\n      downloadImage(previewDialog.currentIndex)\n    }\n\n    // 删除图像\n    const deleteImage = async (index) => {\n      try {\n        await ElMessageBox.confirm(`确定要删除图像 ${index + 1} 吗？`, '确认删除', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        images.value.splice(index, 1)\n        // 重新设置索引\n        images.value.forEach((img, idx) => {\n          img.index = idx\n        })\n\n        ElMessage.success('图像删除成功')\n      } catch {\n        // 用户取消操作\n      }\n    }\n\n    // 本地保存\n    const saveAs = () => {\n      if (!scanonweb.value) {\n        ElMessage.error('扫描服务未初始化')\n        return\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning('没有可保存的图像')\n        return\n      }\n\n      try {\n        const filename = `d:/scan_${Date.now()}.pdf`\n        scanonweb.value.saveAllImageToLocal(filename)\n        ElMessage.success('文件保存成功')\n      } catch (error) {\n        ElMessage.error('保存失败')\n      }\n    }\n\n    // 生命周期钩子\n    onMounted(() => {\n      initScanService()\n      // 自动加载设备列表\n      setTimeout(() => {\n        loadDevices()\n      }, 1000)\n    })\n\n    onUnmounted(() => {\n      // 清理资源\n      if (scanonweb.value && scanonweb.value.h5socket) {\n        scanonweb.value.h5socket.close()\n      }\n    })\n\n    // 返回模板需要的数据和方法\n    return {\n      // 响应式数据\n      isConnected,\n      devices,\n      selectedDevice,\n      images,\n      viewMode,\n      config,\n      loading,\n      uploadDialog,\n      previewDialog,\n\n      // 方法\n      loadDevices,\n      onDeviceChange,\n      startScan,\n      getAllImage,\n      clearAll,\n      showUploadDialog,\n      confirmUpload,\n      previewImage,\n      prevImage,\n      nextImage,\n      downloadImage,\n      downloadCurrentImage,\n      deleteImage,\n      saveAs\n    }\n  }\n}\n</script>\n\n<style scoped>\n.scan-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n.header-card {\n  margin-bottom: 20px;\n  text-align: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n}\n\n.page-title {\n  margin: 0;\n  font-size: 2.5em;\n  font-weight: 300;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n}\n\n.page-subtitle {\n  margin: 10px 0 0 0;\n  font-size: 1.1em;\n  opacity: 0.9;\n}\n\n.connection-alert {\n  margin-bottom: 20px;\n}\n\n.config-card,\n.action-card,\n.result-card {\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  border: none;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-actions {\n  margin-left: auto;\n}\n\n.dpi-separator {\n  text-align: center;\n  line-height: 32px;\n  font-weight: bold;\n  color: #909399;\n}\n\n.advanced-options-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.options-row {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n  padding: 10px 0;\n}\n\n.image-item {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.image-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n.image-wrapper {\n  position: relative;\n  overflow: hidden;\n}\n\n.scan-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.scan-image:hover {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.2s;\n}\n\n.image-wrapper:hover .image-overlay {\n  opacity: 1;\n}\n\n.image-info {\n  padding: 15px;\n  text-align: center;\n}\n\n.image-index {\n  font-weight: 600;\n  color: #303133;\n}\n\n.table-thumbnail {\n  width: 80px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.table-thumbnail:hover {\n  transform: scale(1.1);\n}\n\n.empty-state {\n  margin: 40px 0;\n  padding: 40px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.preview-container {\n  text-align: center;\n  padding: 20px;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.preview-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n}\n\n.preview-info {\n  font-weight: 600;\n  color: #606266;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .scan-container {\n    padding: 10px;\n  }\n\n  .page-title {\n    font-size: 2em;\n  }\n\n  .image-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 15px;\n  }\n\n  .preview-footer {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .preview-actions {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.config-card,\n.action-card,\n.result-card {\n  animation: fadeIn 0.5s ease-out;\n}\n\n/* Element Plus 组件样式覆盖 */\n.el-card__header {\n  background-color: #fafafa;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.el-button {\n  transition: all 0.2s;\n}\n\n.el-button:hover {\n  transform: translateY(-1px);\n}\n\n.el-form-item__label {\n  font-weight: 600;\n  color: #606266;\n}\n\n.el-alert {\n  border-radius: 8px;\n}\n</style>\n"], "mappings": ";AAoZA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC1D,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAM,QAAS,yBAAwB;AAC1E,OAAOC,SAAQ,MAAO,gBAAe;AACrC,SAASC,KAAI,QAAS,mBAAkB;AAExC,eAAe;EACbC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACVP,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC;EACDK,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,SAAQ,GAAIf,GAAG,CAAC,IAAI;IAC1B,MAAMgB,WAAU,GAAIhB,GAAG,CAAC,KAAK;IAC7B,MAAMiB,OAAM,GAAIjB,GAAG,CAAC,EAAE;IACtB,MAAMkB,cAAa,GAAIlB,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAMmB,MAAK,GAAInB,GAAG,CAAC,EAAE;IACrB,MAAMoB,QAAO,GAAIpB,GAAG,CAAC,MAAM;;IAE3B;IACA,MAAMqB,MAAK,GAAIpB,QAAQ,CAAC;MACtBqB,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,GAAG;MACVC,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,KAAK;MACjBC,mBAAmB,EAAE;IACvB,CAAC;;IAED;IACA,MAAMC,OAAM,GAAI9B,QAAQ,CAAC;MACvBgB,OAAO,EAAE,KAAK;MACde,IAAI,EAAE,KAAK;MACXb,MAAM,EAAE,KAAK;MACbc,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMC,YAAW,GAAIjC,QAAQ,CAAC;MAC5BkC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE;QACJC,EAAE,EAAE,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE;MACV;IACF,CAAC;;IAED;IACA,MAAMC,aAAY,GAAIvC,QAAQ,CAAC;MAC7BkC,OAAO,EAAE,KAAK;MACdM,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE;IAChB,CAAC;;IAED;IACA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAI;QACF5B,SAAS,CAAC6B,KAAI,GAAI,IAAIlC,SAAS,CAAC;;QAEhC;QACAK,SAAS,CAAC6B,KAAK,CAACC,qBAAoB,GAAKC,GAAG,IAAK;UAC/C7B,OAAO,CAAC2B,KAAI,GAAIE,GAAG,CAAC7B,OAAM,IAAK,EAAC;UAChC;UACA,IAAIA,OAAO,CAAC2B,KAAK,CAACG,MAAK,GAAI,CAAC,EAAE;YAC5B7B,cAAc,CAAC0B,KAAI,GAAIE,GAAG,CAACL,YAAW,IAAK,IAAIK,GAAG,CAACL,YAAW,GAAI;YAClE;YACA,IAAIvB,cAAc,CAAC0B,KAAI,IAAK,CAAC,EAAE;cAC7BI,UAAU,CAAC,MAAM;gBACfC,cAAc,CAAC/B,cAAc,CAAC0B,KAAK;cACrC,CAAC,EAAE,GAAG;YACR;UACF,OAAO;YACL1B,cAAc,CAAC0B,KAAI,GAAI,CAAC;UAC1B;UACAb,OAAO,CAACd,OAAM,GAAI,KAAI;UACtBD,WAAW,CAAC4B,KAAI,GAAI,IAAG;UACvBxC,SAAS,CAAC8C,OAAO,CAAE,MAAKjC,OAAO,CAAC2B,KAAK,CAACG,MAAM,SAAS7B,cAAc,CAAC0B,KAAI,IAAK,IAAI,aAAY,GAAI,EAAG,EAAC;QACvG;QAEA7B,SAAS,CAAC6B,KAAK,CAACO,mBAAkB,GAAKL,GAAG,IAAK;UAC7Cf,OAAO,CAACC,IAAG,GAAI,KAAI;UACnB5B,SAAS,CAAC8C,OAAO,CAAE,YAAWJ,GAAG,CAACM,eAAe,MAAM;UACvDC,WAAW,CAAC;QACd;QAEAtC,SAAS,CAAC6B,KAAK,CAACU,kBAAiB,GAAKR,GAAG,IAAK;UAC5Cf,OAAO,CAACZ,MAAK,GAAI,KAAI;UACrB,IAAI2B,GAAG,CAAC3B,MAAK,IAAK2B,GAAG,CAAC3B,MAAM,CAAC4B,MAAK,GAAI,CAAC,EAAE;YACvC5B,MAAM,CAACyB,KAAI,GAAIE,GAAG,CAAC3B,MAAM,CAACoC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,MAAM;cAC/CC,GAAG,EAAG,yBAAwBF,KAAM,EAAC;cACrCC,KAAK,EAAEA,KAAK;cACZE,MAAM,EAAEH;YACV,CAAC,CAAC;YACFpD,SAAS,CAAC8C,OAAO,CAAE,OAAM/B,MAAM,CAACyB,KAAK,CAACG,MAAM,MAAM;UACpD,OAAO;YACL3C,SAAS,CAACwD,IAAI,CAAC,QAAQ;UACzB;QACF;QAEA7C,SAAS,CAAC6B,KAAK,CAACiB,mBAAkB,GAAKf,GAAG,IAAK;UAC7C,IAAIA,GAAG,CAACgB,WAAW,EAAE;YACnBC,QAAQ,CAACjB,GAAG,CAACgB,WAAW;UAC1B;QACF;QAEA/C,SAAS,CAAC6B,KAAK,CAACoB,kBAAiB,GAAKlB,GAAG,IAAK;UAC5C1C,SAAS,CAACwD,IAAI,CAAE,MAAKd,GAAG,CAACmB,UAAS,GAAI,CAAC,MAAM;UAC7C,IAAInB,GAAG,CAACgB,WAAW,EAAE;YACnBI,SAAS,CAACpB,GAAG,CAACmB,UAAU,EAAEnB,GAAG,CAACgB,WAAW;UAC3C;QACF;QAEA/C,SAAS,CAAC6B,KAAK,CAACuB,aAAY,GAAI,MAAM;UACpC/D,SAAS,CAACwD,IAAI,CAAC,WAAW;UAC1BQ,gBAAgB,CAAC;QACnB;;QAEA;QACApB,UAAU,CAAC,MAAM;UACf,IAAI/B,OAAO,CAAC2B,KAAK,CAACG,MAAK,KAAM,CAAC,EAAE;YAC9B/B,WAAW,CAAC4B,KAAI,GAAI,KAAI;YACxBxC,SAAS,CAACiE,OAAO,CAAC,oBAAoB;UACxC;QACF,CAAC,EAAE,IAAI;MAET,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjCtD,WAAW,CAAC4B,KAAI,GAAI,KAAI;QACxBxC,SAAS,CAACkE,KAAK,CAAC,WAAW;MAC7B;IACF;;IAEA;IACA,MAAME,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAACzD,SAAS,CAAC6B,KAAK,EAAE;QACpBxC,SAAS,CAACkE,KAAK,CAAC,UAAU;QAC1B;MACF;MAEAvC,OAAO,CAACd,OAAM,GAAI,IAAG;MACrB,IAAI;QACFF,SAAS,CAAC6B,KAAK,CAAC4B,WAAW,CAAC;MAC9B,EAAE,OAAOF,KAAK,EAAE;QACdvC,OAAO,CAACd,OAAM,GAAI,KAAI;QACtBb,SAAS,CAACkE,KAAK,CAAC,UAAU;MAC5B;IACF;;IAEA;IACA,MAAMrB,cAAa,GAAKwB,WAAW,IAAK;MACtC,IAAI1D,SAAS,CAAC6B,KAAK,EAAE;QACnB7B,SAAS,CAAC6B,KAAK,CAAC8B,gBAAgB,CAACD,WAAW;QAC5CrE,SAAS,CAACwD,IAAI,CAAE,UAAS3C,OAAO,CAAC2B,KAAK,CAAC6B,WAAW,CAAE,EAAC;MACvD;IACF;;IAEA;IACA,MAAME,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAAC5D,SAAS,CAAC6B,KAAK,EAAE;QACpBxC,SAAS,CAACkE,KAAK,CAAC,UAAU;QAC1B;MACF;MAEA,IAAIpD,cAAc,CAAC0B,KAAI,KAAM,CAAC,CAAC,EAAE;QAC/BxC,SAAS,CAACiE,OAAO,CAAC,UAAU;QAC5B;MACF;MAEAtC,OAAO,CAACC,IAAG,GAAI,IAAG;MAClB,IAAI;QACF;QACAjB,SAAS,CAAC6B,KAAK,CAACgC,kBAAiB,GAAI;UACnC,GAAG7D,SAAS,CAAC6B,KAAK,CAACgC,kBAAkB;UACrC,GAAGvD,MAAM;UACToD,WAAW,EAAEvD,cAAc,CAAC0B;QAC9B;QAEA7B,SAAS,CAAC6B,KAAK,CAAC+B,SAAS,CAAC;QAC1BvE,SAAS,CAACwD,IAAI,CAAC,SAAS;MAC1B,EAAE,OAAOU,KAAK,EAAE;QACdvC,OAAO,CAACC,IAAG,GAAI,KAAI;QACnB5B,SAAS,CAACkE,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMjB,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAACtC,SAAS,CAAC6B,KAAK,EAAE;QACpBxC,SAAS,CAACkE,KAAK,CAAC,UAAU;QAC1B;MACF;MAEAvC,OAAO,CAACZ,MAAK,GAAI,IAAG;MACpB,IAAI;QACFJ,SAAS,CAAC6B,KAAK,CAACS,WAAW,CAAC;MAC9B,EAAE,OAAOiB,KAAK,EAAE;QACdvC,OAAO,CAACZ,MAAK,GAAI,KAAI;QACrBf,SAAS,CAACkE,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMO,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF,MAAMxE,YAAY,CAACyE,OAAO,CAAC,eAAe,EAAE,MAAM,EAAE;UAClDC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CAAC;QAED,IAAIlE,SAAS,CAAC6B,KAAK,EAAE;UACnB7B,SAAS,CAAC6B,KAAK,CAACiC,QAAQ,CAAC;UACzB1D,MAAM,CAACyB,KAAI,GAAI,EAAC;UAChBxC,SAAS,CAAC8C,OAAO,CAAC,WAAW;QAC/B;MACF,EAAE,MAAM;QACN;MAAA;IAEJ;;IAEA;IACA,MAAMa,QAAO,GAAKD,WAAW,IAAK;MAChC3C,MAAM,CAACyB,KAAK,CAACsC,IAAI,CAAC;QAChBxB,GAAG,EAAG,yBAAwBI,WAAY,EAAC;QAC3CL,KAAK,EAAEtC,MAAM,CAACyB,KAAK,CAACG,MAAM;QAC1BY,MAAM,EAAEG;MACV,CAAC;IACH;;IAEA;IACA,MAAMI,SAAQ,GAAIA,CAACT,KAAK,EAAEK,WAAW,KAAK;MACxC,IAAIL,KAAI,IAAK,KAAKA,KAAI,GAAItC,MAAM,CAACyB,KAAK,CAACG,MAAM,EAAE;QAC7C5B,MAAM,CAACyB,KAAK,CAACa,KAAK,IAAI;UACpBC,GAAG,EAAG,yBAAwBI,WAAY,EAAC;UAC3CL,KAAK,EAAEA,KAAK;UACZE,MAAM,EAAEG;QACV;MACF;IACF;;IAEA;IACA,MAAMM,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIjD,MAAM,CAACyB,KAAK,CAACG,MAAK,KAAM,CAAC,EAAE;QAC7B3C,SAAS,CAACiE,OAAO,CAAC,UAAU;QAC5B;MACF;MAEAnC,YAAY,CAACE,IAAI,CAACC,EAAC,GAAI1B,KAAK,CAACwE,UAAU,CAAC;MACxCjD,YAAY,CAACE,IAAI,CAACE,WAAU,GAAK,QAAO,IAAI8C,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAE;MACpEnD,YAAY,CAACC,OAAM,GAAI,IAAG;IAC5B;;IAEA;IACA,MAAMmD,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACpD,YAAY,CAACE,IAAI,CAACC,EAAE,CAACkD,IAAI,CAAC,CAAC,EAAE;QAChCnF,SAAS,CAACiE,OAAO,CAAC,SAAS;QAC3B;MACF;MAEA,IAAI,CAACtD,SAAS,CAAC6B,KAAK,EAAE;QACpBxC,SAAS,CAACkE,KAAK,CAAC,UAAU;QAC1B;MACF;MAEA,IAAInD,MAAM,CAACyB,KAAK,CAACG,MAAK,KAAM,CAAC,EAAE;QAC7B3C,SAAS,CAACiE,OAAO,CAAC,UAAU;QAC5B;MACF;MAEAtC,OAAO,CAACE,MAAK,GAAI,IAAG;MACpB,IAAI;QACF,MAAM;UAAEI,EAAE;UAAEC,WAAW;UAAEC;QAAO,IAAIL,YAAY,CAACE,IAAG;QACpD,MAAMoD,SAAQ,GAAI,8BAA6B;;QAE/C;QACAzE,SAAS,CAAC6B,KAAK,CAAC6C,+BAA8B,GAAK3C,GAAG,IAAK;UACzDyB,OAAO,CAACmB,GAAG,CAAC,UAAU,EAAE5C,GAAG;UAC3Bf,OAAO,CAACE,MAAK,GAAI,KAAI;UAErB,IAAI;YACF;YACA,IAAI0D,YAAW,GAAI,IAAG;YACtB,IAAI7C,GAAG,CAAC6C,YAAY,EAAE;cACpBA,YAAW,GAAI,OAAO7C,GAAG,CAAC6C,YAAW,KAAM,QAAO,GAC9CC,IAAI,CAACC,KAAK,CAAC/C,GAAG,CAAC6C,YAAY,IAC3B7C,GAAG,CAAC6C,YAAW;YACrB;YAEA,IAAIA,YAAW,IAAKA,YAAY,CAACzC,OAAO,EAAE;cACxC9C,SAAS,CAAC8C,OAAO,CAAE,eAAcJ,GAAG,CAACgD,UAAS,IAAK,CAAC,MAAM;cAC1D5D,YAAY,CAACC,OAAM,GAAI,KAAI;cAC3BoC,OAAO,CAACmB,GAAG,CAAC,OAAO,EAAEC,YAAY;YACnC,OAAO;cACL,MAAMI,QAAO,GAAIJ,YAAY,EAAEK,OAAM,IAAKlD,GAAG,CAACkD,OAAM,IAAK,MAAK;cAC9D5F,SAAS,CAACkE,KAAK,CAAE,YAAWyB,QAAS,EAAC;YACxC;UACF,EAAE,OAAOzB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,EAAExB,GAAG;YACrC1C,SAAS,CAACkE,KAAK,CAAE,iBAAgB;UACnC;QACF;QAEAvD,SAAS,CAAC6B,KAAK,CAACqD,gCAA+B,GAAKnD,GAAG,IAAK;UAC1DyB,OAAO,CAACmB,GAAG,CAAC,WAAW,EAAE5C,GAAG;UAC5Bf,OAAO,CAACE,MAAK,GAAI,KAAI;UAErB,IAAI;YACF;YACA,IAAI0D,YAAW,GAAI,IAAG;YACtB,IAAI7C,GAAG,CAAC6C,YAAY,EAAE;cACpBA,YAAW,GAAI,OAAO7C,GAAG,CAAC6C,YAAW,KAAM,QAAO,GAC9CC,IAAI,CAACC,KAAK,CAAC/C,GAAG,CAAC6C,YAAY,IAC3B7C,GAAG,CAAC6C,YAAW;YACrB;YAEA,IAAIA,YAAW,IAAKA,YAAY,CAACzC,OAAO,EAAE;cACxC9C,SAAS,CAAC8C,OAAO,CAAE,gBAAeJ,GAAG,CAACgD,UAAS,IAAK,CAAC,MAAM;cAC3D5D,YAAY,CAACC,OAAM,GAAI,KAAI;cAC3BoC,OAAO,CAACmB,GAAG,CAAC,OAAO,EAAEC,YAAY;YACnC,OAAO;cACL,MAAMI,QAAO,GAAIJ,YAAY,EAAEK,OAAM,IAAKlD,GAAG,CAACkD,OAAM,IAAK,MAAK;cAC9D5F,SAAS,CAACkE,KAAK,CAAE,aAAYyB,QAAS,EAAC;YACzC;UACF,EAAE,OAAOzB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,EAAExB,GAAG;YACrC1C,SAAS,CAACkE,KAAK,CAAE,kBAAiB;UACpC;QACF;QAEAvD,SAAS,CAAC6B,KAAK,CAACsD,0BAAyB,GAAKpD,GAAG,IAAK;UACpDyB,OAAO,CAACmB,GAAG,CAAC,UAAU,EAAE5C,GAAG;UAC3Bf,OAAO,CAACE,MAAK,GAAI,KAAI;UAErB,IAAI;YACF;YACA,IAAI0D,YAAW,GAAI,IAAG;YACtB,IAAI7C,GAAG,CAAC6C,YAAY,EAAE;cACpBA,YAAW,GAAI,OAAO7C,GAAG,CAAC6C,YAAW,KAAM,QAAO,GAC9CC,IAAI,CAACC,KAAK,CAAC/C,GAAG,CAAC6C,YAAY,IAC3B7C,GAAG,CAAC6C,YAAW;YACrB;YAEA,IAAIA,YAAW,IAAKA,YAAY,CAACzC,OAAO,EAAE;cACxC9C,SAAS,CAAC8C,OAAO,CAAE,YAAW;cAC9BhB,YAAY,CAACC,OAAM,GAAI,KAAI;cAC3BoC,OAAO,CAACmB,GAAG,CAAC,OAAO,EAAEC,YAAY;YACnC,OAAO;cACL,MAAMI,QAAO,GAAIJ,YAAY,EAAEK,OAAM,IAAKlD,GAAG,CAACkD,OAAM,IAAK,MAAK;cAC9D5F,SAAS,CAACkE,KAAK,CAAE,YAAWyB,QAAS,EAAC;YACxC;UACF,EAAE,OAAOzB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,EAAExB,GAAG;YACrC1C,SAAS,CAACkE,KAAK,CAAE,iBAAgB;UACnC;QACF;;QAEA;QACA,IAAI/B,MAAK,KAAM,KAAK,EAAE;UACpBnC,SAAS,CAACwD,IAAI,CAAC,cAAc;UAC7B7C,SAAS,CAAC6B,KAAK,CAACuD,wBAAwB,CAACX,SAAS,EAAEnD,EAAE,EAAEC,WAAW;QACrE,OAAO,IAAIC,MAAK,KAAM,MAAM,EAAE;UAC5BnC,SAAS,CAACwD,IAAI,CAAC,eAAe;UAC9B,MAAMwC,aAAY,GAAI,mCAAkC;UACxDrF,SAAS,CAAC6B,KAAK,CAACyD,yBAAyB,CAACD,aAAa,EAAE/D,EAAE,EAAEC,WAAW;QAC1E,OAAO,IAAIC,MAAK,KAAM,KAAK,EAAE;UAC3BnC,SAAS,CAACwD,IAAI,CAAC,cAAc;UAC7B,MAAM0C,YAAW,GAAI,kCAAiC;UACtD;UACAvF,SAAS,CAAC6B,KAAK,CAAC2D,qBAAqB,CAACD,YAAY,EAAEjE,EAAE,EAAEC,WAAW,EAAE,CAAC;QACxE;MAEF,EAAE,OAAOgC,KAAK,EAAE;QACdvC,OAAO,CAACE,MAAK,GAAI,KAAI;QACrBsC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5BlE,SAAS,CAACkE,KAAK,CAAE,SAAQA,KAAK,CAAC0B,OAAM,IAAK,MAAO,EAAC;MACpD;IACF;;IAEA;IACA,MAAMQ,YAAW,GAAK/C,KAAK,IAAK;MAC9B,IAAIA,KAAI,IAAK,KAAKA,KAAI,GAAItC,MAAM,CAACyB,KAAK,CAACG,MAAM,EAAE;QAC7CP,aAAa,CAACC,YAAW,GAAIgB,KAAI;QACjCjB,aAAa,CAACE,YAAW,GAAIvB,MAAM,CAACyB,KAAK,CAACa,KAAK;QAC/CjB,aAAa,CAACL,OAAM,GAAI,IAAG;MAC7B;IACF;;IAEA;IACA,MAAMsE,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIjE,aAAa,CAACC,YAAW,GAAI,CAAC,EAAE;QAClCD,aAAa,CAACC,YAAY,EAAC;QAC3BD,aAAa,CAACE,YAAW,GAAIvB,MAAM,CAACyB,KAAK,CAACJ,aAAa,CAACC,YAAY;MACtE;IACF;;IAEA;IACA,MAAMiE,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIlE,aAAa,CAACC,YAAW,GAAItB,MAAM,CAACyB,KAAK,CAACG,MAAK,GAAI,CAAC,EAAE;QACxDP,aAAa,CAACC,YAAY,EAAC;QAC3BD,aAAa,CAACE,YAAW,GAAIvB,MAAM,CAACyB,KAAK,CAACJ,aAAa,CAACC,YAAY;MACtE;IACF;;IAEA;IACA,MAAMkE,aAAY,GAAKlD,KAAK,IAAK;MAC/B,IAAIA,KAAI,IAAK,KAAKA,KAAI,GAAItC,MAAM,CAACyB,KAAK,CAACG,MAAM,EAAE;QAC7C,MAAMS,KAAI,GAAIrC,MAAM,CAACyB,KAAK,CAACa,KAAK;QAChC,MAAMmD,QAAO,GAAK,cAAanD,KAAI,GAAI,CAAE,MAAI;QAC7C9C,KAAK,CAACkG,mBAAmB,CAACrD,KAAK,CAACG,MAAM,EAAEiD,QAAQ;QAChDxG,SAAS,CAAC8C,OAAO,CAAE,MAAKO,KAAI,GAAI,CAAC,OAAO;MAC1C;IACF;;IAEA;IACA,MAAMqD,oBAAmB,GAAIA,CAAA,KAAM;MACjCH,aAAa,CAACnE,aAAa,CAACC,YAAY;IAC1C;;IAEA;IACA,MAAMsE,WAAU,GAAI,MAAOtD,KAAK,IAAK;MACnC,IAAI;QACF,MAAMpD,YAAY,CAACyE,OAAO,CAAE,WAAUrB,KAAI,GAAI,CAAC,KAAK,EAAE,MAAM,EAAE;UAC5DsB,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CAAC;QAED9D,MAAM,CAACyB,KAAK,CAACoE,MAAM,CAACvD,KAAK,EAAE,CAAC;QAC5B;QACAtC,MAAM,CAACyB,KAAK,CAACqE,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;UACjCD,GAAG,CAACzD,KAAI,GAAI0D,GAAE;QAChB,CAAC;QAED/G,SAAS,CAAC8C,OAAO,CAAC,QAAQ;MAC5B,EAAE,MAAM;QACN;MAAA;IAEJ;;IAEA;IACA,MAAMkE,MAAK,GAAIA,CAAA,KAAM;MACnB,IAAI,CAACrG,SAAS,CAAC6B,KAAK,EAAE;QACpBxC,SAAS,CAACkE,KAAK,CAAC,UAAU;QAC1B;MACF;MAEA,IAAInD,MAAM,CAACyB,KAAK,CAACG,MAAK,KAAM,CAAC,EAAE;QAC7B3C,SAAS,CAACiE,OAAO,CAAC,UAAU;QAC5B;MACF;MAEA,IAAI;QACF,MAAMuC,QAAO,GAAK,WAAUxB,IAAI,CAACiC,GAAG,CAAC,CAAE,MAAI;QAC3CtG,SAAS,CAAC6B,KAAK,CAAC0E,mBAAmB,CAACV,QAAQ;QAC5CxG,SAAS,CAAC8C,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOoB,KAAK,EAAE;QACdlE,SAAS,CAACkE,KAAK,CAAC,MAAM;MACxB;IACF;;IAEA;IACApE,SAAS,CAAC,MAAM;MACdyC,eAAe,CAAC;MAChB;MACAK,UAAU,CAAC,MAAM;QACfwB,WAAW,CAAC;MACd,CAAC,EAAE,IAAI;IACT,CAAC;IAEDrE,WAAW,CAAC,MAAM;MAChB;MACA,IAAIY,SAAS,CAAC6B,KAAI,IAAK7B,SAAS,CAAC6B,KAAK,CAAC2E,QAAQ,EAAE;QAC/CxG,SAAS,CAAC6B,KAAK,CAAC2E,QAAQ,CAACC,KAAK,CAAC;MACjC;IACF,CAAC;;IAED;IACA,OAAO;MACL;MACAxG,WAAW;MACXC,OAAO;MACPC,cAAc;MACdC,MAAM;MACNC,QAAQ;MACRC,MAAM;MACNU,OAAO;MACPG,YAAY;MACZM,aAAa;MAEb;MACAgC,WAAW;MACXvB,cAAc;MACd0B,SAAS;MACTtB,WAAW;MACXwB,QAAQ;MACRT,gBAAgB;MAChBkB,aAAa;MACbkB,YAAY;MACZC,SAAS;MACTC,SAAS;MACTC,aAAa;MACbG,oBAAoB;MACpBC,WAAW;MACXK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import basePropertyOf from './_basePropertyOf.js';\n\n/** Used to map HTML entities to characters. */\nvar htmlUnescapes = {\n  '&amp;': '&',\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&#39;': \"'\"\n};\n\n/**\n * Used by `_.unescape` to convert HTML entities to characters.\n *\n * @private\n * @param {string} chr The matched character to unescape.\n * @returns {string} Returns the unescaped character.\n */\nvar unescapeHtmlChar = basePropertyOf(htmlUnescapes);\nexport default unescapeHtmlChar;", "map": {"version": 3, "names": ["basePropertyOf", "htmlUnescapes", "unescapeHtmlChar"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_unescapeHtmlChar.js"], "sourcesContent": ["import basePropertyOf from './_basePropertyOf.js';\n\n/** Used to map HTML entities to characters. */\nvar htmlUnescapes = {\n  '&amp;': '&',\n  '&lt;': '<',\n  '&gt;': '>',\n  '&quot;': '\"',\n  '&#39;': \"'\"\n};\n\n/**\n * Used by `_.unescape` to convert HTML entities to characters.\n *\n * @private\n * @param {string} chr The matched character to unescape.\n * @returns {string} Returns the unescaped character.\n */\nvar unescapeHtmlChar = basePropertyOf(htmlUnescapes);\n\nexport default unescapeHtmlChar;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,aAAa,GAAG;EAClB,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,MAAM,EAAE,GAAG;EACX,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAGF,cAAc,CAACC,aAAa,CAAC;AAEpD,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
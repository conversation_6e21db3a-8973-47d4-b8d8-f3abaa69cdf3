package com.scanonweb.controller;

import com.scanonweb.dto.UploadRequest;
import com.scanonweb.dto.UploadResponse;
import com.scanonweb.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class UploadController {

    private final FileService fileService;

    /**
     * PDF格式上传接口
     * 支持multipart/form-data和application/json两种格式
     */
    @PostMapping(value = "", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<UploadResponse> uploadPdf(
            @RequestParam(value = "image", required = false) MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description,
            @RequestBody(required = false) @Valid UploadRequest request) {
        
        log.info("收到PDF上传请求");
        
        try {
            // 处理multipart/form-data上传（来自扫描控件）
            if (file != null && !file.isEmpty()) {
                return handleMultipartUpload(file, id, description, "pdf");
            }
            
            // 处理JSON格式上传（来自前端axios）
            if (request != null && StringUtils.hasText(request.getImageData())) {
                return handleJsonUpload(request, "pdf");
            }
            
            return ResponseEntity.badRequest()
                    .body(UploadResponse.error("缺少上传文件或图像数据"));
                    
        } catch (Exception e) {
            log.error("PDF上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("PDF上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * TIFF格式上传接口
     */
    @PostMapping(value = "/tiff", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<UploadResponse> uploadTiff(
            @RequestParam(value = "image", required = false) MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description,
            @RequestBody(required = false) @Valid UploadRequest request) {
        
        log.info("收到TIFF上传请求");
        
        try {
            // 处理multipart/form-data上传
            if (file != null && !file.isEmpty()) {
                return handleMultipartUpload(file, id, description, "tiff");
            }
            
            // 处理JSON格式上传
            if (request != null && StringUtils.hasText(request.getImageData())) {
                return handleJsonUpload(request, "tiff");
            }
            
            return ResponseEntity.badRequest()
                    .body(UploadResponse.error("缺少上传文件或图像数据"));
                    
        } catch (Exception e) {
            log.error("TIFF上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("TIFF上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * JPG格式上传接口
     */
    @PostMapping(value = "/jpg", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<UploadResponse> uploadJpg(
            @RequestParam(value = "image", required = false) MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description,
            @RequestParam(value = "index", required = false) Integer index,
            @RequestBody(required = false) @Valid UploadRequest request) {
        
        log.info("收到JPG上传请求");
        
        try {
            // 处理multipart/form-data上传
            if (file != null && !file.isEmpty()) {
                String fileId = id;
                if (index != null) {
                    fileId = (id != null ? id : "scan") + "_" + index;
                }
                return handleMultipartUpload(file, fileId, description, "jpg");
            }
            
            // 处理JSON格式上传
            if (request != null && StringUtils.hasText(request.getImageData())) {
                String fileId = request.getId();
                if (request.getIndex() != null) {
                    fileId = fileId + "_" + request.getIndex();
                }
                request.setId(fileId);
                return handleJsonUpload(request, "jpg");
            }
            
            return ResponseEntity.badRequest()
                    .body(UploadResponse.error("缺少上传文件或图像数据"));
                    
        } catch (Exception e) {
            log.error("JPG上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("JPG上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * 处理multipart文件上传
     */
    private ResponseEntity<UploadResponse> handleMultipartUpload(
            MultipartFile file, String id, String description, String format) throws Exception {
        
        if (id == null || id.trim().isEmpty()) {
            id = "scan_" + System.currentTimeMillis();
        }
        if (description == null) {
            description = "扫描文档";
        }

        String filename = fileService.saveMultipartFile(file, id, format);
        
        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("originalName", file.getOriginalFilename());
        data.put("size", file.getSize());
        data.put("id", id);
        data.put("format", format);
        
        String message = format.toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;
        
        return ResponseEntity.ok(UploadResponse.success(message, id, path, data));
    }

    /**
     * 处理JSON格式上传
     */
    private ResponseEntity<UploadResponse> handleJsonUpload(
            UploadRequest request, String format) throws Exception {
        
        String filename = fileService.saveBase64File(request.getImageData(), request.getId(), format);
        
        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("id", request.getId());
        data.put("format", format);
        
        String message = format.toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;
        
        return ResponseEntity.ok(UploadResponse.success(message, request.getId(), path, data));
    }
}

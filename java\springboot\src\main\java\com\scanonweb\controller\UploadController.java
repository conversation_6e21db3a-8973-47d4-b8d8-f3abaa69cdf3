package com.scanonweb.controller;

import com.scanonweb.dto.UploadRequest;
import com.scanonweb.dto.UploadResponse;
import com.scanonweb.service.FileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class UploadController {

    private final FileService fileService;

    /**
     * PDF格式上传接口 - multipart/form-data格式（扫描控件使用）
     */
    @PostMapping(value = "", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadResponse> uploadPdfMultipart(
            @RequestParam(value = "image") MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description) {

        log.info("收到PDF multipart上传请求");

        try {
            return handleMultipartUpload(file, id, description, "pdf");
        } catch (Exception e) {
            log.error("PDF上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("PDF上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * PDF格式上传接口 - JSON格式（前端axios使用）
     */
    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UploadResponse> uploadPdfJson(
            @RequestBody @Valid UploadRequest request) {

        log.info("收到PDF JSON上传请求");

        try {
            if (!StringUtils.hasText(request.getImageData())) {
                return ResponseEntity.badRequest()
                        .body(UploadResponse.error("缺少图像数据"));
            }

            return handleJsonUpload(request, "pdf");
        } catch (Exception e) {
            log.error("PDF上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("PDF上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * TIFF格式上传接口
     */
    @PostMapping(value = "/tiff", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadResponse> uploadTiff(
            @RequestParam(value = "image") MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description) {

        log.info("收到TIFF上传请求");

        try {
            return handleMultipartUpload(file, id, description, "tiff");
        } catch (Exception e) {
            log.error("TIFF上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("TIFF上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * TIFF格式上传接口 - 兼容Go版本路径
     */
    @PostMapping(value = "-tiff", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadResponse> uploadTiffCompat(
            @RequestParam(value = "image") MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description) {

        return uploadTiff(file, id, description);
    }

    /**
     * JPG格式上传接口
     */
    @PostMapping(value = "/jpg", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadResponse> uploadJpg(
            @RequestParam(value = "image") MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description,
            @RequestParam(value = "index", required = false) Integer index) {

        log.info("收到JPG上传请求");

        try {
            String fileId = id;
            if (index != null) {
                fileId = (id != null ? id : "scan") + "_" + index;
            }
            return handleMultipartUpload(file, fileId, description, "jpg");
        } catch (Exception e) {
            log.error("JPG上传失败", e);
            return ResponseEntity.internalServerError()
                    .body(UploadResponse.error("JPG上传失败: " + e.getMessage(), 500));
        }
    }

    /**
     * JPG格式上传接口 - 兼容Go版本路径
     */
    @PostMapping(value = "-jpg", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<UploadResponse> uploadJpgCompat(
            @RequestParam(value = "image") MultipartFile file,
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "desc", required = false) String description,
            @RequestParam(value = "index", required = false) Integer index) {

        return uploadJpg(file, id, description, index);
    }

    /**
     * 处理multipart文件上传
     */
    private ResponseEntity<UploadResponse> handleMultipartUpload(
            MultipartFile file, String id, String description, String format) throws Exception {

        if (id == null || id.trim().isEmpty()) {
            id = "scan_" + System.currentTimeMillis();
        }
        if (description == null) {
            description = "扫描文档";
        }

        String filename = fileService.saveMultipartFile(file, id, format);

        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("originalName", file.getOriginalFilename());
        data.put("size", file.getSize());
        data.put("id", id);
        data.put("format", format);

        String message = format.toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;

        return ResponseEntity.ok(UploadResponse.success(message, id, path, data));
    }

    /**
     * 处理JSON格式上传
     */
    private ResponseEntity<UploadResponse> handleJsonUpload(
            UploadRequest request, String format) throws Exception {

        String filename = fileService.saveBase64File(request.getImageData(), request.getId(), format);

        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("id", request.getId());
        data.put("format", format);

        String message = format.toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;

        return ResponseEntity.ok(UploadResponse.success(message, request.getId(), path, data));
    }
}

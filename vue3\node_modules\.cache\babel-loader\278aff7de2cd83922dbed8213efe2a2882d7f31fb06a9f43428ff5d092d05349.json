{"ast": null, "code": "import baseUnset from './_baseUnset.js';\nimport isIndex from './_isIndex.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAt` without support for individual\n * indexes or capturing the removed elements.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {number[]} indexes The indexes of elements to remove.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAt(array, indexes) {\n  var length = array ? indexes.length : 0,\n    lastIndex = length - 1;\n  while (length--) {\n    var index = indexes[length];\n    if (length == lastIndex || index !== previous) {\n      var previous = index;\n      if (isIndex(index)) {\n        splice.call(array, index, 1);\n      } else {\n        baseUnset(array, index);\n      }\n    }\n  }\n  return array;\n}\nexport default basePullAt;", "map": {"version": 3, "names": ["baseUnset", "isIndex", "arrayProto", "Array", "prototype", "splice", "basePullAt", "array", "indexes", "length", "lastIndex", "index", "previous", "call"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_basePullAt.js"], "sourcesContent": ["import baseUnset from './_baseUnset.js';\nimport isIndex from './_isIndex.js';\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAt` without support for individual\n * indexes or capturing the removed elements.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {number[]} indexes The indexes of elements to remove.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAt(array, indexes) {\n  var length = array ? indexes.length : 0,\n      lastIndex = length - 1;\n\n  while (length--) {\n    var index = indexes[length];\n    if (length == lastIndex || index !== previous) {\n      var previous = index;\n      if (isIndex(index)) {\n        splice.call(array, index, 1);\n      } else {\n        baseUnset(array, index);\n      }\n    }\n  }\n  return array;\n}\n\nexport default basePullAt;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,UAAU,GAAGC,KAAK,CAACC,SAAS;;AAEhC;AACA,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,IAAIC,MAAM,GAAGF,KAAK,GAAGC,OAAO,CAACC,MAAM,GAAG,CAAC;IACnCC,SAAS,GAAGD,MAAM,GAAG,CAAC;EAE1B,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIE,KAAK,GAAGH,OAAO,CAACC,MAAM,CAAC;IAC3B,IAAIA,MAAM,IAAIC,SAAS,IAAIC,KAAK,KAAKC,QAAQ,EAAE;MAC7C,IAAIA,QAAQ,GAAGD,KAAK;MACpB,IAAIV,OAAO,CAACU,KAAK,CAAC,EAAE;QAClBN,MAAM,CAACQ,IAAI,CAACN,KAAK,EAAEI,KAAK,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLX,SAAS,CAACO,KAAK,EAAEI,KAAK,CAAC;MACzB;IACF;EACF;EACA,OAAOJ,KAAK;AACd;AAEA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
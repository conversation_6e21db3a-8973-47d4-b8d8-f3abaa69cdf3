{"ast": null, "code": "/**\n * The base implementation of methods like `_.findKey` and `_.findLast<PERSON>ey`,\n * without support for iteratee shorthands, which iterates over `collection`\n * using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the found element or its key, else `undefined`.\n */\nfunction baseFindKey(collection, predicate, eachFunc) {\n  var result;\n  eachFunc(collection, function (value, key, collection) {\n    if (predicate(value, key, collection)) {\n      result = key;\n      return false;\n    }\n  });\n  return result;\n}\nexport default baseFindKey;", "map": {"version": 3, "names": ["baseFindKey", "collection", "predicate", "eachFunc", "result", "value", "key"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/_baseFindKey.js"], "sourcesContent": ["/**\n * The base implementation of methods like `_.findKey` and `_.findLast<PERSON>ey`,\n * without support for iteratee shorthands, which iterates over `collection`\n * using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the found element or its key, else `undefined`.\n */\nfunction baseFindKey(collection, predicate, eachFunc) {\n  var result;\n  eachFunc(collection, function(value, key, collection) {\n    if (predicate(value, key, collection)) {\n      result = key;\n      return false;\n    }\n  });\n  return result;\n}\n\nexport default baseFindKey;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EACpD,IAAIC,MAAM;EACVD,QAAQ,CAACF,UAAU,EAAE,UAASI,KAAK,EAAEC,GAAG,EAAEL,UAAU,EAAE;IACpD,IAAIC,SAAS,CAACG,KAAK,EAAEC,GAAG,EAAEL,UAAU,CAAC,EAAE;MACrCG,MAAM,GAAGE,GAAG;MACZ,OAAO,KAAK;IACd;EACF,CAAC,CAAC;EACF,OAAOF,MAAM;AACf;AAEA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
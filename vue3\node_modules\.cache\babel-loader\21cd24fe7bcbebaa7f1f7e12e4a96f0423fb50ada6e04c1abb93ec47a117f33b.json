{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport unzip from './unzip.js';\n\n/**\n * Creates an array of grouped elements, the first of which contains the\n * first elements of the given arrays, the second of which contains the\n * second elements of the given arrays, and so on.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n */\nvar zip = baseRest(unzip);\nexport default zip;", "map": {"version": 3, "names": ["baseRest", "unzip", "zip"], "sources": ["D:/peihexian/scanonwebh5_demo/vue3/node_modules/lodash-es/zip.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport unzip from './unzip.js';\n\n/**\n * Creates an array of grouped elements, the first of which contains the\n * first elements of the given arrays, the second of which contains the\n * second elements of the given arrays, and so on.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {...Array} [arrays] The arrays to process.\n * @returns {Array} Returns the new array of grouped elements.\n * @example\n *\n * _.zip(['a', 'b'], [1, 2], [true, false]);\n * // => [['a', 1, true], ['b', 2, false]]\n */\nvar zip = baseRest(unzip);\n\nexport default zip;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,YAAY;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAGF,QAAQ,CAACC,KAAK,CAAC;AAEzB,eAAeC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}